{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
  {{ block.super }}
  <style>
    .chart-container {
      position: relative;
      height: 300px;
      margin-bottom: 20px;
    }
    .dashboard-charts {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }
    .chart-card {
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      padding: 15px;
    }
    .chart-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 15px;
      color: #333;
    }
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 15px;
      margin-bottom: 30px;
    }
    .stat-card {
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      padding: 15px;
      text-align: center;
    }
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
    }
    .stat-label {
      font-size: 14px;
      color: #666;
    }
    .stat-icon {
      font-size: 24px;
      margin-bottom: 10px;
    }
    .stat-primary { color: #007bff; }
    .stat-success { color: #28a745; }
    .stat-warning { color: #ffc107; }
    .stat-danger { color: #dc3545; }
    .stat-info { color: #17a2b8; }
  </style>
{% endblock %}

{% block content %}
<div id="content-main">
  <h1>{% trans 'Dashboard' %}</h1>

  <!-- Stats Cards -->
  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-icon stat-primary">
        <i class="fas fa-users"></i>
      </div>
      <div class="stat-value" id="total-users">-</div>
      <div class="stat-label">Total Users</div>
    </div>

    <div class="stat-card">
      <div class="stat-icon stat-success">
        <i class="fas fa-folder"></i>
      </div>
      <div class="stat-value" id="total-projects">-</div>
      <div class="stat-label">Total Projects</div>
    </div>

    <div class="stat-card">
      <div class="stat-icon stat-warning">
        <i class="fas fa-file"></i>
      </div>
      <div class="stat-value" id="total-files">-</div>
      <div class="stat-label">Total Files</div>
    </div>

    <div class="stat-card">
      <div class="stat-icon stat-info">
        <i class="fas fa-chart-bar"></i>
      </div>
      <div class="stat-value" id="total-analyses">-</div>
      <div class="stat-label">Total Analyses</div>
    </div>
  </div>

  <!-- Charts -->
  <div class="dashboard-charts">
    <!-- Project Status Chart -->
    <div class="chart-card">
      <div class="chart-title">Project Status Distribution</div>
      <div class="chart-container">
        <canvas id="projectStatusChart"></canvas>
      </div>
    </div>

    <!-- User Type Chart -->
    <div class="chart-card">
      <div class="chart-title">User Type Distribution</div>
      <div class="chart-container">
        <canvas id="userTypeChart"></canvas>
      </div>
    </div>

    <!-- File Upload Trend Chart -->
    <div class="chart-card">
      <div class="chart-title">File Upload Trend (Last 30 Days)</div>
      <div class="chart-container">
        <canvas id="fileUploadChart"></canvas>
      </div>
    </div>

    <!-- Testimonial Ratings Chart -->
    <div class="chart-card">
      <div class="chart-title">Testimonial Ratings</div>
      <div class="chart-container">
        <canvas id="testimonialRatingChart"></canvas>
      </div>
    </div>
  </div>

  <div class="module" id="recent-actions-module">
    <h2>{% trans 'Recent Actions' %}</h2>
    <h3>{% trans 'My Actions' %}</h3>
    {% load log %}
    {% get_admin_log 10 as admin_log for_user user %}
    {% if not admin_log %}
      <p>{% trans 'None available' %}</p>
    {% else %}
      <ul class="actionlist">
        {% for entry in admin_log %}
          <li class="{% if entry.is_addition %}addlink{% endif %}{% if entry.is_change %}changelink{% endif %}{% if entry.is_deletion %}deletelink{% endif %}">
            {% if entry.is_deletion or not entry.get_admin_url %}
              {{ entry.object_repr }}
            {% else %}
              <a href="{{ entry.get_admin_url }}">{{ entry.object_repr }}</a>
            {% endif %}
            <br>
            {% if entry.content_type %}
              <span class="mini quiet">{% filter capfirst %}{{ entry.content_type.name }}{% endfilter %}</span>
            {% else %}
              <span class="mini quiet">{% trans 'Unknown content' %}</span>
            {% endif %}
          </li>
        {% endfor %}
      </ul>
    {% endif %}
  </div>
</div>
{% endblock %}

{% block extrahead %}
  {{ block.super }}
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
{% endblock %}

{% block footer %}
  {{ block.super }}
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Fetch dashboard data
      fetch('/completoplus-admin/dashboard/data/')
        .then(response => response.json())
        .then(data => {
          // Update stats
          document.getElementById('total-users').textContent = data.total_users;
          document.getElementById('total-projects').textContent = data.total_projects;
          document.getElementById('total-files').textContent = data.total_files;
          document.getElementById('total-analyses').textContent = data.total_analyses;

          // Project Status Chart
          new Chart(document.getElementById('projectStatusChart'), {
            type: 'pie',
            data: {
              labels: data.project_status.labels,
              datasets: [{
                data: data.project_status.data,
                backgroundColor: [
                  '#ffc107', // Pending - Yellow
                  '#17a2b8', // In Progress - Blue
                  '#28a745', // Completed - Green
                ],
                borderWidth: 1
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: 'right',
                }
              }
            }
          });

          // User Type Chart
          new Chart(document.getElementById('userTypeChart'), {
            type: 'pie',
            data: {
              labels: data.user_types.labels,
              datasets: [{
                data: data.user_types.data,
                backgroundColor: [
                  '#dc3545', // Admin - Red
                  '#007bff', // Client - Blue
                ],
                borderWidth: 1
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: 'right',
                }
              }
            }
          });

          // File Upload Trend Chart
          new Chart(document.getElementById('fileUploadChart'), {
            type: 'line',
            data: {
              labels: data.file_uploads.labels,
              datasets: [{
                label: 'File Uploads',
                data: data.file_uploads.data,
                borderColor: '#17a2b8',
                backgroundColor: 'rgba(23, 162, 184, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    precision: 0
                  }
                }
              }
            }
          });

          // Testimonial Ratings Chart
          new Chart(document.getElementById('testimonialRatingChart'), {
            type: 'bar',
            data: {
              labels: data.testimonial_ratings.labels,
              datasets: [{
                label: 'Count',
                data: data.testimonial_ratings.data,
                backgroundColor: '#ffc107',
                borderColor: '#e0a800',
                borderWidth: 1
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              scales: {
                y: {
                  beginAtZero: true,
                  ticks: {
                    precision: 0
                  }
                }
              }
            }
          });
        })
        .catch(error => {
          console.error('Error fetching dashboard data:', error);
        });
    });
  </script>
{% endblock %}
