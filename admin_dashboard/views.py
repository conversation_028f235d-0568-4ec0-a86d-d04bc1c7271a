from django.http import JsonResponse
from django.db.models import Count
from django.db.models.functions import TruncDay
from django.utils import timezone
from datetime import timedelta
from django.contrib.auth.decorators import user_passes_test
from django.shortcuts import render
from django.contrib.admin.views.decorators import staff_member_required
from users.models import CustomUser, Testimonial
from files.models import Project, File
from analysis.models import AnalysisRequest


@staff_member_required
def admin_dashboard(request):
    """
    View to render the admin dashboard
    """
    return render(request, 'admin_dashboard/dashboard.html')


@user_passes_test(lambda u: u.is_superuser)
def admin_dashboard_data(request):
    """
    View to provide data for the admin dashboard charts
    """
    # Get counts for stats cards
    total_users = CustomUser.objects.count()
    total_projects = Project.objects.count()
    total_files = File.objects.count()
    total_analyses = AnalysisRequest.objects.count()

    # Project Status Chart
    project_status = (
        Project.objects
        .values('status')
        .annotate(count=Count('id'))
        .order_by('status')
    )

    status_labels = []
    status_data = []

    for entry in project_status:
        # Get the display name from the choices
        status = entry['status']
        for choice in Project.STATUS_CHOICES:
            if choice[0] == status:
                label = choice[1]
                break
        else:
            label = status.replace('_', ' ').title()

        status_labels.append(label)
        status_data.append(entry['count'])

    # User Type Chart
    user_types = (
        CustomUser.objects
        .values('user_type')
        .annotate(count=Count('id'))
        .order_by('user_type')
    )

    user_type_labels = []
    user_type_data = []

    for entry in user_types:
        user_type = entry['user_type']
        if user_type == 'admin':
            label = 'Administrators'
        elif user_type == 'client':
            label = 'Clients'
        else:
            label = user_type.capitalize()

        user_type_labels.append(label)
        user_type_data.append(entry['count'])

    # File Upload Trend Chart (Last 30 days)
    end_date = timezone.now()
    start_date = end_date - timedelta(days=30)

    # Get all days in the range
    days = []
    current_date = start_date
    while current_date <= end_date:
        days.append(current_date.date())
        current_date += timedelta(days=1)

    # Get file uploads by day
    file_uploads = (
        File.objects
        .filter(uploaded_at__range=(start_date, end_date))
        .annotate(day=TruncDay('uploaded_at'))
        .values('day')
        .annotate(count=Count('id'))
        .order_by('day')
    )

    # Create a dictionary for quick lookup
    upload_counts = {entry['day'].date(): entry['count']
                     for entry in file_uploads}

    # Create the data arrays with 0 for days with no uploads
    upload_labels = [day.strftime('%b %d') for day in days]
    upload_data = [upload_counts.get(day, 0) for day in days]

    # Testimonial Ratings Chart
    testimonial_ratings = (
        Testimonial.objects
        .values('rating')
        .annotate(count=Count('id'))
        .order_by('rating')
    )

    rating_labels = []
    rating_data = []

    # Initialize with 0 for all ratings 1-5
    for i in range(1, 6):
        rating_labels.append(f"{i} Stars")
        rating_data.append(0)

    # Fill in actual data
    for entry in testimonial_ratings:
        rating = entry['rating']
        if 1 <= rating <= 5:
            rating_data[rating - 1] = entry['count']

    # Prepare the response data
    data = {
        'total_users': total_users,
        'total_projects': total_projects,
        'total_files': total_files,
        'total_analyses': total_analyses,
        'project_status': {
            'labels': status_labels,
            'data': status_data
        },
        'user_types': {
            'labels': user_type_labels,
            'data': user_type_data
        },
        'file_uploads': {
            'labels': upload_labels,
            'data': upload_data
        },
        'testimonial_ratings': {
            'labels': rating_labels,
            'data': rating_data
        }
    }

    return JsonResponse(data)
