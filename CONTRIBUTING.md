# Contributing to CompletoPLUS

Thank you for considering contributing to CompletoPLUS! This document provides guidelines and instructions for contributing to the project.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [How Can I Contribute?](#how-can-i-contribute)
- [Development Setup](#development-setup)
- [Pull Request Process](#pull-request-process)
- [Coding Standards](#coding-standards)
- [Commit Message Guidelines](#commit-message-guidelines)
- [Testing](#testing)
- [Documentation](#documentation)

## Code of Conduct

This project and everyone participating in it is governed by the CompletoPLUS Code of Conduct. By participating, you are expected to uphold this code. Please report unacceptable <NAME_EMAIL>.

## How Can I Contribute?

### Reporting Bugs

- Check if the bug has already been reported in the Issues section
- Use the bug report template when creating a new issue
- Include detailed steps to reproduce the bug
- Include screenshots if applicable
- Describe the expected behavior and what actually happened
- Include system information (browser, OS, etc.)

### Suggesting Enhancements

- Check if the enhancement has already been suggested in the Issues section
- Use the feature request template when creating a new issue
- Provide a clear and detailed explanation of the feature
- Explain why this enhancement would be useful to most users
- Include mockups or examples if applicable

### Pull Requests

- Fill in the required template
- Follow the coding standards
- Update documentation if necessary
- Include tests that verify your changes
- Ensure all tests pass
- Link to any relevant issues

## Development Setup

1. Fork the repository
2. Clone your fork:
   ```bash
   git clone https://github.com/your-username/CompletoPLUS.git
   cd CompletoPLUS
   ```
3. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```
4. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
5. Set up the database:
   ```bash
   python manage.py migrate
   ```
6. Create a superuser:
   ```bash
   python manage.py createsuperuser
   ```
7. Run the development server:
   ```bash
   python manage.py runserver
   ```

## Pull Request Process

1. Update your fork to the latest code from the main repository
2. Create a new branch for your feature or bugfix:
   ```bash
   git checkout -b feature/your-feature-name
   ```
   or
   ```bash
   git checkout -b fix/your-bugfix-name
   ```
3. Make your changes
4. Run tests to ensure your changes don't break existing functionality
5. Commit your changes following the commit message guidelines
6. Push your branch to your fork
7. Submit a pull request to the main repository
8. Address any feedback from code reviewers

## Coding Standards

### Python

- Follow PEP 8 style guide
- Use 4 spaces for indentation (not tabs)
- Maximum line length of 79 characters
- Use docstrings for functions, classes, and modules
- Use meaningful variable and function names
- Write comments for complex code sections

### Django

- Follow Django's coding style
- Use Django's ORM instead of raw SQL when possible
- Use Django's form validation
- Use Django's template system
- Follow Django's security best practices

### HTML/CSS/JavaScript

- Use 2 spaces for indentation in HTML, CSS, and JavaScript
- Follow Bootstrap conventions when using Bootstrap
- Use semantic HTML elements
- Write responsive CSS
- Use modern JavaScript (ES6+) features

## Commit Message Guidelines

We follow the Conventional Commits specification for commit messages:

```
<type>(<scope>): <subject>

<body>

<footer>
```

### Types

- **feat**: A new feature
- **fix**: A bug fix
- **docs**: Documentation only changes
- **style**: Changes that do not affect the meaning of the code (white-space, formatting, etc.)
- **refactor**: A code change that neither fixes a bug nor adds a feature
- **perf**: A code change that improves performance
- **test**: Adding missing tests or correcting existing tests
- **chore**: Changes to the build process or auxiliary tools and libraries

### Scope

The scope should be the name of the Django app affected (e.g., users, files, etc.)

### Subject

- Use the imperative, present tense: "change" not "changed" nor "changes"
- Don't capitalize the first letter
- No dot (.) at the end

### Examples

```
feat(users): add user profile page
fix(files): resolve issue with file upload
docs(readme): update installation instructions
```

## Testing

- Write tests for all new features and bug fixes
- Run the full test suite before submitting a pull request
- Aim for high test coverage
- Use Django's testing framework

To run tests:

```bash
python manage.py test
```

## Documentation

- Update the README.md if necessary
- Document all functions, classes, and modules with docstrings
- Update any relevant documentation in the docs/ directory
- Include comments in your code for complex sections

Thank you for contributing to CompletoPLUS!
