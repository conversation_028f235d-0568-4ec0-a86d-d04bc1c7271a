#!/usr/bin/env python
"""
Simple test to verify email uniqueness validation
"""

import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fileshare.settings')
django.setup()

from users.forms import CustomUserCreationForm

# Test data
form_data = {
    'username': 'testuser1',
    'email': '<EMAIL>',
    'first_name': 'Test',
    'last_name': 'User',
    'password1': 'ComplexPassword123!',
    'password2': 'ComplexPassword123!',
    'user_type': 'client',
    'terms_agreement': True,
}

print("Testing form validation...")
form = CustomUserCreationForm(data=form_data)
print(f"Form is valid: {form.is_valid()}")
if not form.is_valid():
    print(f"Form errors: {form.errors}")
else:
    print("✓ Form validation passed!")

# Test reserved email
print("\nTesting reserved admin email...")
form_data['email'] = '<EMAIL>'
form = CustomUserCreationForm(data=form_data)
print(f"Form is valid: {form.is_valid()}")
if not form.is_valid():
    print(f"✓ Correctly rejected reserved email: {form.errors.get('email', [])}")
else:
    print("✗ Should have rejected reserved email")
