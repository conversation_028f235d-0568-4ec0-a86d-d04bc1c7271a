from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from .models import AnalysisRequest


@admin.register(AnalysisRequest)
class AnalysisRequestAdmin(admin.ModelAdmin):
    def has_module_permission(self, request):
        # Allow staff and admin users to access the module
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_view_permission(self, request, obj=None):
        # Allow staff and admin users to view all analysis requests
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_add_permission(self, request):
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_change_permission(self, request, obj=None):
        if obj and (obj.client == request.user or obj.analyst == request.user):
            return True
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_delete_permission(self, request, obj=None):
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    list_display = ('title', 'client_link', 'analyst_link',
                    'analysis_type', 'status', 'created_at')
    list_filter = ('status', 'analysis_type', 'created_at')
    search_fields = ('title', 'description',
                     'client__username', 'analyst__username')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Analysis Information', {
            'fields': ('title', 'description', 'project', 'client', 'analyst', 'analysis_type', 'status')
        }),
        ('Additional Information', {
            'fields': ('priority', 'estimated_hours', 'actual_hours', 'completed_at')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def client_link(self, obj):
        if obj.client:
            url = reverse('admin:users_customuser_change',
                          args=[obj.client.id])
            return format_html('<a href="{}">{}</a>', url, obj.client.username)
        return "-"

    client_link.short_description = 'Client'

    def analyst_link(self, obj):
        if obj.analyst:
            url = reverse('admin:users_customuser_change',
                          args=[obj.analyst.id])
            return format_html('<a href="{}">{}</a>', url, obj.analyst.username)
        return "-"

    analyst_link.short_description = 'Analyst'
