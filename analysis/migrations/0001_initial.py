# Generated by Django 5.2 on 2025-04-15 04:31

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('files', '0005_auto_add_zip_fields'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AnalysisRequest',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField()),
                ('analysis_type', models.CharField(choices=[('descriptive', 'Descriptive Statistics'), ('inferential', 'Inferential Statistics'), ('regression', 'Regression Analysis'), ('clustering', 'Clustering Analysis'), ('time_series', 'Time Series Analysis'), ('text_analysis', 'Text Analysis'), ('custom', 'Custom Analysis')], max_length=20)),
                ('status', models.Char<PERSON>ield(choices=[('pending', 'Pending'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('failed', 'Failed')], default='pending', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('priority', models.IntegerField(default=0, help_text='Higher number means higher priority')),
                ('estimated_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('actual_hours', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True)),
                ('analyst', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_analyses', to=settings.AUTH_USER_MODEL)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analysis_requests', to=settings.AUTH_USER_MODEL)),
                ('input_files', models.ManyToManyField(related_name='analysis_inputs', to='files.file')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analysis_requests', to='files.project')),
            ],
        ),
        migrations.CreateModel(
            name='AnalysisParameter',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('value', models.TextField()),
                ('description', models.TextField(blank=True, null=True)),
                ('analysis_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='parameters', to='analysis.analysisrequest')),
            ],
        ),
        migrations.CreateModel(
            name='AnalysisNote',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('note', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_private', models.BooleanField(default=False, help_text='If true, only visible to analysts')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='analysis_notes', to=settings.AUTH_USER_MODEL)),
                ('analysis_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes', to='analysis.analysisrequest')),
            ],
        ),
        migrations.CreateModel(
            name='AnalysisResult',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('result_file', models.FileField(upload_to='analysis_results/')),
                ('result_type', models.CharField(help_text='File type or format of the result', max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_final', models.BooleanField(default=False, help_text='Whether this is the final result or an intermediate one')),
                ('analysis_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='results', to='analysis.analysisrequest')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_results', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
