from django.urls import path
from . import views

urlpatterns = [
    # Analysis list and detail views
    path('', views.analysis_list, name='analysis_list'),
    path('<uuid:pk>/', views.analysis_detail, name='analysis_detail'),
    
    # Create and update analysis requests
    path('create/', views.create_analysis_request, name='create_analysis_request'),
    path('create/<uuid:project_id>/', views.create_analysis_request, name='create_analysis_request_for_project'),
    path('<uuid:pk>/update/', views.update_analysis_request, name='update_analysis_request'),
    path('<uuid:pk>/delete/', views.delete_analysis_request, name='delete_analysis_request'),
    
    # Add notes, results, and parameters
    path('<uuid:analysis_id>/add-note/', views.add_analysis_note, name='add_analysis_note'),
    path('<uuid:analysis_id>/add-result/', views.add_analysis_result, name='add_analysis_result'),
    path('<uuid:analysis_id>/add-parameter/', views.add_analysis_parameter, name='add_analysis_parameter'),
    
    # Update status and assign analyst
    path('<uuid:analysis_id>/update-status/', views.update_analysis_status, name='update_analysis_status'),
    path('<uuid:analysis_id>/assign-analyst/', views.assign_analyst, name='assign_analyst'),
]
