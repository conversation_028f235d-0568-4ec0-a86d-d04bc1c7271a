from django.db import models
from django.conf import settings
from files.models import Project, File
import uuid


class AnalysisRequest(models.Model):
    """Model for data analysis requests"""
    ANALYSIS_TYPE_CHOICES = (
        ('descriptive', 'Descriptive Statistics'),
        ('inferential', 'Inferential Statistics'),
        ('regression', 'Regression Analysis'),
        ('clustering', 'Clustering Analysis'),
        ('time_series', 'Time Series Analysis'),
        ('text_analysis', 'Text Analysis'),
        ('custom', 'Custom Analysis'),
    )

    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    project = models.ForeignKey(
        Project, on_delete=models.CASCADE, related_name='analysis_requests')
    client = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='analysis_requests')
    analyst = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL,
                                null=True, blank=True, related_name='assigned_analyses')
    title = models.CharField(max_length=255)
    description = models.TextField()
    analysis_type = models.CharField(
        max_length=20, choices=ANALYSIS_TYPE_CHOICES)
    input_files = models.ManyToManyField(File, related_name='analysis_inputs')
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    priority = models.IntegerField(
        default=0, help_text="Higher number means higher priority")
    estimated_hours = models.DecimalField(
        max_digits=5, decimal_places=2, null=True, blank=True)
    actual_hours = models.DecimalField(
        max_digits=5, decimal_places=2, null=True, blank=True)

    def __str__(self):
        return self.title


class AnalysisResult(models.Model):
    """Model for data analysis results"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    analysis_request = models.ForeignKey(
        AnalysisRequest, on_delete=models.CASCADE, related_name='results')
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    result_file = models.FileField(upload_to='analysis_results/')
    result_type = models.CharField(
        max_length=50, help_text="File type or format of the result")
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='created_results')
    is_final = models.BooleanField(
        default=False, help_text="Whether this is the final result or an intermediate one")

    def __str__(self):
        return self.title


class AnalysisNote(models.Model):
    """Model for notes on data analysis"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    analysis_request = models.ForeignKey(
        AnalysisRequest, on_delete=models.CASCADE, related_name='notes')
    note = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='analysis_notes')
    is_private = models.BooleanField(
        default=False, help_text="If true, only visible to analysts")

    def __str__(self):
        return f"Note on {self.analysis_request.title} by {self.created_by.username}"


class AnalysisParameter(models.Model):
    """Model for parameters used in data analysis"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    analysis_request = models.ForeignKey(
        AnalysisRequest, on_delete=models.CASCADE, related_name='parameters')
    name = models.CharField(max_length=100)
    value = models.TextField()
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.name}: {self.value}"
