from django import forms
from .models import AnalysisRequest, AnalysisResult, AnalysisNote, AnalysisParameter
from files.models import File

class AnalysisRequestForm(forms.ModelForm):
    """Form for creating and updating analysis requests"""
    class Meta:
        model = AnalysisRequest
        fields = ['title', 'description', 'analysis_type', 'input_files']
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'analysis_type': forms.Select(attrs={'class': 'form-select'}),
            'input_files': forms.SelectMultiple(attrs={'class': 'form-select', 'size': 5}),
        }
    
    def __init__(self, *args, project=None, **kwargs):
        super().__init__(*args, **kwargs)
        
        # If project is provided, filter input_files by project
        if project:
            self.fields['input_files'].queryset = File.objects.filter(project=project)
            
        # Add help texts
        self.fields['title'].help_text = 'Enter a descriptive title for this analysis'
        self.fields['description'].help_text = 'Describe what you want to analyze and any specific requirements'
        self.fields['analysis_type'].help_text = 'Select the type of analysis you need'
        self.fields['input_files'].help_text = 'Select the files to analyze (hold Ctrl/Cmd to select multiple)'

class AnalysisResultForm(forms.ModelForm):
    """Form for uploading analysis results"""
    class Meta:
        model = AnalysisResult
        fields = ['title', 'description', 'result_file', 'result_type', 'is_final']
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'result_file': forms.FileInput(attrs={'class': 'form-control'}),
            'result_type': forms.TextInput(attrs={'class': 'form-control'}),
            'is_final': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class AnalysisNoteForm(forms.ModelForm):
    """Form for adding notes to analysis requests"""
    class Meta:
        model = AnalysisNote
        fields = ['note', 'is_private']
        widgets = {
            'note': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Add a note about this analysis...'}),
            'is_private': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class AnalysisParameterForm(forms.ModelForm):
    """Form for adding parameters to analysis requests"""
    class Meta:
        model = AnalysisParameter
        fields = ['name', 'value', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'value': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
        }

class AnalysisFilterForm(forms.Form):
    """Form for filtering analysis requests"""
    STATUS_CHOICES = (
        ('', 'All Statuses'),
    ) + AnalysisRequest.STATUS_CHOICES
    
    ANALYSIS_TYPE_CHOICES = (
        ('', 'All Types'),
    ) + AnalysisRequest.ANALYSIS_TYPE_CHOICES
    
    status = forms.ChoiceField(
        choices=STATUS_CHOICES, 
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    analysis_type = forms.ChoiceField(
        choices=ANALYSIS_TYPE_CHOICES, 
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
    )
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Search...'})
    )
