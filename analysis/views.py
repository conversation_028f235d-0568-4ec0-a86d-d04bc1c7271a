from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse, Http404, JsonResponse
from django.contrib import messages
from django.core.exceptions import PermissionDenied
from django.utils import timezone
from django.db.models import Q

from .models import AnalysisRequest, AnalysisResult, AnalysisNote, AnalysisParameter
from .forms import AnalysisRequestForm, AnalysisResultForm, AnalysisNoteForm, AnalysisParameterForm, AnalysisFilterForm
from files.models import Project, File

import logging

# Set up logger
logger = logging.getLogger(__name__)


@login_required
def analysis_list(request):
    """List all analysis requests"""
    # Initialize the filter form
    filter_form = AnalysisFilterForm(request.GET)

    # Start with all analysis requests the user has access to
    if request.user.is_admin_user():
        # <PERSON><PERSON> can see all analysis requests
        analysis_requests = AnalysisRequest.objects.all()
    else:
        # Clients can only see their own analysis requests
        analysis_requests = AnalysisRequest.objects.filter(client=request.user)

    # Apply filters if the form is valid
    if filter_form.is_valid():
        # Filter by status
        status = filter_form.cleaned_data.get('status')
        if status:
            analysis_requests = analysis_requests.filter(status=status)

        # Filter by analysis type
        analysis_type = filter_form.cleaned_data.get('analysis_type')
        if analysis_type:
            analysis_requests = analysis_requests.filter(
                analysis_type=analysis_type)

        # Filter by date range
        date_from = filter_form.cleaned_data.get('date_from')
        if date_from:
            analysis_requests = analysis_requests.filter(
                created_at__gte=date_from)

        date_to = filter_form.cleaned_data.get('date_to')
        if date_to:
            analysis_requests = analysis_requests.filter(
                created_at__lte=date_to)

        # Search by title or description
        search = filter_form.cleaned_data.get('search')
        if search:
            analysis_requests = analysis_requests.filter(
                Q(title__icontains=search) |
                Q(description__icontains=search)
            )

    # Order by most recent first
    analysis_requests = analysis_requests.order_by('-created_at')

    return render(request, 'analysis/analysis_list.html', {
        'analysis_requests': analysis_requests,
        'filter_form': filter_form
    })


@login_required
def analysis_detail(request, pk):
    """View analysis request details"""
    analysis_request = get_object_or_404(AnalysisRequest, pk=pk)

    # Check if the user has permission to view this analysis
    if not request.user.is_admin_user() and analysis_request.client != request.user:
        raise PermissionDenied

    # Get all results for this analysis
    results = analysis_request.results.all().order_by('-created_at')

    # Get all notes for this analysis
    if request.user.is_admin_user():
        # Admins can see all notes
        notes = analysis_request.notes.all().order_by('-created_at')
    else:
        # Clients can only see public notes
        notes = analysis_request.notes.filter(
            is_private=False).order_by('-created_at')

    # Get all parameters for this analysis
    parameters = analysis_request.parameters.all()

    # Prepare forms
    note_form = AnalysisNoteForm()
    result_form = AnalysisResultForm() if request.user.is_admin_user() else None
    parameter_form = AnalysisParameterForm() if request.user.is_admin_user() else None

    return render(request, 'analysis/analysis_detail.html', {
        'analysis_request': analysis_request,
        'results': results,
        'notes': notes,
        'parameters': parameters,
        'note_form': note_form,
        'result_form': result_form,
        'parameter_form': parameter_form
    })


@login_required
def create_analysis_request(request, project_id=None):
    """Create a new analysis request"""
    # If project_id is provided, get the project
    project = None
    if project_id:
        project = get_object_or_404(Project, pk=project_id)
        # Check if the user has permission to create an analysis for this project
        if not request.user.is_admin_user() and project.client != request.user:
            raise PermissionDenied

    if request.method == 'POST':
        form = AnalysisRequestForm(request.POST, project=project)
        if form.is_valid():
            analysis_request = form.save(commit=False)

            # Set the client and project
            analysis_request.client = request.user
            if project:
                analysis_request.project = project

            # Save the analysis request
            analysis_request.save()

            # Save the many-to-many relationships
            form.save_m2m()

            messages.success(request, 'Analysis request created successfully!')
            return redirect('analysis_detail', pk=analysis_request.id)
    else:
        form = AnalysisRequestForm(project=project)

    return render(request, 'analysis/analysis_form.html', {
        'form': form,
        'project': project
    })


@login_required
def update_analysis_request(request, pk):
    """Update an existing analysis request"""
    analysis_request = get_object_or_404(AnalysisRequest, pk=pk)

    # Check if the user has permission to update this analysis
    if not request.user.is_admin_user() and analysis_request.client != request.user:
        raise PermissionDenied

    # Clients can only update pending analysis requests
    if not request.user.is_admin_user() and analysis_request.status != 'pending':
        messages.error(
            request, 'You can only update pending analysis requests.')
        return redirect('analysis_detail', pk=analysis_request.id)

    if request.method == 'POST':
        form = AnalysisRequestForm(
            request.POST, instance=analysis_request, project=analysis_request.project)
        if form.is_valid():
            form.save()
            messages.success(request, 'Analysis request updated successfully!')
            return redirect('analysis_detail', pk=analysis_request.id)
    else:
        form = AnalysisRequestForm(
            instance=analysis_request, project=analysis_request.project)

    return render(request, 'analysis/analysis_form.html', {
        'form': form,
        'analysis_request': analysis_request,
        'project': analysis_request.project
    })


@login_required
def add_analysis_note(request, analysis_id):
    """Add a note to an analysis request"""
    analysis_request = get_object_or_404(AnalysisRequest, pk=analysis_id)

    # Check if the user has permission to add a note to this analysis
    if not request.user.is_admin_user() and analysis_request.client != request.user:
        raise PermissionDenied

    if request.method == 'POST':
        form = AnalysisNoteForm(request.POST)
        if form.is_valid():
            note = form.save(commit=False)
            note.analysis_request = analysis_request
            note.created_by = request.user

            # Only admins can create private notes
            if not request.user.is_admin_user():
                note.is_private = False

            note.save()

            messages.success(request, 'Note added successfully!')

            # Return JSON response for AJAX requests
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'status': 'success',
                    'message': 'Note added successfully!',
                    'note_id': str(note.id),
                    'note_text': note.note,
                    'created_at': note.created_at.strftime('%b %d, %Y %H:%M'),
                    'created_by': request.user.username,
                    'is_private': note.is_private
                })

            return redirect('analysis_detail', pk=analysis_id)
        else:
            # Return errors for AJAX requests
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({'status': 'error', 'errors': form.errors}, status=400)

    # If not POST or form is invalid, redirect back to analysis detail
    return redirect('analysis_detail', pk=analysis_id)


@login_required
def add_analysis_result(request, analysis_id):
    """Add a result to an analysis request"""
    analysis_request = get_object_or_404(AnalysisRequest, pk=analysis_id)

    # Check if the user has permission to add a result to this analysis
    if not request.user.is_admin_user():
        raise PermissionDenied

    if request.method == 'POST':
        form = AnalysisResultForm(request.POST, request.FILES)
        if form.is_valid():
            result = form.save(commit=False)
            result.analysis_request = analysis_request
            result.created_by = request.user
            result.save()

            # If this is the final result and the analysis is not completed, mark it as completed
            if result.is_final and analysis_request.status != 'completed':
                analysis_request.status = 'completed'
                analysis_request.completed_at = timezone.now()
                analysis_request.save()

            messages.success(request, 'Result added successfully!')
            return redirect('analysis_detail', pk=analysis_id)

    # If not POST or form is invalid, redirect back to analysis detail
    return redirect('analysis_detail', pk=analysis_id)


@login_required
def add_analysis_parameter(request, analysis_id):
    """Add a parameter to an analysis request"""
    analysis_request = get_object_or_404(AnalysisRequest, pk=analysis_id)

    # Check if the user has permission to add a parameter to this analysis
    if not request.user.is_admin_user():
        raise PermissionDenied

    if request.method == 'POST':
        form = AnalysisParameterForm(request.POST)
        if form.is_valid():
            parameter = form.save(commit=False)
            parameter.analysis_request = analysis_request
            parameter.save()

            messages.success(request, 'Parameter added successfully!')
            return redirect('analysis_detail', pk=analysis_id)

    # If not POST or form is invalid, redirect back to analysis detail
    return redirect('analysis_detail', pk=analysis_id)


@login_required
def update_analysis_status(request, analysis_id):
    """Update the status of an analysis request"""
    analysis_request = get_object_or_404(AnalysisRequest, pk=analysis_id)

    # Check if the user has permission to update the status of this analysis
    if not request.user.is_admin_user():
        raise PermissionDenied

    if request.method == 'POST':
        status = request.POST.get('status')
        if status in dict(AnalysisRequest.STATUS_CHOICES):
            analysis_request.status = status

            # If the status is completed, set the completed_at timestamp
            if status == 'completed':
                analysis_request.completed_at = timezone.now()

            analysis_request.save()

            messages.success(
                request, f'Analysis status updated to {dict(AnalysisRequest.STATUS_CHOICES)[status]}!')
        else:
            messages.error(request, 'Invalid status!')

    return redirect('analysis_detail', pk=analysis_id)


@login_required
def assign_analyst(request, analysis_id):
    """Assign an analyst to an analysis request"""
    analysis_request = get_object_or_404(AnalysisRequest, pk=analysis_id)

    # Check if the user has permission to assign an analyst to this analysis
    if not request.user.is_admin_user():
        raise PermissionDenied

    if request.method == 'POST':
        analyst_id = request.POST.get('analyst_id')
        if analyst_id:
            from django.contrib.auth import get_user_model
            User = get_user_model()

            try:
                analyst = User.objects.get(id=analyst_id)
                if analyst.is_admin_user():
                    analysis_request.analyst = analyst
                    analysis_request.save()
                    messages.success(
                        request, f'Analyst {analyst.username} assigned successfully!')
                else:
                    messages.error(request, 'Selected user is not an analyst!')
            except User.DoesNotExist:
                messages.error(request, 'Selected user does not exist!')
        else:
            # If analyst_id is empty, unassign the analyst
            analysis_request.analyst = None
            analysis_request.save()
            messages.success(request, 'Analyst unassigned successfully!')

    return redirect('analysis_detail', pk=analysis_id)


@login_required
def delete_analysis_request(request, pk):
    """Delete an analysis request"""
    analysis_request = get_object_or_404(AnalysisRequest, pk=pk)

    # Check if the user has permission to delete this analysis
    if not request.user.is_admin_user() and analysis_request.client != request.user:
        raise PermissionDenied

    # Clients can only delete pending analysis requests
    if not request.user.is_admin_user() and analysis_request.status != 'pending':
        messages.error(
            request, 'You can only delete pending analysis requests.')
        return redirect('analysis_detail', pk=analysis_request.id)

    if request.method == 'POST':
        analysis_request.delete()
        messages.success(request, 'Analysis request deleted successfully!')
        return redirect('analysis_list')

    return render(request, 'analysis/analysis_confirm_delete.html', {
        'analysis_request': analysis_request
    })
