#!/usr/bin/env python3
"""
Demo script to show HTTPS enforcement for CompletoPLUS
This script demonstrates how the HTTPS enforcement works in different modes.
"""

import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fileshare.settings')
django.setup()

from django.conf import settings
from django.test import RequestFactory
from fileshare.middleware import StrictHTTPSMiddleware


def demo_https_enforcement():
    """Demonstrate HTTPS enforcement in different modes"""
    print("=" * 60)
    print("CompletoPLUS HTTPS Enforcement Demo")
    print("=" * 60)
    print()
    
    # Show current settings
    print("Current Settings:")
    print(f"  DEBUG: {settings.DEBUG}")
    print(f"  HTTPS_ENFORCEMENT_MODE: {getattr(settings, 'HTTPS_ENFORCEMENT_MODE', 'Not set')}")
    print(f"  SECURE_SSL_REDIRECT: {getattr(settings, 'SECURE_SSL_REDIRECT', 'Not set')}")
    print(f"  SECURE_HSTS_SECONDS: {getattr(settings, 'SECURE_HSTS_SECONDS', 'Not set')}")
    print()
    
    # Create request factory
    factory = RequestFactory()
    
    # Create middleware instance
    def dummy_response(request):
        from django.http import HttpResponse
        return HttpResponse("OK")
    
    middleware = StrictHTTPSMiddleware(dummy_response)
    
    # Test scenarios
    test_scenarios = [
        {
            'name': 'HTTP Request to completoplus.com',
            'host': 'completoplus.com',
            'secure': False,
            'path': '/'
        },
        {
            'name': 'HTTP Request to www.completoplus.com',
            'host': 'www.completoplus.com',
            'secure': False,
            'path': '/dashboard'
        },
        {
            'name': 'HTTPS Request to completoplus.com',
            'host': 'completoplus.com',
            'secure': True,
            'path': '/'
        }
    ]
    
    print("Testing HTTPS Enforcement:")
    print("-" * 40)
    
    for scenario in test_scenarios:
        print(f"\nScenario: {scenario['name']}")
        
        # Create request
        if scenario['secure']:
            request = factory.get(scenario['path'], HTTP_HOST=scenario['host'])
            request.META['HTTP_X_FORWARDED_PROTO'] = 'https'
        else:
            request = factory.get(scenario['path'], HTTP_HOST=scenario['host'])
            request.META['HTTP_X_FORWARDED_PROTO'] = 'http'
        
        # Process through middleware
        response = middleware(request)
        
        # Analyze response
        if hasattr(response, 'status_code'):
            if response.status_code in [301, 302, 307, 308]:
                location = response.get('Location', 'No location header')
                print(f"  Result: Redirected to {location} (Status: {response.status_code})")
            elif response.status_code == 403:
                print(f"  Result: Blocked with 403 Forbidden")
            elif response.status_code == 200:
                print(f"  Result: Allowed (Status: 200)")
            else:
                print(f"  Result: Unexpected status {response.status_code}")
        else:
            print(f"  Result: No response object returned")


def show_security_headers():
    """Show what security headers are applied"""
    print("\n" + "=" * 60)
    print("Security Headers Applied in Production")
    print("=" * 60)
    
    headers = [
        ("Strict-Transport-Security", "max-age=31536000; includeSubDomains; preload"),
        ("X-Content-Type-Options", "nosniff"),
        ("X-Frame-Options", "DENY"),
        ("X-XSS-Protection", "1; mode=block"),
        ("Referrer-Policy", "strict-origin-when-cross-origin"),
        ("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline'..."),
        ("Permissions-Policy", "geolocation=(), microphone=(), camera=()..."),
    ]
    
    for header, value in headers:
        print(f"  {header}: {value}")


def show_deployment_info():
    """Show deployment information for Railway.com"""
    print("\n" + "=" * 60)
    print("Railway.com Deployment Configuration")
    print("=" * 60)
    print()
    print("Environment Variables to Set in Railway:")
    print("  DEBUG=False")
    print("  HTTPS_ENFORCEMENT_MODE=REDIRECT  # or BLOCK for stricter security")
    print()
    print("Domain Configuration:")
    print("  Primary: completoplus.com")
    print("  Secondary: www.completoplus.com")
    print()
    print("Expected Behavior in Production:")
    print("  ✓ http://completoplus.com → https://completoplus.com")
    print("  ✓ http://www.completoplus.com → https://www.completoplus.com")
    print("  ✓ https://completoplus.com → 200 OK (with security headers)")
    print("  ✓ https://www.completoplus.com → 200 OK (with security headers)")


if __name__ == "__main__":
    demo_https_enforcement()
    show_security_headers()
    show_deployment_info()
    
    print("\n" + "=" * 60)
    print("To test HTTPS enforcement:")
    print("  python manage.py test_https")
    print("=" * 60)
