from django.contrib import admin
from django.utils.translation import gettext_lazy as _

# Import models
from users.models import CustomUser, DashboardSettings, EmailLog, Testimonial
from files.models import Project, File, Notification, ProgressNote, FileDownload

# Import admin classes
from users.admin import CustomUserAdmin, EmailLogAdmin, TestimonialAdmin
from files.admin import ProjectAdmin, FileAdmin, NotificationAdmin, ProgressNoteAdmin, FileDownloadAdmin

# Configure the default admin site
admin.site.site_title = _('CompletoPLUS Admin')
admin.site.site_header = _('CompletoPLUS Administration')
admin.site.index_title = _('Admin Dashboard')

# Register models with the default admin site
admin.site.register(CustomUser, CustomUserAdmin)
admin.site.register(Project, ProjectAdmin)
admin.site.register(File, FileAdmin)
admin.site.register(Notification, NotificationAdmin)
admin.site.register(ProgressNote, ProgressNoteAdmin)
admin.site.register(FileDownload, FileDownloadAdmin)
admin.site.register(<PERSON><PERSON><PERSON><PERSON>, EmailLogAdmin)
admin.site.register(Testimonial, TestimonialAdmin)
