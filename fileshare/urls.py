"""
URL configuration for fileshare project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic.base import TemplateView
from django.contrib.staticfiles.storage import staticfiles_storage
from django.views.generic.base import RedirectView
from django.http import HttpResponse
from django.template.loader import render_to_string

# Views for dynamic sitemap and robots.txt


def sitemap_view(request):
    """Generate a dynamic sitemap with the current site URL"""
    sitemap_content = render_to_string(
        'sitemap.xml', {'site_url': settings.SITE_URL})
    return HttpResponse(sitemap_content, content_type='application/xml')


def robots_view(request):
    """Generate a dynamic robots.txt with the current site URL"""
    robots_content = render_to_string(
        'robots.txt', {'site_url': settings.SITE_URL})
    return HttpResponse(robots_content, content_type='text/plain')


# Base URL patterns
urlpatterns = [
    # Use the default Django admin site with custom URL
    path('completoplus-admin/', admin.site.urls),
    path('', include('users.urls')),
    path('files/', include('files.urls')),
    path('analysis/', include('analysis.urls')),
    path('robots.txt', robots_view, name='robots'),
    path('sitemap.xml', sitemap_view, name='sitemap'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL,
                          document_root=settings.MEDIA_ROOT)
