class StrictHTTPSMiddleware:
    """
    Middleware to strictly enforce HTTPS in production.
    Provides two modes: REDIRECT (default) or BLOCK.

    Set HTTPS_ENFORCEMENT_MODE in settings to control behavior:
    - 'REDIRECT': Automatically redirect HTTP to HTTPS (user-friendly)
    - 'BLOCK': Block HTTP requests with 403 Forbidden (strict security)
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        from django.conf import settings
        from django.http import HttpResponseForbidden, HttpResponsePermanentRedirect

        # Only enforce in production
        if not settings.DEBUG:
            # Get the host from the request
            host = request.get_host().lower()

            # Check if the request is secure (either directly or via proxy)
            is_secure = request.is_secure() or (
                'HTTP_X_FORWARDED_PROTO' in request.META and
                request.META['HTTP_X_FORWARDED_PROTO'] == 'https'
            )

            # If request is not secure, enforce HTTPS
            if not is_secure:
                # Get enforcement mode from settings (default to REDIRECT for better UX)
                enforcement_mode = getattr(
                    settings, 'HTTPS_ENFORCEMENT_MODE', 'REDIRECT')

                if enforcement_mode == 'BLOCK':
                    # Block HTTP requests with a 403 Forbidden
                    suggested_url = f"https://{host}{request.get_full_path()}"
                    return HttpResponseForbidden(
                        f'<!DOCTYPE html><html><head><title>HTTPS Required</title>'
                        f'<meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1">'
                        f'<style>body{{font-family:Arial,sans-serif;text-align:center;padding:50px;background:#f5f5f5}}'
                        f'.container{{max-width:600px;margin:0 auto;background:white;padding:40px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1)}}'
                        f'h1{{color:#d32f2f;margin-bottom:20px}}p{{color:#666;line-height:1.6;margin-bottom:20px}}'
                        f'a{{color:#1976d2;text-decoration:none;font-weight:bold}}a:hover{{text-decoration:underline}}</style>'
                        f'</head><body><div class="container">'
                        f'<h1>🔒 HTTPS Required</h1>'
                        f'<p>This website requires a secure HTTPS connection for your protection.</p>'
                        f'<p>Please access the site using: <a href="{suggested_url}">{suggested_url}</a></p>'
                        f'<p><small>HTTP connections are not permitted for security reasons.</small></p>'
                        f'</div></body></html>'
                    )
                else:
                    # REDIRECT mode: Automatically redirect to HTTPS
                    https_url = f"https://{host}{request.get_full_path()}"
                    return HttpResponsePermanentRedirect(https_url)

        return self.get_response(request)


class DomainCanonicalizeMiddleware:
    """
    Middleware to ensure both apex domain and www subdomain work properly.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        from django.conf import settings
        from django.http import HttpResponsePermanentRedirect

        # Only process in production
        if not settings.DEBUG:
            host = request.get_host().lower()

            # If the request is secure and for completoplus.com without www
            # We don't redirect - we want both domains to work
            # This middleware just ensures they both can be accessed

            # Process the request normally
            return self.get_response(request)
        else:
            # In development, just process the request normally
            return self.get_response(request)


class SecurityHeadersMiddleware:
    """
    Middleware to add comprehensive security headers for production security
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        # Core security headers (always applied)
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'

        # Content Security Policy for additional protection
        response['Content-Security-Policy'] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; "
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; "
            "font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net; "
            "img-src 'self' data: https: blob:; "
            "connect-src 'self' https:; "
            "frame-ancestors 'none'; "
            "base-uri 'self'; "
            "form-action 'self';"
        )

        # Permissions Policy to restrict browser features
        response['Permissions-Policy'] = (
            "geolocation=(), "
            "microphone=(), "
            "camera=(), "
            "payment=(), "
            "usb=(), "
            "magnetometer=(), "
            "gyroscope=(), "
            "speaker=()"
        )

        # Production-only security headers
        from django.conf import settings
        if not settings.DEBUG:
            # HSTS header for HTTPS enforcement
            response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload'

            # Expect-CT header for certificate transparency
            response['Expect-CT'] = 'max-age=86400, enforce'

            # Additional security headers for production
            response['X-Permitted-Cross-Domain-Policies'] = 'none'
            response['Cross-Origin-Embedder-Policy'] = 'require-corp'
            response['Cross-Origin-Opener-Policy'] = 'same-origin'
            response['Cross-Origin-Resource-Policy'] = 'same-origin'

        return response


class AdminAccessMiddleware:
    """
    Middleware to prevent non-admin users from accessing the admin site
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Check for the admin URL
        if request.path.startswith('/completoplus-admin/'):
            # If the user is not authenticated, redirect to login
            if not request.user.is_authenticated:
                from django.shortcuts import redirect
                from django.urls import reverse
                login_url = f"{reverse('login')}?next={request.path}"
                return redirect(login_url)

            # Check if the user has admin permissions
            has_admin_permission = False

            # Check superuser and staff status
            if request.user.is_superuser or request.user.is_staff:
                has_admin_permission = True

            # Check user_type
            try:
                if getattr(request.user, 'user_type', '') == 'admin':
                    has_admin_permission = True
            except:
                pass

            # If the user doesn't have admin permissions, redirect to dashboard
            if not has_admin_permission:
                from django.contrib import messages
                from django.shortcuts import redirect
                messages.error(
                    request, 'Access denied. You do not have permission to access the admin panel.')
                return redirect('dashboard')

        # Process the request
        return self.get_response(request)
