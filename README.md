# CompletoPLUS

CompletoPLUS is a comprehensive web application designed to facilitate academic work submission, tracking, and delivery between clients and administrators. The platform enables students and researchers to submit their assignments, projects, theses, dissertations, data analysis, web development, and machine learning projects for expert completion.

## Table of Contents

- [Features](#features)
- [Technology Stack](#technology-stack)
- [Project Structure](#project-structure)
- [Installation](#installation)
- [Configuration](#configuration)
- [User Roles](#user-roles)
- [Core Functionality](#core-functionality)
- [File Management](#file-management)
- [Data Analysis](#data-analysis)
- [Project Management](#project-management)
- [Admin Dashboard](#admin-dashboard)
- [Client Dashboard](#client-dashboard)
- [Security Considerations](#security-considerations)
- [Deployment](#deployment)
- [Contributing](#contributing)
- [License](#license)
- [Recent Updates](#recent-updates)
- [Testimonial System](#testimonial-system)

## Features

### Core Features

- **User Authentication**: Secure login, registration, and user management
- **Project Management**: Create, track, and manage academic projects
- **File Sharing**: Secure upload and download of academic files and folders
- **Automatic File Compression**: All uploaded files and folders are automatically zipped to reduce storage space
- **Data Analysis**: Professional data analysis services using Python for research projects
- **Web Development**: Custom web application development services
- **Machine Learning**: AI and machine learning solutions for various applications
- **Notifications**: Updates on project status and file uploads
- **Progress Tracking**: Monitor project completion with progress indicators
- **Admin Dashboard**: Comprehensive management interface for administrators
- **Client Dashboard**: User-friendly interface for clients to track their projects
- **Testimonial System**: Client testimonials with ratings, admin approval, and featured testimonials
- **Dark/Light Mode**: Customizable UI theme for better user experience
- **Responsive Design**: Mobile-friendly interface that works on all devices

### Advanced Features

- **File Preview**: Preview supported file types directly in the browser
- **Batch Operations**: Download or delete multiple files at once
- **Progress Notes**: Add detailed notes to track project development
- **Dashboard Customization**: Personalize dashboard layout and widgets
- **File Download Tracking**: Monitor file access and downloads
- **Export Functionality**: Export project data in CSV or JSON formats

## Technology Stack

- **Backend**: Django 5.2 (Python web framework)
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Database**: SQLite (development), PostgreSQL (recommended for production)
- **File Storage**: Django's file storage system
- **Authentication**: Django's built-in authentication system
- **UI Components**: Bootstrap 5, Font Awesome, Chart.js
- **Admin Dashboard**: Custom Django admin with interactive charts
- **Styling**: Custom CSS with Bootstrap theming

## Project Structure

The application follows a standard Django project structure with the following main components:

- **fileshare/**: Main project directory

  - **settings.py**: Project settings
  - **urls.py**: Main URL routing
  - **asgi.py**: ASGI configuration
  - **wsgi.py**: WSGI configuration for HTTP
  - **admin.py**: Custom admin site configuration

- **users/**: User management app

  - **models.py**: User and dashboard settings models
  - **views.py**: User-related views (login, registration, dashboard)
  - **forms.py**: User forms
  - **urls.py**: User URL routing

- **files/**: File and project management app

  - **models.py**: Project, file, notification, and progress note models
  - **views.py**: File and project views
  - **forms.py**: File and project forms
  - **urls.py**: File URL routing

- **templates/**: HTML templates

  - **base/**: Base templates
  - **users/**: User-related templates
  - **files/**: File and project templates
  - **admin/**: Custom admin templates

- **static/**: Static files

  - **css/**: CSS stylesheets
  - **js/**: JavaScript files
  - **img/**: Images and icons

- **media/**: User-uploaded files (not tracked in version control)

## Installation

### Prerequisites

- Python 3.9 or higher

- Git (optional, for cloning the repository)

### Setup Steps

1. **Clone the repository** (if using Git):

   ```bash
   git clone https://github.com/clevernat/CompletoPLUS.git
   cd CompletoPLUS
   ```

2. **Create and activate a virtual environment**:

   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:

   ```bash
   pip install -r requirements.txt
   ```

4. **Set up the database**:

   ```bash
   python manage.py migrate
   ```

5. **Create a superuser**:

   ```bash
   python manage.py createsuperuser
   ```

6. **Run the development server**:

   ```bash
   python manage.py runserver
   ```

7. **Access the application**:
   - Main site: http://127.0.0.1:8000/
   - Admin interface: http://127.0.0.1:8000/completoplus-admin/ (custom secure URL)

### Alternative Setup with Test Data

To quickly set up the application with test users:

1. **Run the database migrations**:

   ```bash
   python manage.py migrate
   ```

2. **Create a superuser**:

   ```bash
   python manage.py createsuperuser
   ```

   Example credentials:

   - Username: `admin`
   - Email: `<EMAIL>`
   - Password: `admin`

## Configuration

### Environment Variables

For production, it's recommended to use environment variables for sensitive settings. Create a `.env` file in the root directory based on the `.env.example` file:

```
# PostgreSQL Database Configuration
PGDATABASE=railway
PGUSER=postgres
PGPASSWORD=your_password_here
PGHOST=turntable.proxy.rlwy.net
PGPORT=56454

# Django Settings
DEBUG=False
SECRET_KEY=jf9y@lg=6dyps%n!6&mziti&v+%a*65$u3@b@8t*$3s0ucosr

# Email Settings
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_app_password_here

# Cloudinary Settings
CLOUDINARY_CLOUD_NAME=dl82v3a1z
CLOUDINARY_API_KEY=***************
CLOUDINARY_API_SECRET=your_api_secret_here
```

### Settings

Key settings in `fileshare/settings.py`:

- **Database Configuration**: Configure your database connection
  - For development (DEBUG=True): SQLite is used
  - For production (DEBUG=False): PostgreSQL is used with environment variables
- **Static and Media Files**: Configure storage for static and uploaded files
- **Authentication Settings**: Configure login/logout URLs and user model
- **Email Configuration**: Configure email settings for notifications

```python
# Email settings
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-app-password'  # Use an app password for Gmail
DEFAULT_FROM_EMAIL = '<EMAIL>'
CONTACT_EMAIL = '<EMAIL>'

# Site URL for emails - different for development and production
if DEBUG:
    SITE_URL = 'http://127.0.0.1:8001'  # Development URL
else:
    SITE_URL = 'https://completoplus.com'  # Production URL
```

**Note**: For Gmail, you'll need to:

1. Enable 2-Step Verification for your Google account
2. Generate an App Password for the application

## User Roles

The application has two main user roles:

### Administrator

- Create and manage projects
- Upload and download files
- Assign projects to clients
- Update project status and progress
- Add progress notes
- View all projects and files
- Access the admin dashboard
- Manage users and system settings

### Client

- View assigned projects
- Upload and download files
- Track project progress
- Receive notifications
- Customize dashboard settings

## Core Functionality

### Authentication

- **Registration**: New users can register as clients
- **Login/Logout**: Secure authentication system
- **Password Management**: Reset and change password functionality

### Dashboard

- **Admin Dashboard**: Comprehensive overview of all projects and files
- **Client Dashboard**: Overview of client's projects and files
- **Customizable Widgets**: Show/hide dashboard components
- **Theme Settings**: Choose between light and dark mode
- **Layout Options**: Customize dashboard layout

### Projects

- **Project Creation**: Create new academic projects
- **Project Assignment**: Assign projects to clients
- **Status Tracking**: Monitor project status (Pending, In Progress, Completed)
- **Progress Tracking**: Track project completion percentage
- **Progress Notes**: Add detailed notes about project progress

### Files

- **File Upload**: Secure file upload with drag-and-drop support
- **File Download**: Download files with access tracking
- **File Preview**: Preview supported file types in the browser
- **File Management**: Organize files by project
- **Batch Operations**: Download or delete multiple files at once

### Notifications

- **Notification Types**: System, file upload, status change, progress update
- **Notification Management**: Mark as read/unread, delete notifications
- **Email Notifications**: Comprehensive email notification system:
  - Welcome emails for new users
  - Password reset emails with secure links
  - Account change notifications
  - Project invitation emails
  - File sharing notifications
  - Progress update notifications with visual separators between content and updates
- **Beautiful Email Templates**: Modern, responsive HTML email templates for all notifications
- **Notification Display**: Notification counter and list
- **Email Tracking**: Admin dashboard for monitoring email sending statistics
- **Email Logging**: Comprehensive logging of all sent emails

## File Management

### Cloudinary Integration

CompletoPLUS uses Cloudinary for file storage in production. To set up Cloudinary:

1. **Create a Cloudinary account** at [cloudinary.com](https://cloudinary.com/)
2. **Get your Cloudinary credentials** from the Dashboard:
   - Cloud Name
   - API Key
   - API Secret
3. **Update your settings.py** with your Cloudinary credentials:

```python
# Cloudinary settings
CLOUDINARY_STORAGE = {
    'CLOUD_NAME': 'your-cloud-name',
    'API_KEY': 'your-api-key',
    'API_SECRET': 'your-api-secret',
}
```

4. **Enable Cloudinary storage** in settings.py:

```python
# Use Cloudinary for media files in production
if not DEBUG:
    DEFAULT_FILE_STORAGE = 'files.storage.CompletoPLUSCloudinaryStorage'
```

5. **Test Cloudinary in development** by uncommenting this line:

```python
# For testing Cloudinary in development
# DEFAULT_FILE_STORAGE = 'files.storage.CompletoPLUSCloudinaryStorage'
```

### Upload Process

1. Select a project
2. Drag and drop files or use the file/folder selector
3. For files: Use the "Select File" button
4. For folders: Use the "Select Folder" button
5. Add optional file/folder name and notes
6. Submit the form
7. Files/folders are securely stored and associated with the project

### Download Process

1. Navigate to the project or file list
2. Click the download button for a file
3. File download is tracked and recorded
4. File is delivered to the user's browser

### File Types

The system supports various file types, including:

- **Documents**: PDF, Word (.doc, .docx), Excel, PowerPoint, Text
- **Images**: JPEG, PNG, GIF, SVG
- **Archives**: ZIP, RAR, 7z, TAR, GZ
- **Audio/Video**: MP3, MP4, WAV
- **Code**: Python, JavaScript, HTML, CSS, JSON, XML

### Automatic File Compression

All files and folders uploaded to the system are automatically compressed into ZIP archives to reduce storage space and provide a consistent download experience:

- **Individual Files**: Any file uploaded is automatically zipped, regardless of its original format
- **Folders**: Entire folders are compressed while preserving the folder structure
- **Original File Names**: The system preserves and displays the original file names alongside the zip files
- **Storage Efficiency**: Compression reduces storage requirements and speeds up file transfers
- **Consistent Experience**: All downloads are provided as ZIP files for a uniform experience

## Data Analysis, Web Development & Machine Learning

CompletoPLUS offers professional services in multiple technical domains:

### Data Analysis

- **Statistical Analysis**: Descriptive and inferential statistics for research data
- **Data Visualization**: Creation of charts, graphs, and visual representations of data
- **Research Support**: Analysis of research data for academic papers and theses
- **Python-Powered**: Utilizing Python's powerful data analysis libraries (Pandas, NumPy, Matplotlib, etc.)
- **Custom Reports**: Generation of custom reports based on analyzed data

### Web Development

- **Custom Web Applications**: Development of tailored web applications for various needs
- **Responsive Design**: Mobile-friendly interfaces that work on all devices
- **Frontend Development**: Modern UI/UX using HTML5, CSS3, JavaScript, and frameworks
- **Backend Development**: Robust server-side solutions using Django and other technologies
- **API Development**: Creation of RESTful APIs for data exchange

### Machine Learning

- **Predictive Models**: Development of AI models for prediction and forecasting
- **Natural Language Processing**: Text analysis and language understanding solutions
- **Computer Vision**: Image and video analysis applications
- **Recommendation Systems**: Personalized recommendation engines
- **Data Mining**: Extraction of patterns and insights from large datasets

## Project Management

### Project Lifecycle

1. **Creation**: Project is created by an admin or client
2. **Assignment**: Project is assigned to a client (if created by admin)
3. **Submission**: Client uploads required files
4. **Processing**: Admin works on the project and updates status
5. **Progress Updates**: Admin adds progress notes and updates completion percentage
6. **Completion**: Project is marked as completed
7. **Delivery**: Final files are uploaded by admin
8. **Download**: Client downloads completed files

### Project Status

- **Pending**: Project has been created but work hasn't started
- **In Progress**: Work on the project has begun
- **Completed**: Project has been finished

## Admin Dashboard

The admin dashboard provides comprehensive management capabilities and is accessible only to superusers via a secure custom URL (/completoplus-admin/). The admin interface features a modern, customized Django admin interface with interactive charts and statistics:

- **Interactive Dashboard**: Visual overview of system statistics with interactive charts
- **Project Overview**: View all projects with status and progress
- **User Management**: Manage client accounts
- **File Management**: View and manage all uploaded files
- **Notification Management**: Manage system notifications
- **Email Monitoring**: Track email sending statistics and logs
- **Statistics**: View system usage statistics with visual charts
- **Export Data**: Export project and file data
- **Testimonial Management**: Approve, reject, and feature client testimonials

## Client Dashboard

The client dashboard provides a user-friendly interface for clients:

- **Project Overview**: View assigned projects with status and progress
- **File Upload/Download**: Manage project files
- **Notifications**: View system notifications
- **Profile Management**: Update profile information
- **Dashboard Settings**: Customize dashboard appearance

## Security Considerations

- **Authentication**: Secure user authentication system
- **Authorization**: Role-based access control
- **Secure Admin Interface**: Custom admin URL (completoplus-admin) with superuser-only access and enhanced security
- **CSRF Protection**: Cross-Site Request Forgery protection
- **XSS Prevention**: Cross-Site Scripting prevention
- **Secure File Handling**: Secure file upload and download
- **Data Validation**: Input validation and sanitization
- **Secure Cookies**: Secure and HTTP-only cookies
- **Content Security Policy**: Restrict resource loading

## Deployment

### Production Considerations

1. **Use a Production Database**: PostgreSQL is recommended
   - Create a `.env` file with your PostgreSQL credentials
   - Set `DEBUG=False` in settings.py to use PostgreSQL
2. **Configure Static Files**: Use a CDN or static file server
   - Run `python manage.py collectstatic` to collect static files
   - Use whitenoise for serving static files in production
3. **Set Up Media Storage**: Use secure storage for uploaded files
   - Configure Cloudinary for media storage in production
4. **Use HTTPS**: Secure all traffic with SSL/TLS
5. **Set Environment Variables**: Move sensitive settings to environment variables
   - Use the `.env` file for local development
   - Set environment variables in your hosting platform for production
6. **Configure Email**: Set up email for notifications
   - For high volume, consider using a dedicated email service like SendGrid or Mailgun
   - Monitor email sending limits (Gmail has a limit of 500 emails per day)
   - Use environment variables for email credentials
7. **Set Debug to False**: Disable debug mode in production

### Deployment to Railway

1. **Push your code to GitHub**:

   ```bash
   git add .
   git commit -m "Prepare for deployment"
   git push origin main
   ```

2. **Connect your GitHub repository to Railway**:

   - Create an account on [Railway](https://railway.app/)
   - Create a new project and connect your GitHub repository
   - Add a PostgreSQL database to your project

3. **Set environment variables in Railway**:

   - `DEBUG=False`
   - `SECRET_KEY=your_secret_key`
   - `EMAIL_HOST_USER=<EMAIL>`
   - `EMAIL_HOST_PASSWORD=your_app_password`
   - `CLOUDINARY_CLOUD_NAME=dl82v3a1z`
   - `CLOUDINARY_API_KEY=***************`
   - `CLOUDINARY_API_SECRET=your_api_secret`

4. **Deploy your application**:
   - Railway will automatically deploy your application
   - Run migrations in the Railway terminal:
     ```bash
     python manage.py migrate
     ```
   - Create a superuser in the Railway terminal:
     ```bash
     python manage.py createsuperuser
     ```

### Other Deployment Options

- **Traditional Hosting**: Deploy to a VPS or dedicated server
- **Platform as a Service**: Deploy to Heroku, PythonAnywhere, etc.
- **Containerization**: Deploy using Docker and Docker Compose
- **Cloud Providers**: Deploy to AWS, Google Cloud, Azure, etc.

## Contributing

Contributions to CompletoPLUS are welcome! Please follow these steps:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Recent Updates

### April 2025 Update

- **Interactive Admin Dashboard**: Implemented a modern admin dashboard with interactive charts and statistics
- **Visual Data Representation**: Added Chart.js integration for visualizing system data
- **Secure Admin Interface**: Implemented a custom admin URL with superuser-only access for enhanced security
- **Comprehensive Testimonial System**: Added a complete testimonial system with ratings, admin approval, and featured testimonials
- **Public Testimonials**: Made testimonials visible to all visitors, including non-registered users
- **Admin Testimonial Notifications**: Admins now receive notifications and emails when new testimonials are submitted
- **Featured Testimonials**: Added ability for admins to mark testimonials as featured to highlight them
- **Testimonial Filtering and Sorting**: Added filtering by service type, rating, and various sorting options
- **Testimonial Dashboard Integration**: Added testimonial management to client dashboard
- **Comprehensive Email Notification System**: Added a complete email notification system with beautiful HTML templates
- **Email Monitoring Dashboard**: Added admin dashboard for tracking email sending statistics and logs
- **Welcome Emails**: New users now receive a personalized welcome email upon registration
- **Account Change Notifications**: Users receive email notifications when their account details are updated
- **Project Invitation Emails**: Clients receive email notifications when they are invited to projects
- **File Sharing Notifications**: Users receive email notifications when files are shared with them
- **Progress Update Emails**: Clients receive email notifications when project progress is updated
- **Automatic File Zipping**: All uploaded files and folders are now automatically compressed to save storage space
- **Data Analysis Services**: Added Python-based data analysis services for research projects
- **Web Development Services**: Added custom web application development services
- **Machine Learning Solutions**: Added AI and machine learning solutions for various applications
- **UI Improvements**: Updated footer design for a more modern look
- **Original File Names**: System now preserves and displays original file names alongside zip files
- **Storage Optimization**: Improved storage efficiency through automatic compression

## Testimonial System

CompletoPLUS includes a comprehensive testimonial system that allows clients to share their experiences and helps build trust with potential clients:

### Key Features

- **Client Testimonials**: Clients can submit testimonials about their experience with CompletoPLUS services
- **Rating System**: 5-star rating system for service quality evaluation
- **Service Type Categorization**: Testimonials are categorized by service type (Assignment, Project, Thesis, etc.)
- **Admin Approval**: All testimonials require admin approval before being published
- **Featured Testimonials**: Admins can mark testimonials as "featured" to highlight them
- **Public Visibility**: Approved testimonials are visible to all visitors, including non-registered users
- **Admin Notifications**: Admins receive notifications and emails when new testimonials are submitted
- **Testimonial Management**: Comprehensive admin interface for managing testimonials
- **Filtering and Sorting**: Visitors can filter testimonials by service type, rating, and sort by various criteria
- **Pagination**: Testimonial listings include pagination for better navigation
- **Client Dashboard Integration**: Clients can view and manage their testimonials from their dashboard

### Testimonial Workflow

1. **Submission**: Client submits a testimonial with rating, service type, and content
2. **Notification**: Admins receive notification and email about the new testimonial
3. **Review**: Admin reviews the testimonial content
4. **Approval/Rejection**: Admin approves or rejects the testimonial
5. **Publication**: Approved testimonials are displayed on the website
6. **Featuring**: Admin can optionally mark testimonials as "featured" to highlight them

### Testimonial Display

- **Homepage**: Featured and highest-rated testimonials are displayed on the homepage
- **Testimonials Page**: Dedicated page for browsing all approved testimonials
- **Client Dashboard**: Clients can see their own testimonials (both approved and pending)
- **Admin Dashboard**: Admins can manage all testimonials through the admin interface

---

© 2025 CompletoPLUS. All rights reserved.
