// Admin Dashboard Charts
document.addEventListener("DOMContentLoaded", function () {
  // Check if dark mode is active
  const isDarkMode =
    document.body.classList.contains("dark-mode") ||
    localStorage.getItem("adminDarkMode") === "true";

  // Define color schemes for light and dark modes
  const colorSchemes = {
    light: {
      primary: "#4361ee",
      secondary: "#3a0ca3",
      purple: "#7209b7",
      pink: "#f72585",
      lightBlue: "#4cc9f0",
      green: "#2dc653",
      orange: "#ff9e00",
      red: "#e63946",
    },
    dark: {
      primary: "#5b7cff", // Brighter blue
      secondary: "#7b4dff", // Brighter purple
      purple: "#9d4edd", // Brighter purple
      pink: "#ff5db1", // Brighter pink
      lightBlue: "#72efff", // Brighter light blue
      green: "#4ade80", // Brighter green
      orange: "#ffb74d", // Brighter orange
      red: "#ff6b6b", // Brighter red
    },
  };

  // Get the appropriate color scheme
  const colors = isDarkMode ? colorSchemes.dark : colorSchemes.light;

  // Project Status Chart
  const projectStatusCtx = document.getElementById("projectStatusChart");
  if (projectStatusCtx) {
    const statusData = JSON.parse(projectStatusCtx.dataset.stats);

    new Chart(projectStatusCtx, {
      type: "doughnut",
      data: {
        labels: Object.keys(statusData).map((key) => {
          // Convert snake_case to Title Case
          return key
            .split("_")
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" ");
        }),
        datasets: [
          {
            data: Object.values(statusData),
            backgroundColor: [
              colors.primary,
              colors.secondary,
              colors.purple,
              colors.pink,
              colors.lightBlue,
            ],
            borderWidth: isDarkMode ? 2 : 1,
            borderColor: isDarkMode ? "#2a2a2a" : "#ffffff",
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: "right",
          },
          tooltip: {
            callbacks: {
              label: function (context) {
                const label = context.label || "";
                const value = context.raw || 0;
                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                const percentage = Math.round((value / total) * 100);
                return `${label}: ${value} (${percentage}%)`;
              },
            },
          },
        },
      },
    });
  }

  // Activity Timeline Chart
  const activityTimelineCtx = document.getElementById("activityTimelineChart");
  if (activityTimelineCtx) {
    const timelineData = JSON.parse(activityTimelineCtx.dataset.stats);

    new Chart(activityTimelineCtx, {
      type: "line",
      data: {
        labels: timelineData.dates,
        datasets: [
          {
            label: "File Uploads",
            data: timelineData.uploads,
            borderColor: colors.primary,
            backgroundColor: isDarkMode
              ? `rgba(${parseInt(colors.primary.slice(1, 3), 16)}, ${parseInt(
                  colors.primary.slice(3, 5),
                  16
                )}, ${parseInt(colors.primary.slice(5, 7), 16)}, 0.3)`
              : "rgba(67, 97, 238, 0.1)",
            borderWidth: isDarkMode ? 3 : 2,
            tension: 0.3,
            fill: true,
          },
          {
            label: "File Downloads",
            data: timelineData.downloads,
            borderColor: colors.pink,
            backgroundColor: isDarkMode
              ? `rgba(${parseInt(colors.pink.slice(1, 3), 16)}, ${parseInt(
                  colors.pink.slice(3, 5),
                  16
                )}, ${parseInt(colors.pink.slice(5, 7), 16)}, 0.3)`
              : "rgba(247, 37, 133, 0.1)",
            borderWidth: isDarkMode ? 3 : 2,
            tension: 0.3,
            fill: true,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              precision: 0,
            },
          },
        },
        interaction: {
          intersect: false,
          mode: "index",
        },
        plugins: {
          legend: {
            position: "top",
          },
          tooltip: {
            callbacks: {
              title: function (context) {
                return context[0].label;
              },
            },
          },
        },
      },
    });
  }

  // User Growth Chart
  const userGrowthCtx = document.getElementById("userGrowthChart");
  if (userGrowthCtx) {
    const growthData = JSON.parse(userGrowthCtx.dataset.stats);

    new Chart(userGrowthCtx, {
      type: "bar",
      data: {
        labels: growthData.months,
        datasets: [
          {
            label: "New Users",
            data: growthData.users,
            backgroundColor: colors.lightBlue,
            borderColor: isDarkMode ? "#2a2a2a" : null,
            borderWidth: isDarkMode ? 1 : 0,
            borderRadius: 4,
            hoverBackgroundColor: colors.secondary,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              precision: 0,
            },
          },
        },
        plugins: {
          legend: {
            display: false,
          },
        },
      },
    });
  }

  // Storage Usage Chart
  const storageUsageCtx = document.getElementById("storageUsageChart");
  if (storageUsageCtx) {
    const storageData = JSON.parse(storageUsageCtx.dataset.stats);

    new Chart(storageUsageCtx, {
      type: "pie",
      data: {
        labels: Object.keys(storageData),
        datasets: [
          {
            data: Object.values(storageData),
            backgroundColor: [
              colors.primary,
              colors.secondary,
              colors.purple,
              colors.pink,
              colors.lightBlue,
            ],
            borderWidth: isDarkMode ? 2 : 1,
            borderColor: isDarkMode ? "#2a2a2a" : "#ffffff",
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: "bottom",
          },
          tooltip: {
            callbacks: {
              label: function (context) {
                const label = context.label || "";
                const value = context.raw || 0;
                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                const percentage = Math.round((value / total) * 100);
                return `${label}: ${value} MB (${percentage}%)`;
              },
            },
          },
        },
      },
    });
  }
});
