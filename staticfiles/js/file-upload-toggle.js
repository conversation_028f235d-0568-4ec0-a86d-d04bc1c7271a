// File upload toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    // File upload form toggle
    const uploadToggle = document.getElementById('upload-toggle');
    const uploadForm = document.getElementById('upload-form');
    
    if (uploadToggle && uploadForm) {
        uploadToggle.addEventListener('click', function() {
            uploadForm.classList.toggle('d-none');
            
            // Change button text based on form visibility
            if (uploadForm.classList.contains('d-none')) {
                this.innerHTML = '<i class="fas fa-upload"></i> Upload Files';
            } else {
                this.innerHTML = '<i class="fas fa-times"></i> Cancel Upload';
            }
        });
    }
});
