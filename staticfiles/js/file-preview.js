// Enhanced file preview functionality
document.addEventListener("DOMContentLoaded", function () {
  // Get all preview buttons
  const previewButtons = document.querySelectorAll(
    ".file-preview-btn, .preview-file"
  );

  // Remove preview buttons from the DOM
  previewButtons.forEach((button) => {
    button.remove();
  });

  // Disabled preview functionality
  /*
  // Add click event to each preview button
  previewButtons.forEach((button) => {
    button.addEventListener("click", function (e) {
      e.preventDefault();

      // Handle both button types
      let fileUrl, fileName, fileType, fileId;

      if (this.classList.contains("preview-file")) {
        // For preview-file buttons, we need to fetch the file data
        fileId = this.getAttribute("data-file-id");
        // Use the preview URL directly
        fileUrl = `/files/preview/${fileId}/`;
        // Get the file name from the closest row or card
        const fileRow = this.closest("tr") || this.closest(".card");
        if (fileRow) {
          const fileNameElement = fileRow
            .querySelector('a[href*="file_download"]')
            .textContent.trim();
          fileName = fileNameElement || `File ${fileId}`;
        } else {
          fileName = `File ${fileId}`;
        }
        fileType = "";
      } else {
        // For file-preview-btn buttons, use the data attributes
        fileUrl = this.getAttribute("data-file-url");
        fileName = this.getAttribute("data-file-name");
        fileType = this.getAttribute("data-file-type");
        fileId = this.getAttribute("data-file-id") || "preview";
      }

      // Create modal for preview
      const modal = document.createElement("div");
      modal.className = "modal fade";
      modal.id = `filePreviewModal-${fileId}`;
      modal.setAttribute("tabindex", "-1");
      modal.setAttribute("aria-labelledby", `filePreviewModalLabel-${fileId}`);
      modal.setAttribute("aria-hidden", "true");

      // Determine content based on file type
      let previewContent = "";
      let modalSize = "modal-lg";

      // Check file extension
      const extension = fileName.split(".").pop().toLowerCase();

      // Set modal size based on content type
      if (["pdf"].includes(extension)) {
        modalSize = "modal-xl"; // Larger for PDFs
      } else if (
        [
          "txt",
          "md",
          "html",
          "css",
          "js",
          "py",
          "java",
          "c",
          "cpp",
          "json",
          "xml",
        ].includes(extension)
      ) {
        modalSize = "modal-lg"; // Large for text files
      } else if (
        ![
          "jpg",
          "jpeg",
          "png",
          "gif",
          "svg",
          "mp4",
          "webm",
          "ogg",
          "mp3",
          "wav",
        ].includes(extension)
      ) {
        modalSize = "modal-md"; // Medium for non-previewable files
      }

      if (["jpg", "jpeg", "png", "gif", "svg", "webp"].includes(extension)) {
        // Image preview with zoom capability
        previewContent = `
                    <div class="image-preview-container text-center">
                        <img src="${fileUrl}" class="img-fluid preview-image" alt="${fileName}" style="max-height: 70vh;">
                        <div class="mt-3 image-controls">
                            <button class="btn btn-sm btn-outline-secondary zoom-in-btn">
                                <i class="fas fa-search-plus"></i> Zoom In
                            </button>
                            <button class="btn btn-sm btn-outline-secondary zoom-out-btn">
                                <i class="fas fa-search-minus"></i> Zoom Out
                            </button>
                            <button class="btn btn-sm btn-outline-secondary reset-zoom-btn">
                                <i class="fas fa-sync"></i> Reset
                            </button>
                        </div>
                    </div>
                `;
      } else if (["pdf"].includes(extension)) {
        // PDF preview with full-screen option
        previewContent = `
                    <div class="pdf-container">
                        <iframe src="${fileUrl}" width="100%" height="600" frameborder="0" class="pdf-viewer"></iframe>
                    </div>
                `;
      } else if (["mp4", "webm", "ogg"].includes(extension)) {
        // Enhanced video preview with controls
        previewContent = `
                    <div class="video-container text-center">
                        <video controls class="w-100" style="max-height: 70vh;">
                            <source src="${fileUrl}" type="video/${extension}">
                            Your browser does not support the video tag.
                        </video>
                    </div>
                `;
      } else if (["mp3", "wav", "ogg"].includes(extension)) {
        // Enhanced audio preview with visualization
        previewContent = `
                    <div class="audio-container text-center">
                        <div class="audio-player-icon mb-3">
                            <i class="fas fa-music fa-4x text-primary"></i>
                        </div>
                        <audio controls class="w-100">
                            <source src="${fileUrl}" type="audio/${extension}">
                            Your browser does not support the audio tag.
                        </audio>
                        <div class="audio-visualization mt-3" id="audio-visualization-${fileId}">
                            <div class="audio-wave"></div>
                        </div>
                    </div>
                `;
      } else if (
        [
          "txt",
          "md",
          "html",
          "css",
          "js",
          "py",
          "java",
          "c",
          "cpp",
          "json",
          "xml",
        ].includes(extension)
      ) {
        // Enhanced text preview with syntax highlighting
        previewContent = `
                    <div class="text-preview-container">
                        <div class="text-preview-header d-flex justify-content-between align-items-center mb-2">
                            <div class="file-info">
                                <span class="badge bg-secondary">${extension.toUpperCase()}</span>
                            </div>
                            <div class="text-controls">
                                <button class="btn btn-sm btn-outline-secondary copy-content-btn">
                                    <i class="fas fa-copy"></i> Copy
                                </button>
                            </div>
                        </div>
                        <div class="text-preview p-3 bg-light rounded" style="max-height: 500px; overflow-y: auto;">
                            <pre><code class="${extension}">Loading content...</code></pre>
                        </div>
                    </div>
                `;
      } else {
        // Enhanced no-preview available with file type icon
        let fileIcon = "fa-file";

        // Determine file icon based on extension
        if (["doc", "docx"].includes(extension)) {
          fileIcon = "fa-file-word";
        } else if (["xls", "xlsx"].includes(extension)) {
          fileIcon = "fa-file-excel";
        } else if (["ppt", "pptx"].includes(extension)) {
          fileIcon = "fa-file-powerpoint";
        } else if (["zip", "rar", "7z", "tar", "gz"].includes(extension)) {
          fileIcon = "fa-file-archive";
        } else if (["csv"].includes(extension)) {
          fileIcon = "fa-file-csv";
        }

        previewContent = `
                    <div class="text-center p-5">
                        <i class="fas ${fileIcon} fa-5x mb-3 text-primary"></i>
                        <h5>${fileName}</h5>
                        <p class="text-muted">${formatFileSize(
                          this.getAttribute("data-file-size") || 0
                        )}</p>
                        <div class="alert alert-info">
                            <p>Preview not available for this file type. Please download the file to view its contents.</p>
                        </div>
                    </div>
                `;
      }

      // Create modal HTML with enhanced UI
      modal.innerHTML = `
                <div class="modal-dialog ${modalSize} modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="filePreviewModalLabel-${fileId}">
                                <i class="fas fa-file me-2"></i> ${fileName}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            ${previewContent}
                        </div>
                        <div class="modal-footer d-flex justify-content-between">
                            <div>
                                <span class="text-muted small">Uploaded: ${
                                  this.getAttribute("data-upload-date") ||
                                  "Unknown"
                                }</span>
                            </div>
                            <div>
                                <a href="${fileUrl}" class="btn btn-primary" download>
                                    <i class="fas fa-download me-2"></i> Download
                                </a>
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

      // Add modal to body
      document.body.appendChild(modal);

      // Initialize and show modal
      const modalInstance = new bootstrap.Modal(modal);
      modalInstance.show();

      // Load text content if it's a text file
      if (
        [
          "txt",
          "md",
          "html",
          "css",
          "js",
          "py",
          "java",
          "c",
          "cpp",
          "json",
          "xml",
        ].includes(extension)
      ) {
        fetch(fileUrl)
          .then((response) => response.text())
          .then((text) => {
            const codeElement = modal.querySelector("code");
            codeElement.textContent = text;

            // Add copy functionality
            const copyBtn = modal.querySelector(".copy-content-btn");
            if (copyBtn) {
              copyBtn.addEventListener("click", function () {
                navigator.clipboard.writeText(text).then(() => {
                  // Show success feedback
                  const originalText = this.innerHTML;
                  this.innerHTML = '<i class="fas fa-check"></i> Copied!';
                  this.classList.remove("btn-outline-secondary");
                  this.classList.add("btn-success");

                  setTimeout(() => {
                    this.innerHTML = originalText;
                    this.classList.remove("btn-success");
                    this.classList.add("btn-outline-secondary");
                  }, 2000);
                });
              });
            }
          })
          .catch((error) => {
            const codeElement = modal.querySelector("code");
            codeElement.textContent = "Error loading file content.";
          });
      }

      // Add image zoom functionality
      if (["jpg", "jpeg", "png", "gif", "svg", "webp"].includes(extension)) {
        const img = modal.querySelector(".preview-image");
        const zoomInBtn = modal.querySelector(".zoom-in-btn");
        const zoomOutBtn = modal.querySelector(".zoom-out-btn");
        const resetZoomBtn = modal.querySelector(".reset-zoom-btn");

        let scale = 1;

        if (zoomInBtn && zoomOutBtn && resetZoomBtn && img) {
          zoomInBtn.addEventListener("click", function () {
            scale *= 1.2;
            img.style.transform = `scale(${scale})`;
          });

          zoomOutBtn.addEventListener("click", function () {
            scale /= 1.2;
            img.style.transform = `scale(${scale})`;
          });

          resetZoomBtn.addEventListener("click", function () {
            scale = 1;
            img.style.transform = `scale(${scale})`;
          });
        }
      }

      // Remove modal from DOM when hidden
      modal.addEventListener("hidden.bs.modal", function () {
        document.body.removeChild(modal);
      });
    });
  });
  // Helper function to format file size
  function formatFileSize(bytes) {
    if (bytes === 0 || bytes === "0" || !bytes) return "0 Bytes";

    bytes = Number(bytes);
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));

    return parseFloat((bytes / Math.pow(1024, i)).toFixed(2)) + " " + sizes[i];
  }
  */
});
