// Admin Dashboard Widgets and Dark Mode
document.addEventListener("DOMContentLoaded", function () {
  // Dark Mode Toggle
  const body = document.body;
  const darkModeToggle = document.createElement("button");
  darkModeToggle.className = "dark-mode-toggle";
  darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
  darkModeToggle.setAttribute("title", "Toggle Dark Mode");
  document.body.appendChild(darkModeToggle);

  // Check for saved dark mode preference
  const isDarkMode = localStorage.getItem("adminDarkMode") === "true";
  if (isDarkMode) {
    body.classList.add("dark-mode");
    darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
  }

  // Toggle dark mode
  darkModeToggle.addEventListener("click", function () {
    body.classList.toggle("dark-mode");
    const isDark = body.classList.contains("dark-mode");
    localStorage.setItem("adminDarkMode", isDark);

    if (isDark) {
      darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
    } else {
      darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
    }

    // Update charts if they exist
    if (window.Chart) {
      Chart.instances.forEach((chart) => {
        if (isDark) {
          // Improve chart text contrast in dark mode
          chart.options.plugins.legend.labels.color = "#ffffff";
          chart.options.scales.x.ticks.color = "#ffffff";
          chart.options.scales.y.ticks.color = "#ffffff";
          chart.options.scales.x.grid.color = "rgba(255, 255, 255, 0.2)";
          chart.options.scales.y.grid.color = "rgba(255, 255, 255, 0.2)";

          // Make chart title more visible
          if (chart.options.plugins.title) {
            chart.options.plugins.title.color = "#ffffff";
          }

          // Enhance tooltip visibility
          if (chart.options.plugins.tooltip) {
            chart.options.plugins.tooltip.backgroundColor =
              "rgba(30, 30, 30, 0.9)";
            chart.options.plugins.tooltip.titleColor = "#ffffff";
            chart.options.plugins.tooltip.bodyColor = "#ffffff";
            chart.options.plugins.tooltip.borderColor = "#4cc9f0";
            chart.options.plugins.tooltip.borderWidth = 1;
          }

          // Brighten dataset colors for better visibility
          if (chart.config.type === "doughnut" || chart.config.type === "pie") {
            // Increase opacity and brightness for pie/doughnut charts
            chart.data.datasets.forEach((dataset) => {
              if (dataset.backgroundColor) {
                // Make colors more vibrant
                dataset.backgroundColor = dataset.backgroundColor.map(
                  (color) => {
                    if (typeof color === "string" && color.startsWith("#")) {
                      return color; // Keep hex colors as is
                    } else if (
                      typeof color === "string" &&
                      color.startsWith("rgba")
                    ) {
                      // Increase opacity for rgba colors
                      return color.replace(
                        /rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d\.]+\)/,
                        "rgba($1, $2, $3, 0.9)"
                      );
                    }
                    return color;
                  }
                );
              }
            });
          }
        } else {
          // Light mode settings
          chart.options.plugins.legend.labels.color = "#333";
          chart.options.scales.x.ticks.color = "#333";
          chart.options.scales.y.ticks.color = "#333";
          chart.options.scales.x.grid.color = "rgba(0, 0, 0, 0.1)";
          chart.options.scales.y.grid.color = "rgba(0, 0, 0, 0.1)";

          if (chart.options.plugins.title) {
            chart.options.plugins.title.color = "#333";
          }

          if (chart.options.plugins.tooltip) {
            chart.options.plugins.tooltip.backgroundColor =
              "rgba(255, 255, 255, 0.9)";
            chart.options.plugins.tooltip.titleColor = "#333";
            chart.options.plugins.tooltip.bodyColor = "#333";
            chart.options.plugins.tooltip.borderColor = "#4361ee";
            chart.options.plugins.tooltip.borderWidth = 1;
          }
        }
        chart.update();
      });
    }
  });

  // Widget Layout Controls
  const widgetControls = document.querySelectorAll(".widget-control");
  const contentMain = document.getElementById("content-main");

  // Check for saved layout preference
  const savedLayout = localStorage.getItem("adminLayout") || "grid";
  setLayout(savedLayout);

  // Activate the correct button
  widgetControls.forEach((control) => {
    if (control.dataset.layout === savedLayout) {
      control.classList.add("active");
    } else {
      control.classList.remove("active");
    }

    control.addEventListener("click", function () {
      const layout = this.dataset.layout;

      // Update active state
      widgetControls.forEach((btn) => btn.classList.remove("active"));
      this.classList.add("active");

      // Set layout
      setLayout(layout);

      // Save preference
      localStorage.setItem("adminLayout", layout);
    });
  });

  function setLayout(layout) {
    // Remove existing layout classes
    contentMain.classList.remove(
      "layout-grid",
      "layout-list",
      "layout-compact"
    );

    // Add new layout class
    contentMain.classList.add(`layout-${layout}`);

    // Apply specific layout adjustments
    if (layout === "compact") {
      document.querySelectorAll(".chart-container").forEach((container) => {
        container.style.height = "200px";
      });
    } else if (layout === "list") {
      document.querySelectorAll(".chart-container").forEach((container) => {
        container.style.height = "300px";
      });
    } else {
      document.querySelectorAll(".chart-container").forEach((container) => {
        container.style.height = "250px";
      });
    }

    // Update any charts to fit new container sizes
    if (window.Chart) {
      setTimeout(() => {
        Chart.instances.forEach((chart) => chart.resize());
      }, 100);
    }
  }

  // Add custom bulk actions
  const actionSelect = document.querySelector('select[name="action"]');
  if (actionSelect) {
    // Add export options if not already present
    if (!document.querySelector('option[value="export_csv"]')) {
      const exportOption = document.createElement("option");
      exportOption.value = "export_csv";
      exportOption.textContent = "Export selected items to CSV";
      actionSelect.appendChild(exportOption);
    }

    if (!document.querySelector('option[value="export_json"]')) {
      const exportJsonOption = document.createElement("option");
      exportJsonOption.value = "export_json";
      exportJsonOption.textContent = "Export selected items to JSON";
      actionSelect.appendChild(exportJsonOption);
    }
  }

  // Add file preview enhancements
  document
    .querySelectorAll(
      'a[href$=".jpg"], a[href$=".jpeg"], a[href$=".png"], a[href$=".gif"]'
    )
    .forEach((link) => {
      if (link.textContent.includes("View File") || link.querySelector("img")) {
        link.addEventListener("click", function (e) {
          e.preventDefault();

          const overlay = document.createElement("div");
          overlay.style.position = "fixed";
          overlay.style.top = "0";
          overlay.style.left = "0";
          overlay.style.width = "100%";
          overlay.style.height = "100%";
          overlay.style.backgroundColor = "rgba(0, 0, 0, 0.8)";
          overlay.style.zIndex = "9999";
          overlay.style.display = "flex";
          overlay.style.alignItems = "center";
          overlay.style.justifyContent = "center";

          const img = document.createElement("img");
          img.src = this.href;
          img.style.maxWidth = "90%";
          img.style.maxHeight = "90%";
          img.style.boxShadow = "0 0 20px rgba(0, 0, 0, 0.5)";

          const closeBtn = document.createElement("button");
          closeBtn.innerHTML = '<i class="fas fa-times"></i>';
          closeBtn.style.position = "absolute";
          closeBtn.style.top = "20px";
          closeBtn.style.right = "20px";
          closeBtn.style.background = "none";
          closeBtn.style.border = "none";
          closeBtn.style.color = "white";
          closeBtn.style.fontSize = "24px";
          closeBtn.style.cursor = "pointer";

          overlay.appendChild(img);
          overlay.appendChild(closeBtn);
          document.body.appendChild(overlay);

          closeBtn.addEventListener("click", function () {
            document.body.removeChild(overlay);
          });

          overlay.addEventListener("click", function (e) {
            if (e.target === overlay) {
              document.body.removeChild(overlay);
            }
          });
        });
      }
    });
});
