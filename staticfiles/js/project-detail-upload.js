/**
 * Project Detail Page File Upload
 *
 * Features:
 * - Drag and drop file upload in project detail page
 * - Multiple file selection
 * - File preview
 * - Progress tracking
 * - Client-side zipping
 */

document.addEventListener("DOMContentLoaded", function () {
  // Elements
  const uploadToggleButton = document.getElementById("upload-toggle-button");
  const uploadForm = document.getElementById("upload-form");
  const cancelUploadButton = document.getElementById("cancel-upload");
  const dropzone = document.getElementById("dropzone");
  const fileInput = document.getElementById("id_file");
  const fileNameInput = document.getElementById("id_file_name");
  const uploadButton = document.getElementById("upload-button");
  const uploadProgress = document.getElementById("upload-progress");
  const progressBar = uploadProgress
    ? uploadProgress.querySelector(".progress-bar")
    : null;
  const progressPercentage = document.getElementById("progress-percentage");
  const emptyUploadButton = document.getElementById("empty-upload-button");

  // File collection
  let selectedFiles = [];

  // Toggle upload form
  if (uploadToggleButton && uploadForm) {
    uploadToggleButton.addEventListener("click", function () {
      uploadForm.classList.toggle("d-none");
      if (!uploadForm.classList.contains("d-none")) {
        // Scroll to the form
        uploadForm.scrollIntoView({ behavior: "smooth" });
      }
    });
  }

  // Empty upload button
  if (emptyUploadButton) {
    emptyUploadButton.addEventListener("click", function () {
      if (uploadToggleButton) {
        uploadToggleButton.click();
      }
    });
  }

  // Cancel upload
  if (cancelUploadButton) {
    cancelUploadButton.addEventListener("click", function () {
      uploadForm.classList.add("d-none");
      // Clear selected files
      selectedFiles = [];
      if (fileInput) fileInput.value = "";
      if (fileNameInput) fileNameInput.value = "";
      updateFileList();
      updateUploadButton();
    });
  }

  // Drag and drop functionality
  if (dropzone && fileInput) {
    // Handle drag and drop events
    ["dragenter", "dragover", "dragleave", "drop"].forEach((eventName) => {
      dropzone.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Add visual feedback for drag events
    ["dragenter", "dragover"].forEach((eventName) => {
      dropzone.addEventListener(eventName, highlight, false);
    });

    ["dragleave", "drop"].forEach((eventName) => {
      dropzone.addEventListener(eventName, unhighlight, false);
    });

    function highlight() {
      dropzone.classList.add("drag-highlight");
    }

    function unhighlight() {
      dropzone.classList.remove("drag-highlight");
    }

    // Handle dropped files
    dropzone.addEventListener("drop", handleDrop, false);

    function handleDrop(e) {
      const dt = e.dataTransfer;
      const files = dt.files;

      handleFiles(files);
    }

    // Handle file input change
    fileInput.addEventListener("change", function () {
      handleFiles(this.files);
    });

    // Handle files
    function handleFiles(files) {
      if (!files || files.length === 0) return;

      // Check total size
      let totalSize = 0;
      for (let i = 0; i < files.length; i++) {
        totalSize += files[i].size;
      }

      if (totalSize > 104857600) {
        // 100MB
        showError("Total file size must be under 100MB");
        return;
      }

      // Add files to collection
      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        // Check if file already exists in the collection
        const fileExists = selectedFiles.some(
          (f) =>
            f.name === file.name &&
            f.size === file.size &&
            f.lastModified === file.lastModified
        );

        if (!fileExists) {
          selectedFiles.push(file);
        }
      }

      // Update file list
      updateFileList();

      // Update file name if not set
      if (fileNameInput && !fileNameInput.value.trim()) {
        if (selectedFiles.length === 1) {
          fileNameInput.value = selectedFiles[0].name.replace(/\.[^/.]+$/, "");
        } else {
          fileNameInput.value = "Multiple Files";
        }
      }

      // Update upload button
      updateUploadButton();
    }
  }

  // Create file list container if it doesn't exist
  function createFileListContainer() {
    if (document.getElementById("file-list-container")) return;

    const container = document.createElement("div");
    container.id = "file-list-container";
    container.className = "mt-3 border rounded";
    container.style.maxHeight = "200px";
    container.style.overflowY = "auto";

    const header = document.createElement("div");
    header.className =
      "p-2 bg-light border-bottom d-flex justify-content-between align-items-center";
    header.innerHTML = `
            <h6 class="mb-0">Selected Files</h6>
            <button type="button" class="btn btn-sm btn-outline-danger" id="clear-files">
                <i class="fas fa-trash-alt me-1"></i>Clear All
            </button>
        `;

    const fileList = document.createElement("div");
    fileList.id = "file-list";

    container.appendChild(header);
    container.appendChild(fileList);

    // Insert after dropzone
    if (dropzone) {
      dropzone.parentNode.insertBefore(container, dropzone.nextSibling);

      // Add event listener to clear files button
      document
        .getElementById("clear-files")
        .addEventListener("click", function () {
          selectedFiles = [];
          updateFileList();
          updateUploadButton();
        });
    }
  }

  // Update file list
  function updateFileList() {
    // Create container if it doesn't exist
    if (
      selectedFiles.length > 0 &&
      !document.getElementById("file-list-container")
    ) {
      createFileListContainer();
    }

    const fileListContainer = document.getElementById("file-list-container");
    const fileList = document.getElementById("file-list");

    if (!fileList) return;

    // Clear file list
    fileList.innerHTML = "";

    // Show/hide container
    if (fileListContainer) {
      fileListContainer.style.display =
        selectedFiles.length > 0 ? "block" : "none";
    }

    // Add files to list
    selectedFiles.forEach((file, index) => {
      const fileItem = document.createElement("div");
      fileItem.className =
        "file-item d-flex align-items-center p-2 border-bottom";

      // File icon based on type
      let fileIcon = "fa-file";
      if (file.type.includes("image")) {
        fileIcon = "fa-file-image text-info";
      } else if (file.type.includes("pdf")) {
        fileIcon = "fa-file-pdf text-danger";
      } else if (
        file.type.includes("word") ||
        file.name.endsWith(".doc") ||
        file.name.endsWith(".docx")
      ) {
        fileIcon = "fa-file-word text-primary";
      } else if (
        file.type.includes("excel") ||
        file.name.endsWith(".xls") ||
        file.name.endsWith(".xlsx")
      ) {
        fileIcon = "fa-file-excel text-success";
      } else if (
        file.type.includes("zip") ||
        file.name.endsWith(".zip") ||
        file.name.endsWith(".rar")
      ) {
        fileIcon = "fa-file-archive text-warning";
      } else if (file.type.includes("video")) {
        fileIcon = "fa-file-video text-danger";
      } else if (file.type.includes("audio")) {
        fileIcon = "fa-file-audio text-warning";
      } else if (file.type.includes("text")) {
        fileIcon = "fa-file-alt text-secondary";
      }

      // Format file size
      const fileSize = formatFileSize(file.size);

      fileItem.innerHTML = `
                <div class="me-2"><i class="fas ${fileIcon}"></i></div>
                <div class="flex-grow-1">
                    <div class="fw-bold">${file.name}</div>
                    <div class="small text-muted">${fileSize}</div>
                </div>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-file" data-index="${index}">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

      fileList.appendChild(fileItem);
    });

    // Add event listeners to remove buttons
    document.querySelectorAll(".remove-file").forEach((button) => {
      button.addEventListener("click", function () {
        const index = parseInt(this.dataset.index);
        selectedFiles.splice(index, 1);
        updateFileList();
        updateUploadButton();
      });
    });
  }

  // Update upload button
  function updateUploadButton() {
    if (!uploadButton) return;

    if (selectedFiles.length > 0) {
      uploadButton.disabled = false;
      uploadButton.innerHTML = `<i class="fas fa-upload me-2"></i>Upload ${
        selectedFiles.length
      } ${selectedFiles.length === 1 ? "File" : "Files"}`;
    } else {
      uploadButton.disabled = true;
      uploadButton.innerHTML = '<i class="fas fa-upload me-2"></i>Upload Files';
    }
  }

  // Format file size
  function formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  // Show error
  function showError(message) {
    alert(message);
  }

  // Reset progress
  function resetProgress() {
    if (uploadProgress) {
      uploadProgress.classList.add("d-none");
      if (progressBar) {
        progressBar.style.width = "0%";
        progressBar.setAttribute("aria-valuenow", 0);
      }
      if (progressPercentage) {
        progressPercentage.textContent = "0%";
      }
    }
  }

  // Show progress
  function showProgress(percent) {
    if (uploadProgress) {
      uploadProgress.classList.remove("d-none");
      if (progressBar) {
        progressBar.style.width = percent + "%";
        progressBar.setAttribute("aria-valuenow", percent);
      }
      if (progressPercentage) {
        progressPercentage.textContent = percent + "%";
      }
    }
  }

  // Handle form submission
  if (uploadForm) {
    uploadForm.addEventListener("submit", function (e) {
      e.preventDefault();

      if (selectedFiles.length === 0) {
        showError("Please select at least one file to upload");
        return;
      }

      // Show progress indicator
      resetProgress();
      showProgress(5);

      // Create form data
      const formData = new FormData(uploadForm);

      // Remove any existing file inputs
      formData.delete("file");

      // Set the file name if not already set
      if (!formData.has("file_name") || formData.get("file_name") === "") {
        if (selectedFiles.length === 1) {
          formData.set(
            "file_name",
            selectedFiles[0].name.replace(/\.[^/.]+$/, "")
          );
        } else {
          formData.set("file_name", "Multiple Files");
        }
      }

      // Always create a zip file for all uploads
      // Set the is_zipped flag to true
      formData.set("is_zipped", "true");

      // Load JSZip if not already loaded
      if (typeof JSZip === "undefined") {
        const script = document.createElement("script");
        script.src =
          "https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js";
        script.onload = () => createAndSendZip();
        document.head.appendChild(script);
      } else {
        createAndSendZip();
      }

      // Create and send zip file
      async function createAndSendZip() {
        try {
          showProgress(10);

          // Create a new zip file
          const zip = new JSZip();

          // Add files to zip
          for (let i = 0; i < selectedFiles.length; i++) {
            const file = selectedFiles[i];
            const fileData = await readFileAsync(file);
            zip.file(file.name, fileData);

            // Update progress
            showProgress(10 + Math.round((i / selectedFiles.length) * 40));
          }

          // Generate zip file
          showProgress(50);
          const zipBlob = await zip.generateAsync({
            type: "blob",
            compression: "DEFLATE",
          });

          // Create a File object from the Blob
          const zipFileName = formData.get("file_name") || "files";
          const zipFile = new File([zipBlob], zipFileName + ".zip", {
            type: "application/zip",
          });

          // Add the zip file to the form data
          formData.append("file", zipFile);
          formData.set("is_zipped", "true");

          // Send the form data
          showProgress(60);
          sendFormData(formData);
        } catch (error) {
          console.error("Error creating zip file:", error);
          showError("Error creating zip file. Please try again.");
          resetProgress();
        }
      }

      // Helper function to read a file as ArrayBuffer
      function readFileAsync(file) {
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result);
          reader.onerror = reject;
          reader.readAsArrayBuffer(file);
        });
      }

      // Send form data
      function sendFormData(formData) {
        // Create AJAX request
        const xhr = new XMLHttpRequest();
        xhr.open("POST", uploadForm.action, true);
        xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");

        // Track upload progress
        xhr.upload.onprogress = function (e) {
          if (e.lengthComputable) {
            const percentComplete = 60 + Math.round((e.loaded / e.total) * 30);
            showProgress(percentComplete);
          }
        };

        // Handle response
        xhr.onload = function () {
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText);

              if (response.status === "success") {
                showProgress(100);

                // Reload after a short delay
                setTimeout(() => {
                  if (response.redirect) {
                    window.location.href = response.redirect;
                  } else {
                    window.location.reload();
                  }
                }, 1000);
              } else {
                showError(
                  response.message || "Upload failed. Please try again."
                );
                resetProgress();
              }
            } catch (e) {
              // If the response is not JSON, assume it's a redirect
              showProgress(100);
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            }
          } else {
            try {
              const response = JSON.parse(xhr.responseText);
              showError(response.message || "Upload failed. Please try again.");
            } catch (e) {
              showError("Upload failed. Please try again.");
            }
            resetProgress();
          }
        };

        xhr.onerror = function () {
          showError("Network error. Please try again.");
          resetProgress();
        };

        xhr.send(formData);
      }
    });
  }
});
