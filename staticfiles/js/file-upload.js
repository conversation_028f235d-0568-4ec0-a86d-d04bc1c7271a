// JavaScript for enhancing file upload experience

document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('id_file');
    const fileNameInput = document.getElementById('id_file_name');
    const uploadArea = document.querySelector('.file-upload-area');
    
    if (fileInput && uploadArea) {
        // Handle drag and drop events
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        // Add visual feedback for drag events
        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, unhighlight, false);
        });
        
        function highlight() {
            uploadArea.classList.add('border-primary');
            uploadArea.classList.add('bg-light');
        }
        
        function unhighlight() {
            uploadArea.classList.remove('border-primary');
            uploadArea.classList.remove('bg-light');
        }
        
        // Handle dropped files
        uploadArea.addEventListener('drop', handleDrop, false);
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            fileInput.files = files;
            
            // Update file name input if it's empty
            if (fileNameInput && fileNameInput.value === '' && files.length > 0) {
                // Remove file extension for cleaner display
                const fileName = files[0].name.replace(/\.[^/.]+$/, "");
                fileNameInput.value = fileName;
            }
            
            // Show file info
            updateFileInfo(files[0]);
        }
        
        // Also handle regular file input change
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                // Update file name input if it's empty
                if (fileNameInput && fileNameInput.value === '') {
                    // Remove file extension for cleaner display
                    const fileName = this.files[0].name.replace(/\.[^/.]+$/, "");
                    fileNameInput.value = fileName;
                }
                
                // Show file info
                updateFileInfo(this.files[0]);
            }
        });
        
        // Function to display file information
        function updateFileInfo(file) {
            // Create or update file info display
            let fileInfoDiv = document.getElementById('file-info');
            if (!fileInfoDiv) {
                fileInfoDiv = document.createElement('div');
                fileInfoDiv.id = 'file-info';
                fileInfoDiv.className = 'alert alert-info mt-3';
                uploadArea.parentNode.insertBefore(fileInfoDiv, uploadArea.nextSibling);
            }
            
            // Format file size
            const size = formatFileSize(file.size);
            
            // Get file type
            const fileType = getFileType(file.name);
            
            // Update info
            fileInfoDiv.innerHTML = `
                <strong>Selected File:</strong> ${file.name}<br>
                <strong>Type:</strong> ${fileType}<br>
                <strong>Size:</strong> ${size}
            `;
        }
        
        // Helper function to format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // Helper function to get file type
        function getFileType(filename) {
            const ext = filename.split('.').pop().toLowerCase();
            
            const fileTypes = {
                // Documents
                'pdf': 'PDF Document',
                'doc': 'Word Document',
                'docx': 'Word Document',
                'xls': 'Excel Spreadsheet',
                'xlsx': 'Excel Spreadsheet',
                'ppt': 'PowerPoint Presentation',
                'pptx': 'PowerPoint Presentation',
                'txt': 'Text Document',
                
                // Images
                'jpg': 'JPEG Image',
                'jpeg': 'JPEG Image',
                'png': 'PNG Image',
                'gif': 'GIF Image',
                'svg': 'SVG Image',
                
                // Archives
                'zip': 'ZIP Archive',
                'rar': 'RAR Archive',
                '7z': '7-Zip Archive',
                'tar': 'TAR Archive',
                'gz': 'GZip Archive',
                
                // Audio/Video
                'mp3': 'MP3 Audio',
                'mp4': 'MP4 Video',
                'wav': 'WAV Audio',
                
                // Code
                'py': 'Python File',
                'js': 'JavaScript File',
                'html': 'HTML File',
                'css': 'CSS File',
                'json': 'JSON File',
                'xml': 'XML File'
            };
            
            return fileTypes[ext] || `${ext.toUpperCase()} File`;
        }
    }
});
