// Search functionality for projects and files
document.addEventListener('DOMContentLoaded', function() {
    // Project search
    const projectSearchInput = document.getElementById('projectSearch');
    const projectTable = document.getElementById('projectTable');
    
    if (projectSearchInput && projectTable) {
        projectSearchInput.addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = projectTable.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const projectName = row.querySelector('td:first-child').textContent.toLowerCase();
                const clientName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                const status = row.querySelector('td:nth-child(3) .badge').textContent.toLowerCase();
                
                if (projectName.includes(searchTerm) || clientName.includes(searchTerm) || status.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
            
            // Show message if no results
            const visibleRows = projectTable.querySelectorAll('tbody tr:not([style*="display: none"])');
            const noResultsMessage = document.getElementById('noProjectResults');
            
            if (visibleRows.length === 0 && searchTerm !== '') {
                if (!noResultsMessage) {
                    const message = document.createElement('div');
                    message.id = 'noProjectResults';
                    message.className = 'alert alert-info mt-3';
                    message.innerHTML = `No projects found matching "<strong>${searchTerm}</strong>". Try a different search term.`;
                    projectTable.parentNode.appendChild(message);
                } else {
                    noResultsMessage.innerHTML = `No projects found matching "<strong>${searchTerm}</strong>". Try a different search term.`;
                    noResultsMessage.style.display = 'block';
                }
            } else if (noResultsMessage) {
                noResultsMessage.style.display = 'none';
            }
        });
    }
    
    // File search
    const fileSearchInput = document.getElementById('fileSearch');
    const fileTables = document.querySelectorAll('.file-table');
    
    if (fileSearchInput && fileTables.length > 0) {
        fileSearchInput.addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            
            fileTables.forEach(table => {
                const rows = table.querySelectorAll('tbody tr');
                
                rows.forEach(row => {
                    const fileName = row.querySelector('td:first-child').textContent.toLowerCase();
                    const uploadedBy = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                    const fileType = row.querySelector('td:nth-child(3) .badge') ? 
                                    row.querySelector('td:nth-child(3) .badge').textContent.toLowerCase() : '';
                    const notes = row.querySelector('td:nth-child(5)').textContent.toLowerCase();
                    
                    if (fileName.includes(searchTerm) || 
                        uploadedBy.includes(searchTerm) || 
                        fileType.includes(searchTerm) || 
                        notes.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
                
                // Show message if no results
                const visibleRows = table.querySelectorAll('tbody tr:not([style*="display: none"])');
                const tabPane = table.closest('.tab-pane');
                let noResultsMessage = tabPane.querySelector('.no-file-results');
                
                if (visibleRows.length === 0 && searchTerm !== '') {
                    if (!noResultsMessage) {
                        const message = document.createElement('div');
                        message.className = 'alert alert-info mt-3 no-file-results';
                        message.innerHTML = `No files found matching "<strong>${searchTerm}</strong>". Try a different search term.`;
                        table.parentNode.appendChild(message);
                    } else {
                        noResultsMessage.innerHTML = `No files found matching "<strong>${searchTerm}</strong>". Try a different search term.`;
                        noResultsMessage.style.display = 'block';
                    }
                } else if (noResultsMessage) {
                    noResultsMessage.style.display = 'none';
                }
            });
        });
    }
});
