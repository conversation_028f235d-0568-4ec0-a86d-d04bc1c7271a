// Custom JavaScript for File Sharing Platform

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });
    
    // Auto-dismiss alerts after 5 seconds
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
    
    // File input enhancement
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(function(input) {
        input.addEventListener('change', function(e) {
            const fileName = e.target.files[0]?.name;
            const fileNameInput = document.querySelector('#id_file_name');
            
            // If there's a file name input and it's empty, fill it with the file name
            if (fileNameInput && fileNameInput.value === '') {
                fileNameInput.value = fileName;
            }
        });
    });
    
    // Status filter functionality
    const statusFilter = document.querySelector('#status');
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            this.form.submit();
        });
    }
    
    // Project search functionality
    const searchForm = document.querySelector('form[role="search"]');
    if (searchForm) {
        const searchInput = searchForm.querySelector('input[name="search"]');
        const clearButton = searchForm.querySelector('.btn-close');
        
        if (clearButton) {
            clearButton.addEventListener('click', function() {
                searchInput.value = '';
                searchForm.submit();
            });
        }
    }
    
    // File tabs persistence
    const filesTabs = document.querySelector('#filesTabs');
    if (filesTabs) {
        const tabLinks = filesTabs.querySelectorAll('.nav-link');
        
        // Get active tab from localStorage
        const activeTab = localStorage.getItem('activeFileTab');
        if (activeTab) {
            const tabToActivate = document.querySelector(activeTab);
            if (tabToActivate) {
                const tab = new bootstrap.Tab(tabToActivate);
                tab.show();
            }
        }
        
        // Save active tab to localStorage
        tabLinks.forEach(function(tabLink) {
            tabLink.addEventListener('shown.bs.tab', function(e) {
                localStorage.setItem('activeFileTab', '#' + e.target.id);
            });
        });
    }
    
    // Notification mark as read
    const markReadButtons = document.querySelectorAll('.mark-as-read');
    markReadButtons.forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const url = this.getAttribute('href');
            const notificationItem = this.closest('.list-group-item');
            
            fetch(url, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (response.ok) {
                    notificationItem.classList.remove('list-group-item-primary');
                    this.remove();
                    
                    // Update notification badge count
                    const badge = document.querySelector('.notification-badge');
                    if (badge) {
                        const count = parseInt(badge.textContent) - 1;
                        if (count <= 0) {
                            badge.remove();
                        } else {
                            badge.textContent = count;
                        }
                    }
                }
            });
        });
    });
    
    // Helper function to get CSRF token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
});
