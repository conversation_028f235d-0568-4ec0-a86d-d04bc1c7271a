// Activity chart for dashboard
document.addEventListener('DOMContentLoaded', function() {
    const activityChartCanvas = document.getElementById('activityChart');
    
    if (activityChartCanvas) {
        // Get activity data from the data attribute
        const activityData = JSON.parse(activityChartCanvas.getAttribute('data-activity'));
        
        // Prepare data for chart
        const labels = activityData.map(item => item.date);
        const uploads = activityData.map(item => item.uploads);
        const downloads = activityData.map(item => item.downloads);
        
        // Create chart
        const ctx = activityChartCanvas.getContext('2d');
        const activityChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Uploads',
                        data: uploads,
                        backgroundColor: 'rgba(67, 97, 238, 0.2)',
                        borderColor: 'rgba(67, 97, 238, 1)',
                        borderWidth: 2,
                        tension: 0.3,
                        pointBackgroundColor: 'rgba(67, 97, 238, 1)',
                        pointRadius: 4
                    },
                    {
                        label: 'Downloads',
                        data: downloads,
                        backgroundColor: 'rgba(6, 214, 160, 0.2)',
                        borderColor: 'rgba(6, 214, 160, 1)',
                        borderWidth: 2,
                        tension: 0.3,
                        pointBackgroundColor: 'rgba(6, 214, 160, 1)',
                        pointRadius: 4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
    }
});
