/* CompletoPLUS Admin Login Styles - Modern UI */

:root {
  /* Modern color palette */
  --primary-color: #4361ee;
  --primary-dark: #3a56d4;
  --secondary-color: #3a0ca3;
  --accent-color: #4cc9f0;
  --success-color: #06d6a0;
  --warning-color: #ffd166;
  --danger-color: #ef476f;
  --light-color: #f8f9fa;
  --dark-color: #212529;

  /* Neutral colors */
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;

  /* UI elements */
  --border-radius: 12px;
  --box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
  --input-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  --transition-speed: 0.3s;
}

/* Theme toggle styles */
#user-tools {
  position: absolute;
  top: 15px;
  right: 40px;
  z-index: 10000;
  display: flex;
  align-items: center;
  color: var(--header-link-color, #fff);
}

/* THEME SWITCH */
.theme-toggle {
  cursor: pointer;
  border: none;
  padding: 0;
  background: transparent;
  vertical-align: middle;
  margin-inline-start: 5px;
  margin-top: -1px;
}

.theme-toggle svg {
  vertical-align: middle;
  height: 1.5rem;
  width: 1.5rem;
  display: none;
}

/*
Fully hide screen reader text so we only show the one matching the current
theme.
*/
.theme-toggle .visually-hidden {
  display: none;
}

html[data-theme="auto"] .theme-toggle .theme-label-when-auto {
  display: block;
}

html[data-theme="dark"] .theme-toggle .theme-label-when-dark {
  display: block;
}

html[data-theme="light"] .theme-toggle .theme-label-when-light {
  display: block;
}

/* ICONS */
.theme-toggle svg.theme-icon-when-auto,
.theme-toggle svg.theme-icon-when-dark,
.theme-toggle svg.theme-icon-when-light {
  fill: var(--header-link-color, #fff);
  color: var(--header-bg, #417690);
}

html[data-theme="auto"] .theme-toggle svg.theme-icon-when-auto {
  display: block;
}

html[data-theme="dark"] .theme-toggle svg.theme-icon-when-dark {
  display: block;
}

html[data-theme="light"] .theme-toggle svg.theme-icon-when-light {
  display: block;
}

/* Animated background */
body.login {
  background-color: #f5f7ff;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  margin: 0;
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}

/* Django admin header */
#header {
  width: 100%;
  height: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 40px;
  box-sizing: border-box;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  background-color: var(--header-bg, #417690);
  color: var(--header-link-color, #fff);
}

#branding h1 {
  margin: 0;
  font-weight: 300;
  font-size: 1.5rem;
  color: var(--header-branding-color, #f5dd5d);
}

#branding h1 a {
  color: var(--header-branding-color, #f5dd5d);
  text-decoration: none;
}

body.login::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(67, 97, 238, 0.05) 0%,
    rgba(58, 12, 163, 0.05) 100%
  );
  transform: rotate(-45deg);
  z-index: -1;
}

.login-container {
  width: 100%;
  max-width: 420px;
  position: relative;
  z-index: 1;
  margin: 0 auto; /* Center horizontally */
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.login-box {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 40px;
  animation: slideUp 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  border: 1px solid rgba(0, 0, 0, 0.03);
  width: 100%; /* Ensure it takes full width of container */
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-logo {
  text-align: center;
  margin-bottom: 30px;
}

.login-logo i {
  font-size: 2.8rem;
  margin-bottom: 15px;
  color: var(--primary-color);
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.login-logo h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--dark-color);
  margin: 0;
  letter-spacing: -0.5px;
}

.login-welcome {
  text-align: center;
  margin-bottom: 35px;
}

.login-welcome h2 {
  font-size: 1.6rem;
  font-weight: 700;
  color: var(--dark-color);
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;
}

.login-welcome p {
  font-size: 1rem;
  color: var(--gray-600);
  margin: 0;
}

.login-error {
  background-color: rgba(239, 71, 111, 0.08);
  color: var(--danger-color);
  border-left: 3px solid var(--danger-color);
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 25px;
  font-size: 0.95rem;
  display: flex;
  align-items: flex-start;
}

.login-error::before {
  content: "⚠️";
  margin-right: 10px;
  font-size: 1.1rem;
}

.login-info {
  background-color: rgba(67, 97, 238, 0.08);
  color: var(--primary-color);
  border-left: 3px solid var(--primary-color);
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 25px;
  font-size: 0.95rem;
  display: flex;
  align-items: flex-start;
}

.login-info::before {
  content: "ℹ️";
  margin-right: 10px;
  font-size: 1.1rem;
}

.form-group {
  margin-bottom: 24px;
  position: relative;
}

.form-group label {
  display: block;
  font-size: 0.95rem;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: 10px;
  transition: var(--transition-speed);
}

.form-group input {
  width: 100%;
  padding: 12px 14px;
  border: 1px solid var(--gray-300);
  border-radius: 4px;
  font-size: 1rem;
  transition: all var(--transition-speed);
  box-shadow: var(--input-shadow);
  background-color: var(--light-color);
}

.form-group input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
  outline: none;
}

.field-error {
  color: var(--danger-color);
  font-size: 0.85rem;
  margin-top: 8px;
  display: flex;
  align-items: center;
}

.field-error::before {
  content: "⚠️";
  font-size: 0.85rem;
  margin-right: 6px;
}

.form-actions {
  margin-top: 24px;
  text-align: center;
}

.login-btn {
  width: 100%;
  padding: 10px 15px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-speed);
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(67, 97, 238, 0.2);
}

.login-btn:hover {
  background: var(--primary-dark);
  box-shadow: 0 3px 8px rgba(67, 97, 238, 0.25);
}

.login-btn span {
  position: relative;
  z-index: 1;
}

.login-links {
  text-align: center;
  margin-top: 20px;
  font-size: 0.9rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 10px;
}

.login-links a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-speed);
  font-weight: 500;
}

.login-links a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.login-footer {
  text-align: center;
  margin-top: 30px;
  color: var(--gray-600);
  font-size: 0.85rem;
  width: 100%;
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
}

.footer-brand {
  font-weight: 600;
  color: var(--primary-color);
}

/* Dark Mode Support */
html[data-theme="dark"] body.login {
  background-color: #121212;
}

html[data-theme="dark"] body.login::before {
  background: linear-gradient(
    45deg,
    rgba(67, 97, 238, 0.08) 0%,
    rgba(58, 12, 163, 0.08) 100%
  );
}

html[data-theme="dark"] #header {
  background-color: #1e1e1e;
}

@media (prefers-color-scheme: dark) {
  body.login {
    background-color: #121212;
  }

  body.login::before {
    background: linear-gradient(
      45deg,
      rgba(67, 97, 238, 0.08) 0%,
      rgba(58, 12, 163, 0.08) 100%
    );
  }

  #header {
    background-color: #1e1e1e;
  }
}

html[data-theme="dark"] .login-box {
  background-color: #1e1e1e;
  border-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

html[data-theme="dark"] .login-logo h1 {
  color: #f0f0f0;
}

html[data-theme="dark"] .login-welcome h2 {
  color: #f0f0f0;
}

html[data-theme="dark"] .login-welcome p {
  color: #aaa;
}

html[data-theme="dark"] .login-error {
  background-color: rgba(239, 71, 111, 0.1);
  border-color: var(--danger-color);
}

html[data-theme="dark"] .login-info {
  background-color: rgba(67, 97, 238, 0.1);
  border-color: var(--primary-color);
}

html[data-theme="dark"] .form-group label {
  color: #d0d0d0;
}

html[data-theme="dark"] .form-group input {
  background-color: #2a2a2a;
  border-color: #444;
  color: #f0f0f0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

html[data-theme="dark"] .form-group input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
}

html[data-theme="dark"] .login-btn {
  box-shadow: 0 2px 5px rgba(67, 97, 238, 0.3);
}

html[data-theme="dark"] .login-btn:hover {
  background: var(--secondary-color);
  box-shadow: 0 3px 8px rgba(67, 97, 238, 0.4);
}

html[data-theme="dark"] .login-links a {
  color: var(--accent-color);
}

html[data-theme="dark"] .login-links a:hover {
  color: #6ad5ff;
  text-decoration: underline;
}

html[data-theme="dark"] .login-footer {
  color: #aaa;
}

html[data-theme="dark"] .footer-brand {
  color: var(--accent-color);
}

@media (prefers-color-scheme: dark) {
  .login-box {
    background-color: #1e1e1e;
    border-color: rgba(255, 255, 255, 0.05);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  }

  .login-logo h1 {
    color: #f0f0f0;
  }

  .login-welcome h2 {
    color: #f0f0f0;
  }

  .login-welcome p {
    color: #aaa;
  }

  .login-error {
    background-color: rgba(239, 71, 111, 0.1);
    border-color: var(--danger-color);
  }

  .login-info {
    background-color: rgba(67, 97, 238, 0.1);
    border-color: var(--primary-color);
  }

  .form-group label {
    color: #d0d0d0;
  }

  .form-group input {
    background-color: #2a2a2a;
    border-color: #444;
    color: #f0f0f0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }

  .form-group input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
  }

  .login-btn {
    box-shadow: 0 2px 5px rgba(67, 97, 238, 0.3);
  }

  .login-btn:hover {
    background: var(--secondary-color);
    box-shadow: 0 3px 8px rgba(67, 97, 238, 0.4);
  }

  .login-links a {
    color: var(--accent-color);
  }

  .login-links a:hover {
    color: #6ad5ff;
    text-decoration: underline;
  }

  .login-footer {
    color: #aaa;
  }

  .footer-brand {
    color: var(--accent-color);
  }
}

/* Responsive Adjustments */
@media (max-width: 480px) {
  .login-container {
    max-width: 100%;
    padding: 0 15px;
  }

  .login-box {
    padding: 25px 20px;
    border-radius: 8px;
  }

  .login-logo i {
    font-size: 2.5rem;
  }

  .login-logo h1 {
    font-size: 1.6rem;
  }

  .login-welcome h2 {
    font-size: 1.4rem;
  }

  .login-welcome p {
    font-size: 0.95rem;
  }

  .form-group {
    margin-bottom: 20px;
  }

  .form-group input {
    padding: 10px 12px;
  }

  .login-btn {
    padding: 10px;
  }

  .login-footer {
    position: relative;
    margin-top: 30px;
    bottom: auto;
  }
}
