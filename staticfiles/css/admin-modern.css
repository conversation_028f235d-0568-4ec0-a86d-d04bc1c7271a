/* CompletoPLUS Modern Admin Theme */

/* Variables */
:root {
  --primary-color: #4361ee;
  --primary-dark: #3a56d4;
  --secondary-color: #7209b7;
  --success-color: #06d6a0;
  --warning-color: #ffd166;
  --danger-color: #ef476f;
  --info-color: #4cc9f0;
  --light-color: #f8f9fa;
  --dark-color: #212529;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  --border-radius: 10px;
  --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --transition-speed: 0.3s;

  /* Light mode specific variables */
  --body-bg: #f8f9fa;
  --body-text: #212529;
  --module-bg: #ffffff;
  --module-border: var(--gray-200);
  --link-color: var(--primary-color);
  --link-hover: var(--primary-dark);
  --header-bg: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  --header-text: #ffffff;
}

/* Global Styles */
body {
  font-family: "Poppins", sans-serif;
  background-color: var(--body-bg);
  color: var(--body-text);
}

/* Dark Mode Toggle */
.theme-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  border: none;
  transition: all var(--transition-speed) ease;
}

.theme-toggle:hover {
  transform: scale(1.1);
}

.theme-toggle i {
  font-size: 1.2rem;
}

/* Header */
#header {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.15);
  padding: 18px 25px;
  height: auto;
  line-height: normal;
  border-bottom: none;
}

#branding h1 {
  font-weight: 600;
  margin: 0;
  padding: 0;
}

.brand-link {
  display: flex;
  align-items: center;
  color: white !important;
  text-decoration: none;
}

.brand-icon {
  margin-right: 10px;
  font-size: 1.5rem;
}

/* Admin Shortcuts */
.admin-shortcuts {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 15px;
  padding: 10px 0;
}

.shortcut-link {
  color: white !important;
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  text-decoration: none;
}

.shortcut-link:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* User Tools */
.user-tools {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-icon {
  font-size: 1.2rem;
}

.user-actions {
  display: flex;
  gap: 15px;
}

.action-link {
  color: rgba(255, 255, 255, 0.8) !important;
  display: flex;
  align-items: center;
  gap: 5px;
  text-decoration: none;
  transition: color 0.3s ease;
}

.action-link:hover {
  color: white !important;
}

/* Breadcrumbs */
div.breadcrumbs {
  background: var(--secondary-color);
  padding: 12px 20px;
  border-bottom: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  font-size: 0.9rem;
}

div.breadcrumbs a {
  color: rgba(255, 255, 255, 0.8) !important;
}

div.breadcrumbs a:hover {
  color: white !important;
}

/* Content */
#content {
  padding: 20px;
}

/* Modules */
.module {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-bottom: 25px;
  background-color: white;
  overflow: hidden;
  border: 1px solid var(--gray-200);
}

.module h2,
.module caption,
.inline-group h2 {
  background: var(--primary-color);
  color: white;
  font-weight: 600;
  padding: 12px 15px;
  font-size: 1rem;
}

/* Tables */
table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
}

table thead th {
  background: var(--gray-100);
  color: var(--gray-800);
  font-weight: 600;
  padding: 12px 15px;
  border-top: none;
  border-bottom: 1px solid var(--gray-300);
}

table tbody tr td {
  padding: 12px 15px;
  border-bottom: 1px solid var(--gray-200);
}

.row1 {
  background: white;
}

.row2 {
  background: var(--gray-100);
}

/* Buttons */
.button,
input[type="submit"],
input[type="button"],
.submit-row input,
a.button {
  background: var(--primary-color);
  color: white;
  border-radius: 6px;
  padding: 10px 18px;
  font-weight: 500;
  border: none;
  transition: all var(--transition-speed) ease;
  box-shadow: 0 2px 5px rgba(67, 97, 238, 0.2);
}

.button:hover,
input[type="submit"]:hover,
input[type="button"]:hover,
.submit-row input:hover,
a.button:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.button.default,
input[type="submit"].default,
.submit-row input.default {
  background: var(--success-color);
}

.button.default:hover,
input[type="submit"].default:hover,
.submit-row input.default:hover {
  background: #05b989;
}

/* Links */
a:link,
a:visited {
  color: var(--primary-color);
  transition: color 0.3s;
}

a:hover {
  color: var(--primary-dark);
}

/* Form Elements */
.form-row {
  padding: 12px 15px;
  border-bottom: 1px solid var(--gray-200);
}

.form-row label {
  font-weight: 500;
  color: var(--gray-700);
}

input[type="text"],
input[type="password"],
input[type="email"],
input[type="url"],
input[type="number"],
input[type="tel"],
textarea,
select,
.vTextField {
  border: 1px solid var(--gray-300);
  border-radius: 4px;
  padding: 8px 12px;
  font-family: "Poppins", sans-serif;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="number"]:focus,
input[type="tel"]:focus,
textarea:focus,
select:focus,
.vTextField:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/* Messages */
ul.messagelist {
  padding: 0;
  margin: 0 0 15px 0;
}

ul.messagelist li {
  padding: 12px 15px;
  margin: 0 0 10px 0;
  border-radius: 4px;
  font-weight: 500;
}

ul.messagelist li.success {
  background: rgba(6, 214, 160, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(6, 214, 160, 0.2);
}

ul.messagelist li.warning {
  background: rgba(255, 209, 102, 0.1);
  color: #e6b800;
  border: 1px solid rgba(255, 209, 102, 0.2);
}

ul.messagelist li.error {
  background: rgba(239, 71, 111, 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(239, 71, 111, 0.2);
}

/* Footer */
#footer {
  padding: 20px;
  color: var(--gray-600);
  font-size: 0.9rem;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Responsive Adjustments */
@media (max-width: 767px) {
  .admin-shortcuts {
    flex-direction: column;
    gap: 5px;
  }

  #header {
    padding: 10px 15px;
  }

  #branding h1 {
    font-size: 1.2rem;
  }

  .user-tools {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .user-actions {
    margin-top: 5px;
  }

  .footer-content {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}

/* Dark Mode Support */
[data-theme="dark"] {
  --body-bg: #121212;
  --body-text: #e0e0e0;
  --module-bg: #1e1e1e;
  --module-border: #333;
  --link-color: #6d8fff;
  --link-hover: #8aa4ff;
}

[data-theme="dark"] body {
  background-color: var(--body-bg);
  color: var(--body-text);
}

[data-theme="dark"] .module {
  background-color: var(--module-bg);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  border-color: var(--module-border);
}

[data-theme="dark"] .dashboard-section {
  background-color: var(--module-bg);
  border-color: var(--module-border);
}

[data-theme="dark"] .stat-card {
  background-color: var(--module-bg);
  border-color: var(--module-border);
}

[data-theme="dark"] .section-title {
  color: var(--body-text);
  border-bottom-color: var(--module-border);
}

[data-theme="dark"] .app-item {
  background-color: var(--module-bg);
  border-color: var(--module-border);
}

[data-theme="dark"] .app-name {
  color: var(--body-text);
  border-bottom-color: var(--module-border);
}

[data-theme="dark"] .model-item {
  background-color: #2a2a2a;
}

[data-theme="dark"] .model-item:hover {
  background-color: #333;
  border-color: var(--primary-color);
}

[data-theme="dark"] .model-link {
  color: var(--link-color);
}

[data-theme="dark"] table thead th {
  background: #2a2a2a;
  color: var(--body-text);
  border-bottom: 1px solid var(--module-border);
}

[data-theme="dark"] table tbody tr td {
  border-bottom: 1px solid var(--module-border);
}

[data-theme="dark"] .row1 {
  background: var(--module-bg);
}

[data-theme="dark"] .row2 {
  background: #2a2a2a;
}

[data-theme="dark"] .form-row {
  border-bottom: 1px solid var(--module-border);
}

[data-theme="dark"] .form-row label {
  color: var(--body-text);
}

[data-theme="dark"] input[type="text"],
[data-theme="dark"] input[type="password"],
[data-theme="dark"] input[type="email"],
[data-theme="dark"] input[type="url"],
[data-theme="dark"] input[type="number"],
[data-theme="dark"] input[type="tel"],
[data-theme="dark"] textarea,
[data-theme="dark"] select,
[data-theme="dark"] .vTextField {
  background-color: #2a2a2a;
  border: 1px solid #444;
  color: var(--body-text);
}

[data-theme="dark"] a:link,
[data-theme="dark"] a:visited {
  color: var(--link-color);
}

[data-theme="dark"] a:hover {
  color: var(--link-hover);
}

[data-theme="dark"] #footer {
  color: #aaa;
}

[data-theme="dark"] .theme-toggle {
  background-color: #333;
}

/* System preference based dark mode as fallback */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]) {
    --body-bg: #121212;
    --body-text: #e0e0e0;
    --module-bg: #1e1e1e;
    --module-border: #333;
    --link-color: #6d8fff;
    --link-hover: #8aa4ff;
  }
}
