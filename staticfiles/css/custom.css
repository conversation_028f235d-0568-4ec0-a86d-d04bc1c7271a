/* Custom CSS for File Sharing Platform */

/* General Styles */
body {
  padding-top: 56px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
}

.content {
  flex: 1;
}

.footer {
  margin-top: auto;
  padding: 20px 0;
  background-color: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

/* Navigation */
.navbar-brand {
  font-weight: bold;
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0.25rem 0.5rem;
  border-radius: 50%;
  background-color: #dc3545;
  color: white;
  font-size: 0.75rem;
}

/* Cards */
.card {
  margin-bottom: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: none;
  border-radius: 0.5rem;
}

.card-header {
  border-radius: 0.5rem 0.5rem 0 0 !important;
}

/* Status Badges */
.status-badge {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
}

.status-pending {
  background-color: #ffc107;
}

.status-in-progress {
  background-color: #17a2b8;
  color: white;
}

.status-completed {
  background-color: #28a745;
  color: white;
}

/* Forms */
.form-control:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Buttons */
.btn {
  border-radius: 0.25rem;
}

.btn-primary {
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:hover {
  background-color: #0069d9;
  border-color: #0062cc;
}

/* Tables */
.table th {
  background-color: #f8f9fa;
  border-top: none;
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container {
    padding-left: 15px;
    padding-right: 15px;
  }

  h1 {
    font-size: 1.8rem;
  }

  .card-body {
    padding: 1rem;
  }
}

/* File upload area */
.file-upload-area {
  border: 2px dashed #ddd;
  border-radius: 5px;
  padding: 20px;
  text-align: center;
  margin-bottom: 20px;
  transition: all 0.3s;
}

.file-upload-area:hover {
  border-color: #007bff;
  background-color: #f8f9fa;
}

/* Notification styles */
.notification-unread {
  background-color: #e8f4ff;
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.notification-item {
  transition: background-color 0.3s;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-message {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

/* Toast notifications */
.toast-container {
  z-index: 1060;
}

.toast {
  min-width: 300px;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border: none;
  opacity: 1;
}

.toast .toast-header {
  border-bottom: none;
}

.toast .toast-body {
  padding-top: 0.5rem;
}

/* Dashboard customization */
.theme-light {
  --bg-color: #f8f9fa;
  --text-color: #212529;
  --card-bg: #ffffff;
  --card-border: rgba(0, 0, 0, 0.125);
  --input-bg: #ffffff;
}

.theme-dark {
  --bg-color: #212529;
  --text-color: #f8f9fa;
  --card-bg: #343a40;
  --card-border: rgba(255, 255, 255, 0.125);
  --input-bg: #2b3035;
}

/* Color schemes */
.color-blue {
  --primary-color: #4361ee;
  --secondary-color: #3f37c9;
  --success-color: #4cc9f0;
  --info-color: #4895ef;
  --warning-color: #f72585;
  --danger-color: #e63946;
}

.color-green {
  --primary-color: #2d6a4f;
  --secondary-color: #40916c;
  --success-color: #52b788;
  --info-color: #74c69d;
  --warning-color: #d8f3dc;
  --danger-color: #b7e4c7;
}

.color-purple {
  --primary-color: #7209b7;
  --secondary-color: #560bad;
  --success-color: #480ca8;
  --info-color: #3a0ca3;
  --warning-color: #f72585;
  --danger-color: #4cc9f0;
}

.color-orange {
  --primary-color: #fb8500;
  --secondary-color: #ffb703;
  --success-color: #fd9e02;
  --info-color: #8ecae6;
  --warning-color: #219ebc;
  --danger-color: #023047;
}

.color-red {
  --primary-color: #e63946;
  --secondary-color: #a8201a;
  --success-color: #f9c74f;
  --info-color: #577590;
  --warning-color: #f8961e;
  --danger-color: #d62828;
}

/* Dashboard layouts */
.layout-compact .card {
  margin-bottom: 0.75rem;
}

.layout-compact .card-body {
  padding: 0.75rem;
}

.layout-expanded .card {
  margin-bottom: 1.5rem;
}

.layout-expanded .card-body {
  padding: 1.5rem;
}

/* Dark mode button enhancements */
body.theme-dark .btn,
body.theme-dark button.btn {
  border-width: 2px;
  font-weight: 600;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

body.theme-dark .btn:hover,
body.theme-dark button.btn:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  transform: translateY(-1px);
}

/* Primary buttons */
body.theme-dark .btn-primary {
  background-color: #5b7cff !important; /* Brighter blue */
  border-color: #7b4dff !important;
  color: #ffffff !important;
}

body.theme-dark .btn-primary:hover {
  background-color: #7b4dff !important;
  border-color: #9d4edd !important;
}

/* Success buttons */
body.theme-dark .btn-success {
  background-color: #4ade80 !important; /* Bright green */
  border-color: #2dc653 !important;
  color: #121212 !important;
}

body.theme-dark .btn-success:hover {
  background-color: #2dc653 !important;
  border-color: #4ade80 !important;
}

/* Danger buttons */
body.theme-dark .btn-danger {
  background-color: #ff6b6b !important; /* Bright red */
  border-color: #e63946 !important;
  color: #ffffff !important;
}

body.theme-dark .btn-danger:hover {
  background-color: #e63946 !important;
  border-color: #ff6b6b !important;
}

/* Warning buttons */
body.theme-dark .btn-warning {
  background-color: #ffb74d !important; /* Bright orange */
  border-color: #ff9e00 !important;
  color: #121212 !important;
}

body.theme-dark .btn-warning:hover {
  background-color: #ff9e00 !important;
  border-color: #ffb74d !important;
}

/* Info buttons */
body.theme-dark .btn-info {
  background-color: #72efff !important; /* Bright cyan */
  border-color: #4cc9f0 !important;
  color: #121212 !important;
}

body.theme-dark .btn-info:hover {
  background-color: #4cc9f0 !important;
  border-color: #72efff !important;
}

/* Secondary buttons */
body.theme-dark .btn-secondary {
  background-color: #9d4edd !important; /* Bright purple */
  border-color: #7209b7 !important;
  color: #ffffff !important;
}

body.theme-dark .btn-secondary:hover {
  background-color: #7209b7 !important;
  border-color: #9d4edd !important;
}

/* Outline buttons */
body.theme-dark .btn-outline-primary {
  border-color: #5b7cff !important;
  color: #5b7cff !important;
  background-color: transparent !important;
}

body.theme-dark .btn-outline-primary:hover {
  background-color: #5b7cff !important;
  color: #ffffff !important;
}

body.theme-dark .btn-outline-success {
  border-color: #4ade80 !important;
  color: #4ade80 !important;
  background-color: transparent !important;
}

body.theme-dark .btn-outline-success:hover {
  background-color: #4ade80 !important;
  color: #121212 !important;
}

body.theme-dark .btn-outline-danger {
  border-color: #ff6b6b !important;
  color: #ff6b6b !important;
  background-color: transparent !important;
}

body.theme-dark .btn-outline-danger:hover {
  background-color: #ff6b6b !important;
  color: #ffffff !important;
}

body.theme-dark .btn-outline-warning {
  border-color: #ffb74d !important;
  color: #ffb74d !important;
  background-color: transparent !important;
}

body.theme-dark .btn-outline-warning:hover {
  background-color: #ffb74d !important;
  color: #121212 !important;
}

body.theme-dark .btn-outline-info {
  border-color: #72efff !important;
  color: #72efff !important;
  background-color: transparent !important;
}

body.theme-dark .btn-outline-info:hover {
  background-color: #72efff !important;
  color: #121212 !important;
}

body.theme-dark .btn-outline-secondary {
  border-color: #9d4edd !important;
  color: #9d4edd !important;
  background-color: transparent !important;
}

body.theme-dark .btn-outline-secondary:hover {
  background-color: #9d4edd !important;
  color: #ffffff !important;
}

/* Project cards */
.project-card {
  transition: transform 0.3s;
}

.project-card:hover {
  transform: translateY(-5px);
}

/* Testimonials Section */
.testimonials-section {
  padding: 2rem 0;
}

.testimonial-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

.testimonial-card .card-body {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Testimonial avatar circles */
.avatar-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  font-size: 14px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Responsive testimonials */
@media (max-width: 767px) {
  .testimonial-carousel .row {
    display: flex;
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 1rem;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
  }

  .testimonial-carousel .col-md-4 {
    flex: 0 0 85%;
    max-width: 85%;
    scroll-snap-align: center;
    padding-right: 10px;
    padding-left: 10px;
  }

  .testimonial-carousel .col-md-4:first-child {
    padding-left: 15px;
  }

  .testimonial-carousel .col-md-4:last-child {
    padding-right: 15px;
  }
}

/* Custom file input */
.custom-file-input::-webkit-file-upload-button {
  visibility: hidden;
}

.custom-file-input::before {
  content: "Select file";
  display: inline-block;
  background: linear-gradient(top, #f9f9f9, #e3e3e3);
  border: 1px solid #999;
  border-radius: 3px;
  padding: 5px 8px;
  outline: none;
  white-space: nowrap;
  cursor: pointer;
  text-shadow: 1px 1px #fff;
  font-weight: 700;
  font-size: 10pt;
}

.custom-file-input:hover::before {
  border-color: black;
}

.custom-file-input:active::before {
  background: -webkit-linear-gradient(top, #e3e3e3, #f9f9f9);
}
