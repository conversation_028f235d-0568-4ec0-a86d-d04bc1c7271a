[{"model": "auth.permission", "pk": 1, "fields": {"name": "Can add log entry", "content_type": 1, "codename": "add_logentry"}}, {"model": "auth.permission", "pk": 2, "fields": {"name": "Can change log entry", "content_type": 1, "codename": "change_logentry"}}, {"model": "auth.permission", "pk": 3, "fields": {"name": "Can delete log entry", "content_type": 1, "codename": "delete_logentry"}}, {"model": "auth.permission", "pk": 4, "fields": {"name": "Can view log entry", "content_type": 1, "codename": "view_logentry"}}, {"model": "auth.permission", "pk": 5, "fields": {"name": "Can add permission", "content_type": 2, "codename": "add_permission"}}, {"model": "auth.permission", "pk": 6, "fields": {"name": "Can change permission", "content_type": 2, "codename": "change_permission"}}, {"model": "auth.permission", "pk": 7, "fields": {"name": "Can delete permission", "content_type": 2, "codename": "delete_permission"}}, {"model": "auth.permission", "pk": 8, "fields": {"name": "Can view permission", "content_type": 2, "codename": "view_permission"}}, {"model": "auth.permission", "pk": 9, "fields": {"name": "Can add group", "content_type": 3, "codename": "add_group"}}, {"model": "auth.permission", "pk": 10, "fields": {"name": "Can change group", "content_type": 3, "codename": "change_group"}}, {"model": "auth.permission", "pk": 11, "fields": {"name": "Can delete group", "content_type": 3, "codename": "delete_group"}}, {"model": "auth.permission", "pk": 12, "fields": {"name": "Can view group", "content_type": 3, "codename": "view_group"}}, {"model": "auth.permission", "pk": 13, "fields": {"name": "Can add content type", "content_type": 4, "codename": "add_contenttype"}}, {"model": "auth.permission", "pk": 14, "fields": {"name": "Can change content type", "content_type": 4, "codename": "change_contenttype"}}, {"model": "auth.permission", "pk": 15, "fields": {"name": "Can delete content type", "content_type": 4, "codename": "delete_contenttype"}}, {"model": "auth.permission", "pk": 16, "fields": {"name": "Can view content type", "content_type": 4, "codename": "view_contenttype"}}, {"model": "auth.permission", "pk": 17, "fields": {"name": "Can add session", "content_type": 5, "codename": "add_session"}}, {"model": "auth.permission", "pk": 18, "fields": {"name": "Can change session", "content_type": 5, "codename": "change_session"}}, {"model": "auth.permission", "pk": 19, "fields": {"name": "Can delete session", "content_type": 5, "codename": "delete_session"}}, {"model": "auth.permission", "pk": 20, "fields": {"name": "Can view session", "content_type": 5, "codename": "view_session"}}, {"model": "auth.permission", "pk": 21, "fields": {"name": "Can add user", "content_type": 6, "codename": "add_customuser"}}, {"model": "auth.permission", "pk": 22, "fields": {"name": "Can change user", "content_type": 6, "codename": "change_customuser"}}, {"model": "auth.permission", "pk": 23, "fields": {"name": "Can delete user", "content_type": 6, "codename": "delete_customuser"}}, {"model": "auth.permission", "pk": 24, "fields": {"name": "Can view user", "content_type": 6, "codename": "view_customuser"}}, {"model": "auth.permission", "pk": 25, "fields": {"name": "Can add dashboard settings", "content_type": 7, "codename": "add_dashboardsettings"}}, {"model": "auth.permission", "pk": 26, "fields": {"name": "Can change dashboard settings", "content_type": 7, "codename": "change_dashboardsettings"}}, {"model": "auth.permission", "pk": 27, "fields": {"name": "Can delete dashboard settings", "content_type": 7, "codename": "delete_dashboardsettings"}}, {"model": "auth.permission", "pk": 28, "fields": {"name": "Can view dashboard settings", "content_type": 7, "codename": "view_dashboardsettings"}}, {"model": "auth.permission", "pk": 29, "fields": {"name": "Can add email log", "content_type": 8, "codename": "add_emaillog"}}, {"model": "auth.permission", "pk": 30, "fields": {"name": "Can change email log", "content_type": 8, "codename": "change_emaillog"}}, {"model": "auth.permission", "pk": 31, "fields": {"name": "Can delete email log", "content_type": 8, "codename": "delete_emaillog"}}, {"model": "auth.permission", "pk": 32, "fields": {"name": "Can view email log", "content_type": 8, "codename": "view_emaillog"}}, {"model": "auth.permission", "pk": 33, "fields": {"name": "Can add testimonial", "content_type": 9, "codename": "add_testimonial"}}, {"model": "auth.permission", "pk": 34, "fields": {"name": "Can change testimonial", "content_type": 9, "codename": "change_testimonial"}}, {"model": "auth.permission", "pk": 35, "fields": {"name": "Can delete testimonial", "content_type": 9, "codename": "delete_testimonial"}}, {"model": "auth.permission", "pk": 36, "fields": {"name": "Can view testimonial", "content_type": 9, "codename": "view_testimonial"}}, {"model": "auth.permission", "pk": 37, "fields": {"name": "Can add project", "content_type": 10, "codename": "add_project"}}, {"model": "auth.permission", "pk": 38, "fields": {"name": "Can change project", "content_type": 10, "codename": "change_project"}}, {"model": "auth.permission", "pk": 39, "fields": {"name": "Can delete project", "content_type": 10, "codename": "delete_project"}}, {"model": "auth.permission", "pk": 40, "fields": {"name": "Can view project", "content_type": 10, "codename": "view_project"}}, {"model": "auth.permission", "pk": 41, "fields": {"name": "Can add notification", "content_type": 11, "codename": "add_notification"}}, {"model": "auth.permission", "pk": 42, "fields": {"name": "Can change notification", "content_type": 11, "codename": "change_notification"}}, {"model": "auth.permission", "pk": 43, "fields": {"name": "Can delete notification", "content_type": 11, "codename": "delete_notification"}}, {"model": "auth.permission", "pk": 44, "fields": {"name": "Can view notification", "content_type": 11, "codename": "view_notification"}}, {"model": "auth.permission", "pk": 45, "fields": {"name": "Can add file", "content_type": 12, "codename": "add_file"}}, {"model": "auth.permission", "pk": 46, "fields": {"name": "Can change file", "content_type": 12, "codename": "change_file"}}, {"model": "auth.permission", "pk": 47, "fields": {"name": "Can delete file", "content_type": 12, "codename": "delete_file"}}, {"model": "auth.permission", "pk": 48, "fields": {"name": "Can view file", "content_type": 12, "codename": "view_file"}}, {"model": "auth.permission", "pk": 49, "fields": {"name": "Can add progress note", "content_type": 13, "codename": "add_progressnote"}}, {"model": "auth.permission", "pk": 50, "fields": {"name": "Can change progress note", "content_type": 13, "codename": "change_progressnote"}}, {"model": "auth.permission", "pk": 51, "fields": {"name": "Can delete progress note", "content_type": 13, "codename": "delete_progressnote"}}, {"model": "auth.permission", "pk": 52, "fields": {"name": "Can view progress note", "content_type": 13, "codename": "view_progressnote"}}, {"model": "auth.permission", "pk": 53, "fields": {"name": "Can add file download", "content_type": 14, "codename": "add_filedownload"}}, {"model": "auth.permission", "pk": 54, "fields": {"name": "Can change file download", "content_type": 14, "codename": "change_filedownload"}}, {"model": "auth.permission", "pk": 55, "fields": {"name": "Can delete file download", "content_type": 14, "codename": "delete_filedownload"}}, {"model": "auth.permission", "pk": 56, "fields": {"name": "Can view file download", "content_type": 14, "codename": "view_filedownload"}}, {"model": "auth.permission", "pk": 57, "fields": {"name": "Can add analysis request", "content_type": 15, "codename": "add_analysisrequest"}}, {"model": "auth.permission", "pk": 58, "fields": {"name": "Can change analysis request", "content_type": 15, "codename": "change_analysisrequest"}}, {"model": "auth.permission", "pk": 59, "fields": {"name": "Can delete analysis request", "content_type": 15, "codename": "delete_analysisrequest"}}, {"model": "auth.permission", "pk": 60, "fields": {"name": "Can view analysis request", "content_type": 15, "codename": "view_analysisrequest"}}, {"model": "auth.permission", "pk": 61, "fields": {"name": "Can add analysis parameter", "content_type": 16, "codename": "add_analysisparameter"}}, {"model": "auth.permission", "pk": 62, "fields": {"name": "Can change analysis parameter", "content_type": 16, "codename": "change_analysisparameter"}}, {"model": "auth.permission", "pk": 63, "fields": {"name": "Can delete analysis parameter", "content_type": 16, "codename": "delete_analysisparameter"}}, {"model": "auth.permission", "pk": 64, "fields": {"name": "Can view analysis parameter", "content_type": 16, "codename": "view_analysisparameter"}}, {"model": "auth.permission", "pk": 65, "fields": {"name": "Can add analysis note", "content_type": 17, "codename": "add_analysisnote"}}, {"model": "auth.permission", "pk": 66, "fields": {"name": "Can change analysis note", "content_type": 17, "codename": "change_analysisnote"}}, {"model": "auth.permission", "pk": 67, "fields": {"name": "Can delete analysis note", "content_type": 17, "codename": "delete_analysisnote"}}, {"model": "auth.permission", "pk": 68, "fields": {"name": "Can view analysis note", "content_type": 17, "codename": "view_analysisnote"}}, {"model": "auth.permission", "pk": 69, "fields": {"name": "Can add analysis result", "content_type": 18, "codename": "add_analysisresult"}}, {"model": "auth.permission", "pk": 70, "fields": {"name": "Can change analysis result", "content_type": 18, "codename": "change_analysisresult"}}, {"model": "auth.permission", "pk": 71, "fields": {"name": "Can delete analysis result", "content_type": 18, "codename": "delete_analysisresult"}}, {"model": "auth.permission", "pk": 72, "fields": {"name": "Can view analysis result", "content_type": 18, "codename": "view_analysisresult"}}]