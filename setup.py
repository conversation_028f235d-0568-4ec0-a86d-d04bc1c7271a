from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = fh.read().splitlines()

setup(
    name="CompletoPLUS",
    version="1.0.0",
    author="CompletoPLUS Team",
    author_email="<EMAIL>",
    description="Professional file sharing and project management platform",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/clevernat/CompletoPLUS",
    packages=find_packages(),
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Framework :: Django",
        "Framework :: Django :: 5.2",
    ],
    python_requires=">=3.9",
    install_requires=requirements,
)
