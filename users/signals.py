from django.db.models.signals import post_save, pre_delete
from django.dispatch import receiver
from django.conf import settings
from django.contrib.auth import get_user_model
from django.utils import timezone
import logging
from files.storage import CompletoPLUSCloudinaryStorage
from files.notifications import create_welcome_notification
from .utils import send_email_with_logging

logger = logging.getLogger(__name__)
User = get_user_model()


@receiver(post_save, sender=User)
def create_user_cloudinary_folder(sender, instance, created, **kwargs):
    """
    Create a Cloudinary folder for the user when they are created.
    """
    if created:
        # Create the Cloudinary folder directly
        if not settings.DEBUG or getattr(settings, 'CLOUDINARY_CREATE_FOLDERS_IN_DEBUG', False):
            logger.info(
                f"Creating Cloudinary folder for user {instance.username}")
            try:
                from files.storage import CompletoPLUSCloudinaryStorage
                storage = CompletoPLUSCloudinaryStorage()
                storage.create_user_folder(instance)
                logger.info(
                    f"Successfully created Cloudinary folder for user {instance.username}")
            except Exception as e:
                logger.error(
                    f"Error creating Cloudinary folder for user {instance.username}: {e}")
        else:
            logger.info(
                f"Skipping Cloudinary folder creation in debug mode for user {instance.username}")


@receiver(post_save, sender=User)
def send_welcome_email_on_creation(sender, instance, created, **kwargs):
    """
    Send a welcome email when a new user is created.
    This ensures that even if the view-based email sending fails,
    the user will still receive a welcome email.

    This function creates a welcome notification which will trigger
    the email sending via the notification signal handler.
    """
    if created:
        # Log the signal was triggered
        logger.info(
            f"Welcome email signal triggered for user {instance.username}")

        try:
            # Create welcome notification - this will trigger email sending via notification signal
            from files.notifications import create_welcome_notification

            # Create the welcome notification (this will handle email sending)
            notification = create_welcome_notification(instance)

            if notification:
                logger.info(
                    f"Welcome notification created directly for user {instance.username}")
            else:
                logger.error(
                    f"Failed to create welcome notification for user {instance.username}")

        except Exception as e:
            logger.error(
                f"Error creating welcome notification for user {instance.username}: {e}")

            # Fallback: try to send email directly if notification creation fails
            try:
                logger.info(
                    f"Attempting fallback welcome email to {instance.email}")

                # Send welcome email directly as fallback
                subject = 'Welcome to CompletoPLUS!'

                # Context for the email template
                context = {
                    'user': instance,
                    'site_url': settings.SITE_URL,
                    'current_year': timezone.now().year,
                }

                # Send email with logging - force send welcome emails regardless of preferences
                from .utils import send_email_with_logging
                success = send_email_with_logging(
                    recipient=instance,
                    email_type='welcome',
                    subject=subject,
                    template_name='emails/welcome_email.html',
                    context=context,
                    force_send=True  # Force send welcome emails
                )

                if success:
                    logger.info(
                        f"Fallback welcome email sent successfully to {instance.email}")
                else:
                    logger.error(
                        f"Failed to send fallback welcome email to {instance.email}")

            except Exception as fallback_error:
                logger.error(
                    f"Error sending fallback welcome email to {instance.email}: {fallback_error}")
                # Try to log the error
                try:
                    from users.models import EmailLog
                    EmailLog.objects.create(
                        recipient=instance,
                        email_type='welcome',
                        subject='Welcome to CompletoPLUS!',
                        status='failed',
                        error_message=str(fallback_error)
                    )
                except Exception as log_error:
                    logger.error(f"Error logging failed email: {log_error}")


@receiver(pre_delete, sender=User)
def delete_user_cloudinary_folder(sender, instance, **kwargs):
    """
    Delete the user's Cloudinary folder when the user is deleted.
    This ensures that when an admin deletes a client from the system,
    their Cloudinary folder is also deleted to keep storage clean.

    Note: This signal can be temporarily disconnected during bulk operations
    to improve performance, with manual cleanup handled afterward.
    """
    logger.info(
        f"User deletion detected for {instance.username} (ID: {instance.id})")

    try:
        # Only delete folders for client users
        if instance.is_client_user():
            logger.info(
                f"Deleting Cloudinary folder for client user {instance.username}")

            # Check if we're in a bulk operation (set by optimized bulk delete)
            if getattr(instance, '_skip_cloudinary_cleanup', False):
                logger.info(
                    f"Skipping Cloudinary cleanup for {instance.username} (bulk operation)")
                return

            storage = CompletoPLUSCloudinaryStorage()
            success = storage.delete_user_folder(instance)

            if success:
                logger.info(
                    f"Successfully deleted Cloudinary folder for user {instance.username}")
            else:
                logger.warning(
                    f"Failed to delete Cloudinary folder for user {instance.username}")
        else:
            logger.info(
                f"Skipping Cloudinary folder deletion for non-client user {instance.username}")
    except Exception as e:
        logger.error(
            f"Error deleting Cloudinary folder for user {instance.username}: {e}")
