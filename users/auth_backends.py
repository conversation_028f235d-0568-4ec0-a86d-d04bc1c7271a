from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from django.db.models import Q
from django.utils import timezone
import datetime
import uuid

UserModel = get_user_model()


class EmailOrUsernameModelBackend(ModelBackend):
    """
    Authentication backend that allows users to log in with either username or email
    and implements account lockout after multiple failed attempts.
    """

    def authenticate(self, request, username=None, password=None, **kwargs):
        if not username or not password:
            return None

        try:
            # Try to find the user by username or email
            if '@' in username:
                # Try to find user by email
                user = UserModel.objects.get(email=username)
            else:
                # Try to find user by username
                user = UserModel.objects.get(username=username)
        except UserModel.DoesNotExist:
            # Run the default password hasher once to reduce timing
            # attacks revealing whether a username exists or not
            UserModel().set_password(password)
            return None
        except Exception as e:
            print(f"Error finding user: {e}")
            return None

        # Check if account is locked
        if user.is_account_locked():
            # Calculate remaining lockout time
            remaining_time = user.account_locked_until - timezone.now()
            minutes = int(remaining_time.total_seconds() // 60)
            seconds = int(remaining_time.total_seconds() % 60)

            # Set error message on request if available
            if request:
                request.account_locked_message = f"Account locked. Try again in {minutes}m {seconds}s."
            return None

        # Check password
        if user.check_password(password):
            # Reset login attempts on successful login
            user.login_attempts = 0
            user.save(update_fields=['login_attempts'])
            return user
        else:
            # Increment login attempts on failed login
            user.login_attempts += 1

            # Lock account after 5 failed attempts
            if user.login_attempts >= 5:
                user.lock_account(duration_minutes=30)

                # Set error message on request if available
                if request:
                    request.account_locked_message = "Account locked for 30 minutes due to multiple failed login attempts."
            else:
                user.save(update_fields=['login_attempts'])

                # Set error message on request if available
                if request:
                    remaining_attempts = 5 - user.login_attempts
                    request.login_attempts_message = f"Invalid password. {remaining_attempts} attempts remaining before account lockout."

            return None
