"""
Management command to set up performance optimizations for CompletoPLUS.
"""
from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.db import connection
from django.conf import settings


class Command(BaseCommand):
    help = 'Set up performance optimizations for CompletoPLUS'

    def add_arguments(self, parser):
        parser.add_argument(
            '--setup',
            type=str,
            choices=['cache', 'indexes', 'all'],
            default='all',
            help='Type of performance setup to run'
        )

    def handle(self, *args, **options):
        setup_type = options['setup']

        self.stdout.write(self.style.SUCCESS('Setting up CompletoPLUS Performance Optimizations'))
        self.stdout.write('=' * 60)
        
        if setup_type in ['cache', 'all']:
            self.setup_cache()
        
        if setup_type in ['indexes', 'all']:
            self.setup_database_indexes()

    def setup_cache(self):
        """Set up database caching table"""
        self.stdout.write('\n' + self.style.WARNING('Setting up Database Cache'))
        self.stdout.write('-' * 40)
        
        try:
            # Create cache table
            call_command('createcachetable')
            self.stdout.write(self.style.SUCCESS('✓ Cache table created successfully'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Error creating cache table: {e}'))

    def setup_database_indexes(self):
        """Set up database indexes for better performance"""
        self.stdout.write('\n' + self.style.WARNING('Setting up Database Indexes'))
        self.stdout.write('-' * 40)
        
        # Define indexes that should be created for better performance
        indexes = [
            # User indexes
            "CREATE INDEX IF NOT EXISTS idx_users_email ON users_customuser(email);",
            "CREATE INDEX IF NOT EXISTS idx_users_username ON users_customuser(username);",
            
            # Project indexes
            "CREATE INDEX IF NOT EXISTS idx_projects_client ON files_project(client_id);",
            "CREATE INDEX IF NOT EXISTS idx_projects_status ON files_project(status);",
            "CREATE INDEX IF NOT EXISTS idx_projects_created ON files_project(created_at);",
            "CREATE INDEX IF NOT EXISTS idx_projects_client_status ON files_project(client_id, status);",
            
            # File indexes
            "CREATE INDEX IF NOT EXISTS idx_files_project ON files_file(project_id);",
            "CREATE INDEX IF NOT EXISTS idx_files_owner ON files_file(owner_id);",
            "CREATE INDEX IF NOT EXISTS idx_files_uploaded ON files_file(uploaded_at);",
            "CREATE INDEX IF NOT EXISTS idx_files_project_uploaded ON files_file(project_id, uploaded_at);",
            
            # Notification indexes
            "CREATE INDEX IF NOT EXISTS idx_notifications_user ON files_notification(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_notifications_read ON files_notification(is_read);",
            "CREATE INDEX IF NOT EXISTS idx_notifications_created ON files_notification(created_at);",
            "CREATE INDEX IF NOT EXISTS idx_notifications_user_read ON files_notification(user_id, is_read);",
            "CREATE INDEX IF NOT EXISTS idx_notifications_type ON files_notification(notification_type);",
        ]
        
        with connection.cursor() as cursor:
            for index_sql in indexes:
                try:
                    cursor.execute(index_sql)
                    index_name = index_sql.split('idx_')[1].split(' ')[0]
                    self.stdout.write(f"✓ Created index: idx_{index_name}")
                except Exception as e:
                    index_name = index_sql.split('idx_')[1].split(' ')[0] if 'idx_' in index_sql else 'unknown'
                    self.stdout.write(self.style.ERROR(f"✗ Error creating index idx_{index_name}: {e}"))

        self.stdout.write(self.style.SUCCESS('\n✓ Database indexes setup completed'))

    def show_performance_tips(self):
        """Show performance optimization tips"""
        self.stdout.write('\n' + self.style.WARNING('Performance Optimization Tips'))
        self.stdout.write('-' * 40)
        
        tips = [
            "1. Run 'python manage.py performance_analysis' regularly to monitor performance",
            "2. Clear cache periodically: 'python manage.py clear_cache'",
            "3. Monitor Railway.com resource usage in the dashboard",
            "4. Consider upgrading to Redis caching for better performance",
            "5. Use 'python manage.py dbshell' to analyze slow queries",
            "6. Enable Django Debug Toolbar in development for query analysis",
            "7. Monitor file upload sizes and implement streaming for large files",
            "8. Use pagination for large datasets",
            "9. Implement lazy loading for images and files",
            "10. Consider CDN for static files in production"
        ]
        
        for tip in tips:
            self.stdout.write(f"  {tip}")

        self.stdout.write('\n' + '=' * 60)
        self.stdout.write('Performance setup completed!')
        self.stdout.write('Run: python manage.py performance_analysis --test all')
        self.stdout.write('=' * 60)
