"""
Management command to test HTTPS enforcement and security settings.
"""
from django.core.management.base import BaseCommand
from django.conf import settings
import requests
import sys


class Command(BaseCommand):
    help = 'Test HTTPS enforcement and security settings for CompletoPLUS'

    def add_arguments(self, parser):
        parser.add_argument(
            '--mode',
            type=str,
            choices=['test', 'redirect', 'block'],
            default='test',
            help='Mode: test (check current), redirect (set redirect mode), block (set block mode)'
        )
        parser.add_argument(
            '--url',
            type=str,
            default='http://completoplus.com',
            help='URL to test (default: http://completoplus.com)'
        )

    def handle(self, *args, **options):
        mode = options['mode']
        test_url = options['url']

        if mode == 'test':
            self.test_https_enforcement(test_url)
        elif mode == 'redirect':
            self.set_enforcement_mode('REDIRECT')
        elif mode == 'block':
            self.set_enforcement_mode('BLOCK')

    def test_https_enforcement(self, test_url):
        """Test HTTPS enforcement by making requests to HTTP URLs"""
        self.stdout.write(self.style.SUCCESS('Testing HTTPS Enforcement for CompletoPLUS'))
        self.stdout.write('=' * 60)
        
        # Display current settings
        self.stdout.write(f"DEBUG mode: {settings.DEBUG}")
        self.stdout.write(f"SECURE_SSL_REDIRECT: {getattr(settings, 'SECURE_SSL_REDIRECT', 'Not set')}")
        self.stdout.write(f"HTTPS_ENFORCEMENT_MODE: {getattr(settings, 'HTTPS_ENFORCEMENT_MODE', 'Not set')}")
        self.stdout.write(f"SECURE_HSTS_SECONDS: {getattr(settings, 'SECURE_HSTS_SECONDS', 'Not set')}")
        self.stdout.write('')

        # Test URLs
        test_urls = [
            'http://completoplus.com',
            'http://www.completoplus.com',
            'https://completoplus.com',
            'https://www.completoplus.com'
        ]

        if not settings.DEBUG:
            self.stdout.write(self.style.WARNING(
                'Note: Testing in production mode. HTTPS enforcement should be active.'
            ))
        else:
            self.stdout.write(self.style.WARNING(
                'Note: Testing in DEBUG mode. HTTPS enforcement is disabled for development.'
            ))

        self.stdout.write('')
        
        for url in test_urls:
            self.test_single_url(url)

    def test_single_url(self, url):
        """Test a single URL for HTTPS enforcement"""
        try:
            self.stdout.write(f"Testing: {url}")
            
            # Make request with timeout and without following redirects initially
            response = requests.get(url, timeout=10, allow_redirects=False)
            
            if response.status_code in [301, 302, 307, 308]:
                redirect_url = response.headers.get('Location', 'No location header')
                self.stdout.write(self.style.SUCCESS(
                    f"  ✓ Redirects to: {redirect_url} (Status: {response.status_code})"
                ))
            elif response.status_code == 403:
                self.stdout.write(self.style.SUCCESS(
                    f"  ✓ Blocked with 403 Forbidden (HTTPS enforcement active)"
                ))
            elif response.status_code == 200:
                if url.startswith('https://'):
                    self.stdout.write(self.style.SUCCESS(
                        f"  ✓ HTTPS connection successful (Status: {response.status_code})"
                    ))
                else:
                    self.stdout.write(self.style.ERROR(
                        f"  ✗ HTTP connection allowed (Status: {response.status_code}) - HTTPS not enforced!"
                    ))
            else:
                self.stdout.write(self.style.WARNING(
                    f"  ? Unexpected status: {response.status_code}"
                ))
                
        except requests.exceptions.SSLError as e:
            self.stdout.write(self.style.ERROR(f"  ✗ SSL Error: {e}"))
        except requests.exceptions.ConnectionError as e:
            self.stdout.write(self.style.ERROR(f"  ✗ Connection Error: {e}"))
        except requests.exceptions.Timeout:
            self.stdout.write(self.style.ERROR(f"  ✗ Timeout"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"  ✗ Error: {e}"))
        
        self.stdout.write('')

    def set_enforcement_mode(self, mode):
        """Set HTTPS enforcement mode (for development/testing)"""
        self.stdout.write(self.style.SUCCESS(f'Setting HTTPS enforcement mode to: {mode}'))
        self.stdout.write('')
        self.stdout.write('To change the enforcement mode:')
        self.stdout.write(f'1. Edit fileshare/settings.py')
        self.stdout.write(f'2. Set HTTPS_ENFORCEMENT_MODE = "{mode}"')
        self.stdout.write('3. Restart the Django application')
        self.stdout.write('')
        self.stdout.write('Modes:')
        self.stdout.write('  REDIRECT: Automatically redirect HTTP to HTTPS (user-friendly)')
        self.stdout.write('  BLOCK: Block HTTP requests with 403 Forbidden (strict security)')
