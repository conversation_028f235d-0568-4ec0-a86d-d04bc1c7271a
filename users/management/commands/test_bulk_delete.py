"""
Management command to test and diagnose bulk delete functionality in Django admin.
"""
from django.core.management.base import BaseCommand
from django.contrib.admin.actions import delete_selected
from django.contrib.admin.sites import site
from django.contrib.auth import get_user_model
from django.db import transaction, connection
from django.test import RequestFactory
from django.contrib.sessions.middleware import SessionMiddleware
from django.contrib.auth.middleware import AuthenticationMiddleware
from django.contrib.messages.middleware import MessageMiddleware
import time
import traceback

User = get_user_model()


class Command(BaseCommand):
    help = 'Test bulk delete functionality for Django admin models'

    def add_arguments(self, parser):
        parser.add_argument(
            '--model',
            type=str,
            choices=['users', 'projects', 'files', 'notifications', 'all'],
            default='all',
            help='Model to test bulk delete for'
        )
        parser.add_argument(
            '--create-test-data',
            action='store_true',
            help='Create test data for bulk delete testing'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Perform dry run without actually deleting data'
        )

    def handle(self, *args, **options):
        model_type = options['model']
        create_test_data = options['create_test_data']
        dry_run = options['dry_run']

        self.stdout.write(self.style.SUCCESS('Django Admin Bulk Delete Diagnostic'))
        self.stdout.write('=' * 60)
        
        if create_test_data:
            self.create_test_data()
        
        if model_type in ['users', 'all']:
            self.test_user_bulk_delete(dry_run)
        
        if model_type in ['projects', 'all']:
            self.test_project_bulk_delete(dry_run)
        
        if model_type in ['files', 'all']:
            self.test_file_bulk_delete(dry_run)
        
        if model_type in ['notifications', 'all']:
            self.test_notification_bulk_delete(dry_run)

    def create_test_data(self):
        """Create test data for bulk delete testing"""
        self.stdout.write('\n' + self.style.WARNING('Creating Test Data'))
        self.stdout.write('-' * 40)
        
        from files.models import Project, File, Notification
        
        try:
            # Create test users
            test_users = []
            for i in range(3):
                username = f'test_bulk_user_{i}'
                if not User.objects.filter(username=username).exists():
                    user = User.objects.create_user(
                        username=username,
                        email=f'{username}@test.com',
                        password='testpass123',
                        user_type='client'
                    )
                    test_users.append(user)
                    self.stdout.write(f"✓ Created test user: {username}")
            
            # Create test projects
            if test_users:
                for i, user in enumerate(test_users):
                    project_name = f'Test Bulk Project {i}'
                    if not Project.objects.filter(name=project_name).exists():
                        project = Project.objects.create(
                            name=project_name,
                            description=f'Test project for bulk delete testing {i}',
                            client=user,
                            status='pending'
                        )
                        self.stdout.write(f"✓ Created test project: {project_name}")
                        
                        # Create test notifications
                        notification = Notification.objects.create(
                            user=user,
                            project=project,
                            message=f'Test notification for bulk delete {i}',
                            notification_type='project_created'
                        )
                        self.stdout.write(f"✓ Created test notification for {user.username}")
            
            self.stdout.write(self.style.SUCCESS('\n✓ Test data created successfully'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Error creating test data: {e}'))
            traceback.print_exc()

    def test_user_bulk_delete(self, dry_run=False):
        """Test bulk delete for CustomUser model"""
        self.stdout.write('\n' + self.style.WARNING('Testing User Bulk Delete'))
        self.stdout.write('-' * 40)
        
        try:
            # Get test users
            test_users = User.objects.filter(username__startswith='test_bulk_user_')
            if not test_users.exists():
                self.stdout.write(self.style.WARNING('No test users found. Run with --create-test-data first.'))
                return
            
            self.stdout.write(f"Found {test_users.count()} test users for bulk delete")
            
            # Test the bulk delete operation
            if not dry_run:
                start_time = time.time()
                
                # Simulate admin bulk delete
                with transaction.atomic():
                    # Check for related objects that would be deleted
                    related_objects = []
                    for user in test_users:
                        projects = user.projects.all()
                        files = user.files.all()
                        notifications = user.notifications.all()
                        
                        if projects.exists():
                            related_objects.append(f"Projects: {projects.count()}")
                        if files.exists():
                            related_objects.append(f"Files: {files.count()}")
                        if notifications.exists():
                            related_objects.append(f"Notifications: {notifications.count()}")
                    
                    if related_objects:
                        self.stdout.write(f"Related objects to be deleted: {', '.join(related_objects)}")
                    
                    # Perform the deletion
                    deleted_count = test_users.count()
                    test_users.delete()
                    
                    delete_time = (time.time() - start_time) * 1000
                    self.stdout.write(self.style.SUCCESS(
                        f"✓ Successfully deleted {deleted_count} users in {delete_time:.2f}ms"
                    ))
            else:
                self.stdout.write(self.style.WARNING("✓ Dry run - would delete users"))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ User bulk delete failed: {e}'))
            traceback.print_exc()

    def test_project_bulk_delete(self, dry_run=False):
        """Test bulk delete for Project model"""
        self.stdout.write('\n' + self.style.WARNING('Testing Project Bulk Delete'))
        self.stdout.write('-' * 40)
        
        try:
            from files.models import Project
            
            test_projects = Project.objects.filter(name__startswith='Test Bulk Project')
            if not test_projects.exists():
                self.stdout.write(self.style.WARNING('No test projects found. Run with --create-test-data first.'))
                return
            
            self.stdout.write(f"Found {test_projects.count()} test projects for bulk delete")
            
            if not dry_run:
                start_time = time.time()
                
                with transaction.atomic():
                    deleted_count = test_projects.count()
                    test_projects.delete()
                    
                    delete_time = (time.time() - start_time) * 1000
                    self.stdout.write(self.style.SUCCESS(
                        f"✓ Successfully deleted {deleted_count} projects in {delete_time:.2f}ms"
                    ))
            else:
                self.stdout.write(self.style.WARNING("✓ Dry run - would delete projects"))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Project bulk delete failed: {e}'))
            traceback.print_exc()

    def test_file_bulk_delete(self, dry_run=False):
        """Test bulk delete for File model"""
        self.stdout.write('\n' + self.style.WARNING('Testing File Bulk Delete'))
        self.stdout.write('-' * 40)
        
        try:
            from files.models import File
            
            # Get files from test projects
            test_files = File.objects.filter(project__name__startswith='Test Bulk Project')
            
            self.stdout.write(f"Found {test_files.count()} test files for bulk delete")
            
            if test_files.exists() and not dry_run:
                start_time = time.time()
                
                with transaction.atomic():
                    deleted_count = test_files.count()
                    test_files.delete()
                    
                    delete_time = (time.time() - start_time) * 1000
                    self.stdout.write(self.style.SUCCESS(
                        f"✓ Successfully deleted {deleted_count} files in {delete_time:.2f}ms"
                    ))
            else:
                self.stdout.write(self.style.WARNING("✓ No test files found or dry run"))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ File bulk delete failed: {e}'))
            traceback.print_exc()

    def test_notification_bulk_delete(self, dry_run=False):
        """Test bulk delete for Notification model"""
        self.stdout.write('\n' + self.style.WARNING('Testing Notification Bulk Delete'))
        self.stdout.write('-' * 40)
        
        try:
            from files.models import Notification
            
            test_notifications = Notification.objects.filter(
                message__startswith='Test notification for bulk delete'
            )
            
            self.stdout.write(f"Found {test_notifications.count()} test notifications for bulk delete")
            
            if test_notifications.exists() and not dry_run:
                start_time = time.time()
                
                with transaction.atomic():
                    deleted_count = test_notifications.count()
                    test_notifications.delete()
                    
                    delete_time = (time.time() - start_time) * 1000
                    self.stdout.write(self.style.SUCCESS(
                        f"✓ Successfully deleted {deleted_count} notifications in {delete_time:.2f}ms"
                    ))
            else:
                self.stdout.write(self.style.WARNING("✓ No test notifications found or dry run"))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Notification bulk delete failed: {e}'))
            traceback.print_exc()

    def test_admin_bulk_delete_action(self):
        """Test the actual Django admin bulk delete action"""
        self.stdout.write('\n' + self.style.WARNING('Testing Django Admin Bulk Delete Action'))
        self.stdout.write('-' * 40)
        
        try:
            # Create a mock request
            factory = RequestFactory()
            request = factory.post('/admin/')
            
            # Add required middleware attributes
            request.session = {}
            request.user = User.objects.filter(is_superuser=True).first()
            
            if not request.user:
                self.stdout.write(self.style.ERROR('No superuser found for testing'))
                return
            
            # Test with a small queryset
            from files.models import Notification
            test_notifications = Notification.objects.filter(
                message__startswith='Test notification'
            )[:2]
            
            if test_notifications.exists():
                # Get the admin class
                from files.admin import NotificationAdmin
                admin_instance = NotificationAdmin(Notification, site)
                
                # Test the delete action
                result = delete_selected(admin_instance, request, test_notifications)
                self.stdout.write(self.style.SUCCESS('✓ Admin bulk delete action completed'))
            else:
                self.stdout.write(self.style.WARNING('No test notifications for admin action test'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Admin bulk delete action failed: {e}'))
            traceback.print_exc()
