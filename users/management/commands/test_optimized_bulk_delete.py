"""
Management command to test the optimized bulk delete functionality.
"""
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from django.test import RequestFactory
from django.contrib.admin.sites import site
import time
import traceback

User = get_user_model()


class Command(BaseCommand):
    help = 'Test optimized bulk delete functionality'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test-users',
            action='store_true',
            help='Test optimized user bulk delete'
        )
        parser.add_argument(
            '--test-projects',
            action='store_true',
            help='Test optimized project bulk delete'
        )
        parser.add_argument(
            '--compare-performance',
            action='store_true',
            help='Compare optimized vs standard bulk delete performance'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Testing Optimized Bulk Delete Functionality'))
        self.stdout.write('=' * 60)
        
        if options['test_users']:
            self.test_optimized_user_bulk_delete()
        
        if options['test_projects']:
            self.test_optimized_project_bulk_delete()
        
        if options['compare_performance']:
            self.compare_bulk_delete_performance()

    def test_optimized_user_bulk_delete(self):
        """Test the optimized user bulk delete action"""
        self.stdout.write('\n' + self.style.WARNING('Testing Optimized User Bulk Delete'))
        self.stdout.write('-' * 40)
        
        try:
            # Get test users
            test_users = User.objects.filter(username__startswith='test_bulk_user_')
            if not test_users.exists():
                self.stdout.write(self.style.WARNING('No test users found. Run test_bulk_delete --create-test-data first.'))
                return
            
            user_count = test_users.count()
            self.stdout.write(f"Found {user_count} test users for optimized bulk delete")
            
            # Create a mock request for the admin action
            factory = RequestFactory()
            request = factory.post('/admin/')
            request.user = User.objects.filter(is_superuser=True).first()
            
            if not request.user:
                self.stdout.write(self.style.ERROR('No superuser found for testing'))
                return
            
            # Import and test the optimized bulk delete action
            from users.admin_actions import optimized_bulk_delete_users
            from users.admin import CustomUserAdmin
            
            # Create admin instance
            admin_instance = CustomUserAdmin(User, site)
            
            # Test the optimized bulk delete
            start_time = time.time()
            
            try:
                optimized_bulk_delete_users(admin_instance, request, test_users)
                delete_time = (time.time() - start_time) * 1000
                
                self.stdout.write(self.style.SUCCESS(
                    f"✓ Optimized bulk delete completed in {delete_time:.2f}ms"
                ))
                
                # Verify users were deleted
                remaining_users = User.objects.filter(username__startswith='test_bulk_user_')
                if not remaining_users.exists():
                    self.stdout.write(self.style.SUCCESS("✓ All test users successfully deleted"))
                else:
                    self.stdout.write(self.style.WARNING(f"⚠ {remaining_users.count()} users still remain"))
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"✗ Optimized bulk delete failed: {e}"))
                traceback.print_exc()
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Test setup failed: {e}'))
            traceback.print_exc()

    def test_optimized_project_bulk_delete(self):
        """Test the optimized project bulk delete action"""
        self.stdout.write('\n' + self.style.WARNING('Testing Optimized Project Bulk Delete'))
        self.stdout.write('-' * 40)
        
        try:
            from files.models import Project
            
            test_projects = Project.objects.filter(name__startswith='Test Bulk Project')
            if not test_projects.exists():
                self.stdout.write(self.style.WARNING('No test projects found. Run test_bulk_delete --create-test-data first.'))
                return
            
            project_count = test_projects.count()
            self.stdout.write(f"Found {project_count} test projects for optimized bulk delete")
            
            # Create a mock request for the admin action
            factory = RequestFactory()
            request = factory.post('/admin/')
            request.user = User.objects.filter(is_superuser=True).first()
            
            if not request.user:
                self.stdout.write(self.style.ERROR('No superuser found for testing'))
                return
            
            # Import and test the optimized bulk delete action
            from users.admin_actions import optimized_bulk_delete_projects
            from files.admin import ProjectAdmin
            
            # Create admin instance
            admin_instance = ProjectAdmin(Project, site)
            
            # Test the optimized bulk delete
            start_time = time.time()
            
            try:
                optimized_bulk_delete_projects(admin_instance, request, test_projects)
                delete_time = (time.time() - start_time) * 1000
                
                self.stdout.write(self.style.SUCCESS(
                    f"✓ Optimized project bulk delete completed in {delete_time:.2f}ms"
                ))
                
                # Verify projects were deleted
                remaining_projects = Project.objects.filter(name__startswith='Test Bulk Project')
                if not remaining_projects.exists():
                    self.stdout.write(self.style.SUCCESS("✓ All test projects successfully deleted"))
                else:
                    self.stdout.write(self.style.WARNING(f"⚠ {remaining_projects.count()} projects still remain"))
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"✗ Optimized project bulk delete failed: {e}"))
                traceback.print_exc()
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Test setup failed: {e}'))
            traceback.print_exc()

    def compare_bulk_delete_performance(self):
        """Compare performance between standard and optimized bulk delete"""
        self.stdout.write('\n' + self.style.WARNING('Comparing Bulk Delete Performance'))
        self.stdout.write('-' * 40)
        
        try:
            # Create test data for comparison
            self.stdout.write("Creating test data for performance comparison...")
            
            # Create test users for standard delete
            standard_users = []
            for i in range(3):
                username = f'standard_test_user_{i}'
                if not User.objects.filter(username=username).exists():
                    user = User.objects.create_user(
                        username=username,
                        email=f'{username}@test.com',
                        password='testpass123',
                        user_type='client'
                    )
                    standard_users.append(user)
            
            # Create test users for optimized delete
            optimized_users = []
            for i in range(3):
                username = f'optimized_test_user_{i}'
                if not User.objects.filter(username=username).exists():
                    user = User.objects.create_user(
                        username=username,
                        email=f'{username}@test.com',
                        password='testpass123',
                        user_type='client'
                    )
                    optimized_users.append(user)
            
            self.stdout.write(f"Created {len(standard_users)} users for standard delete")
            self.stdout.write(f"Created {len(optimized_users)} users for optimized delete")
            
            # Test standard bulk delete
            if standard_users:
                self.stdout.write("\nTesting standard bulk delete...")
                start_time = time.time()
                
                standard_queryset = User.objects.filter(username__startswith='standard_test_user_')
                standard_queryset.delete()
                
                standard_time = (time.time() - start_time) * 1000
                self.stdout.write(f"Standard bulk delete: {standard_time:.2f}ms")
            
            # Test optimized bulk delete
            if optimized_users:
                self.stdout.write("\nTesting optimized bulk delete...")
                
                factory = RequestFactory()
                request = factory.post('/admin/')
                request.user = User.objects.filter(is_superuser=True).first()
                
                if request.user:
                    from users.admin_actions import optimized_bulk_delete_users
                    from users.admin import CustomUserAdmin
                    
                    admin_instance = CustomUserAdmin(User, site)
                    optimized_queryset = User.objects.filter(username__startswith='optimized_test_user_')
                    
                    start_time = time.time()
                    optimized_bulk_delete_users(admin_instance, request, optimized_queryset)
                    optimized_time = (time.time() - start_time) * 1000
                    
                    self.stdout.write(f"Optimized bulk delete: {optimized_time:.2f}ms")
                    
                    # Calculate improvement
                    if 'standard_time' in locals():
                        improvement = ((standard_time - optimized_time) / standard_time) * 100
                        self.stdout.write(self.style.SUCCESS(
                            f"\n✓ Performance improvement: {improvement:.1f}%"
                        ))
                else:
                    self.stdout.write(self.style.ERROR("No superuser found for optimized test"))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Performance comparison failed: {e}'))
            traceback.print_exc()

    def cleanup_test_data(self):
        """Clean up any remaining test data"""
        self.stdout.write('\n' + self.style.WARNING('Cleaning up test data'))
        self.stdout.write('-' * 40)
        
        try:
            # Clean up any remaining test users
            test_users = User.objects.filter(
                username__regex=r'^(test_bulk_user_|standard_test_user_|optimized_test_user_)'
            )
            if test_users.exists():
                count = test_users.count()
                test_users.delete()
                self.stdout.write(f"✓ Cleaned up {count} test users")
            
            # Clean up test projects
            from files.models import Project
            test_projects = Project.objects.filter(name__startswith='Test Bulk Project')
            if test_projects.exists():
                count = test_projects.count()
                test_projects.delete()
                self.stdout.write(f"✓ Cleaned up {count} test projects")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Cleanup failed: {e}'))
