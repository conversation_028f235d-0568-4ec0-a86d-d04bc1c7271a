"""
Management command to test email routing functionality.
"""
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.conf import settings
from users.utils import send_email_with_logging
from users.models import EmailLog
import time

User = get_user_model()


class Command(BaseCommand):
    help = 'Test email routing functionality to ensure emails go to correct recipients'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test-welcome',
            action='store_true',
            help='Test welcome email routing'
        )
        parser.add_argument(
            '--test-user-creation',
            action='store_true',
            help='Test user creation email flow'
        )
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up test users and email logs'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Email Routing Test'))
        self.stdout.write('=' * 50)
        
        if options['cleanup']:
            self.cleanup_test_data()
        
        if options['test_welcome']:
            self.test_welcome_email_routing()
        
        if options['test_user_creation']:
            self.test_user_creation_flow()

    def test_welcome_email_routing(self):
        """Test welcome email routing to ensure it goes to user's email"""
        self.stdout.write('\n' + self.style.WARNING('Testing Welcome Email Routing'))
        self.stdout.write('-' * 40)
        
        # Create test user without triggering signals
        test_email = '<EMAIL>'
        test_username = 'test_welcome_routing'
        
        # Clean up existing test user
        User.objects.filter(username=test_username).delete()
        
        try:
            # Create user manually to avoid signal triggers
            test_user = User(
                username=test_username,
                email=test_email,
                first_name='Test',
                last_name='User',
                user_type='client'
            )
            test_user.set_password('testpass123')
            test_user.save()
            
            self.stdout.write(f"✓ Created test user: {test_user.username}")
            self.stdout.write(f"  Email: {test_user.email}")
            
            # Test email sending function directly
            context = {
                'user': test_user,
                'site_url': settings.SITE_URL,
                'current_year': 2025,
            }
            
            # Send test welcome email
            self.stdout.write("\nSending test welcome email...")
            
            result = send_email_with_logging(
                recipient=test_user,
                email_type='welcome',
                subject='Test Welcome Email - Routing Check',
                template_name='emails/welcome_email.html',
                context=context,
                force_send=True
            )
            
            if result:
                self.stdout.write(self.style.SUCCESS("✓ Email sending function returned success"))
            else:
                self.stdout.write(self.style.ERROR("✗ Email sending function returned failure"))
            
            # Check email log
            email_log = EmailLog.objects.filter(
                recipient=test_user,
                email_type='welcome'
            ).order_by('-sent_at').first()
            
            if email_log:
                self.stdout.write(f"\n📧 Email Log Details:")
                self.stdout.write(f"   Recipient: {email_log.recipient.email}")
                self.stdout.write(f"   Subject: {email_log.subject}")
                self.stdout.write(f"   Status: {email_log.status}")
                if email_log.error_message:
                    self.stdout.write(f"   Error: {email_log.error_message}")
                
                # Verify recipient is correct
                if email_log.recipient.email == test_email:
                    self.stdout.write(self.style.SUCCESS("✓ Email routed to correct recipient"))
                else:
                    self.stdout.write(self.style.ERROR(
                        f"✗ Email routed incorrectly! Expected: {test_email}, Got: {email_log.recipient.email}"
                    ))
            else:
                self.stdout.write(self.style.WARNING("⚠ No email log found"))
            
            # Verify email settings
            self.stdout.write(f"\n⚙️ Email Configuration:")
            self.stdout.write(f"   From Email: {settings.DEFAULT_FROM_EMAIL}")
            self.stdout.write(f"   Contact Email: {settings.CONTACT_EMAIL}")
            self.stdout.write(f"   SMTP Host: {settings.EMAIL_HOST}")
            self.stdout.write(f"   SMTP Port: {settings.EMAIL_PORT}")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Test failed: {e}"))

    def test_user_creation_flow(self):
        """Test the complete user creation flow including signals"""
        self.stdout.write('\n' + self.style.WARNING('Testing User Creation Flow'))
        self.stdout.write('-' * 40)
        
        test_email = '<EMAIL>'
        test_username = 'test_creation_flow'
        
        # Clean up existing test user
        User.objects.filter(username=test_username).delete()
        
        try:
            self.stdout.write(f"Creating user with email: {test_email}")
            
            # Count existing email logs
            initial_email_count = EmailLog.objects.filter(email_type='welcome').count()
            
            # Create user (this will trigger signals)
            test_user = User.objects.create_user(
                username=test_username,
                email=test_email,
                password='testpass123',
                first_name='Test',
                last_name='Creation',
                user_type='client'
            )
            
            self.stdout.write(f"✓ User created: {test_user.username} ({test_user.email})")
            
            # Wait a moment for signals to process
            time.sleep(2)
            
            # Check for welcome emails
            welcome_emails = EmailLog.objects.filter(
                recipient=test_user,
                email_type='welcome'
            ).order_by('-sent_at')
            
            self.stdout.write(f"\n📧 Welcome Emails Found: {welcome_emails.count()}")
            
            for i, email in enumerate(welcome_emails, 1):
                self.stdout.write(f"   Email {i}:")
                self.stdout.write(f"     To: {email.recipient.email}")
                self.stdout.write(f"     Subject: {email.subject}")
                self.stdout.write(f"     Status: {email.status}")
                self.stdout.write(f"     Sent: {email.sent_at}")
                if email.error_message:
                    self.stdout.write(f"     Error: {email.error_message}")
                
                # Verify recipient
                if email.recipient.email == test_email:
                    self.stdout.write(self.style.SUCCESS("     ✓ Correct recipient"))
                else:
                    self.stdout.write(self.style.ERROR(
                        f"     ✗ Wrong recipient! Expected: {test_email}"
                    ))
            
            # Check for notifications
            from files.models import Notification
            notifications = Notification.objects.filter(
                user=test_user,
                notification_type='welcome'
            )
            
            self.stdout.write(f"\n🔔 Welcome Notifications Found: {notifications.count()}")
            
            for notification in notifications:
                self.stdout.write(f"   Notification: {notification.message}")
                self.stdout.write(f"   User: {notification.user.email}")
            
            # Summary
            total_emails = EmailLog.objects.filter(email_type='welcome').count()
            new_emails = total_emails - initial_email_count
            
            self.stdout.write(f"\n📊 Summary:")
            self.stdout.write(f"   New welcome emails: {new_emails}")
            self.stdout.write(f"   All routed to user email: {test_email}")
            
            if new_emails == 1:
                self.stdout.write(self.style.SUCCESS("✓ Single welcome email sent (no duplicates)"))
            elif new_emails > 1:
                self.stdout.write(self.style.WARNING(f"⚠ Multiple welcome emails sent ({new_emails})"))
            else:
                self.stdout.write(self.style.ERROR("✗ No welcome emails sent"))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Test failed: {e}"))

    def cleanup_test_data(self):
        """Clean up test users and email logs"""
        self.stdout.write('\n' + self.style.WARNING('Cleaning Up Test Data'))
        self.stdout.write('-' * 40)
        
        try:
            # Delete test users
            test_users = User.objects.filter(
                username__in=[
                    'test_welcome_routing',
                    'test_creation_flow',
                    'test_email_routing'
                ]
            )
            
            user_count = test_users.count()
            if user_count > 0:
                test_users.delete()
                self.stdout.write(f"✓ Deleted {user_count} test users")
            
            # Delete test email logs
            test_emails = EmailLog.objects.filter(
                subject__icontains='Test Welcome Email'
            )
            
            email_count = test_emails.count()
            if email_count > 0:
                test_emails.delete()
                self.stdout.write(f"✓ Deleted {email_count} test email logs")
            
            if user_count == 0 and email_count == 0:
                self.stdout.write("✓ No test data to clean up")
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Cleanup failed: {e}"))
