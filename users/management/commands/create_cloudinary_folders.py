from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from files.storage import CompletoPLUSCloudinaryStorage

User = get_user_model()

class Command(BaseCommand):
    help = 'Create Cloudinary folders for all existing users'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user-id',
            type=int,
            help='Create folder for a specific user ID',
        )

    def handle(self, *args, **options):
        storage = CompletoPLUSCloudinaryStorage()
        
        if options['user_id']:
            # Create folder for a specific user
            try:
                user = User.objects.get(id=options['user_id'])
                success = storage.create_user_folder(user)
                if success:
                    self.stdout.write(self.style.SUCCESS(
                        f'Successfully created Cloudinary folder for user {user.username} (ID: {user.id})'
                    ))
                else:
                    self.stdout.write(self.style.ERROR(
                        f'Failed to create Cloudinary folder for user {user.username} (ID: {user.id})'
                    ))
            except User.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'User with ID {options["user_id"]} does not exist'))
        else:
            # Create folders for all users
            users = User.objects.all()
            self.stdout.write(f'Creating Cloudinary folders for {users.count()} users...')
            
            success_count = 0
            failure_count = 0
            
            for user in users:
                success = storage.create_user_folder(user)
                if success:
                    success_count += 1
                    self.stdout.write(self.style.SUCCESS(
                        f'Created folder for {user.username} (ID: {user.id})'
                    ))
                else:
                    failure_count += 1
                    self.stdout.write(self.style.ERROR(
                        f'Failed to create folder for {user.username} (ID: {user.id})'
                    ))
            
            self.stdout.write(self.style.SUCCESS(
                f'Finished creating folders. Success: {success_count}, Failures: {failure_count}'
            ))
