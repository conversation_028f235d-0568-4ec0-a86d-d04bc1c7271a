"""
Management command to analyze performance issues in CompletoPLUS Django application.
"""
from django.core.management.base import BaseCommand
from django.conf import settings
from django.db import connection
from django.test import RequestFactory
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
import time
import sys
import traceback

User = get_user_model()


class Command(BaseCommand):
    help = 'Analyze performance issues in CompletoPLUS application'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test',
            type=str,
            choices=['database', 'views', 'middleware', 'queries', 'all'],
            default='all',
            help='Type of performance test to run'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output'
        )

    def handle(self, *args, **options):
        test_type = options['test']
        verbose = options['verbose']

        self.stdout.write(self.style.SUCCESS('CompletoPLUS Performance Analysis'))
        self.stdout.write('=' * 60)
        
        if test_type in ['database', 'all']:
            self.test_database_performance(verbose)
        
        if test_type in ['queries', 'all']:
            self.analyze_query_patterns(verbose)
        
        if test_type in ['views', 'all']:
            self.test_view_performance(verbose)
        
        if test_type in ['middleware', 'all']:
            self.test_middleware_performance(verbose)

    def test_database_performance(self, verbose=False):
        """Test database connection and query performance"""
        self.stdout.write('\n' + self.style.WARNING('Database Performance Analysis'))
        self.stdout.write('-' * 40)
        
        # Test database connection
        start_time = time.time()
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
            connection_time = (time.time() - start_time) * 1000
            self.stdout.write(f"✓ Database connection: {connection_time:.2f}ms")
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Database connection failed: {e}"))
            return

        # Test basic queries
        queries = [
            ("User count", "SELECT COUNT(*) FROM users_customuser"),
            ("Project count", "SELECT COUNT(*) FROM files_project"),
            ("File count", "SELECT COUNT(*) FROM files_file"),
            ("Notification count", "SELECT COUNT(*) FROM files_notification"),
        ]
        
        for query_name, query in queries:
            start_time = time.time()
            try:
                with connection.cursor() as cursor:
                    cursor.execute(query)
                    result = cursor.fetchone()
                query_time = (time.time() - start_time) * 1000
                self.stdout.write(f"✓ {query_name}: {result[0]} records ({query_time:.2f}ms)")
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"✗ {query_name} failed: {e}"))

        # Database settings analysis
        db_config = settings.DATABASES['default']
        self.stdout.write(f"\nDatabase Configuration:")
        self.stdout.write(f"  Engine: {db_config.get('ENGINE', 'Not set')}")
        self.stdout.write(f"  Connection Max Age: {db_config.get('CONN_MAX_AGE', 'Not set')}")
        self.stdout.write(f"  Health Checks: {db_config.get('CONN_HEALTH_CHECKS', 'Not set')}")

    def analyze_query_patterns(self, verbose=False):
        """Analyze common query patterns for N+1 problems"""
        self.stdout.write('\n' + self.style.WARNING('Query Pattern Analysis'))
        self.stdout.write('-' * 40)
        
        from files.models import Project, File, Notification
        from users.models import CustomUser
        
        # Test for N+1 query problems
        issues = []
        
        # Check dashboard queries
        try:
            # Simulate admin dashboard query pattern
            start_time = time.time()
            projects = list(Project.objects.all()[:10])
            for project in projects:
                # This would cause N+1 if not optimized
                _ = project.client.username
                _ = project.files.count()
            query_time = (time.time() - start_time) * 1000
            
            if query_time > 100:  # More than 100ms is concerning
                issues.append(f"Dashboard project queries slow: {query_time:.2f}ms")
            else:
                self.stdout.write(f"✓ Dashboard project queries: {query_time:.2f}ms")
        except Exception as e:
            issues.append(f"Dashboard query error: {e}")

        # Check file listing queries
        try:
            start_time = time.time()
            files = list(File.objects.all()[:20])
            for file in files:
                _ = file.project.name
                _ = file.owner.username
            query_time = (time.time() - start_time) * 1000
            
            if query_time > 150:
                issues.append(f"File listing queries slow: {query_time:.2f}ms")
            else:
                self.stdout.write(f"✓ File listing queries: {query_time:.2f}ms")
        except Exception as e:
            issues.append(f"File listing query error: {e}")

        # Check notification queries
        try:
            start_time = time.time()
            notifications = list(Notification.objects.all()[:10])
            for notification in notifications:
                _ = notification.user.username
                if notification.project:
                    _ = notification.project.name
            query_time = (time.time() - start_time) * 1000
            
            if query_time > 50:
                issues.append(f"Notification queries slow: {query_time:.2f}ms")
            else:
                self.stdout.write(f"✓ Notification queries: {query_time:.2f}ms")
        except Exception as e:
            issues.append(f"Notification query error: {e}")

        if issues:
            self.stdout.write(self.style.ERROR("\nQuery Performance Issues Found:"))
            for issue in issues:
                self.stdout.write(f"  ✗ {issue}")
        else:
            self.stdout.write(self.style.SUCCESS("\n✓ No major query performance issues detected"))

    def test_view_performance(self, verbose=False):
        """Test view performance"""
        self.stdout.write('\n' + self.style.WARNING('View Performance Analysis'))
        self.stdout.write('-' * 40)
        
        from users.views import dashboard
        from files.views import project_list, my_files
        
        factory = RequestFactory()
        
        # Create a test user if none exists
        try:
            user = User.objects.first()
            if not user:
                self.stdout.write("No users found for testing")
                return
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error getting test user: {e}"))
            return

        # Test dashboard view
        try:
            request = factory.get('/dashboard/')
            request.user = user
            
            start_time = time.time()
            response = dashboard(request)
            view_time = (time.time() - start_time) * 1000
            
            if view_time > 500:  # More than 500ms is slow
                self.stdout.write(self.style.ERROR(f"✗ Dashboard view slow: {view_time:.2f}ms"))
            else:
                self.stdout.write(f"✓ Dashboard view: {view_time:.2f}ms")
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Dashboard view error: {e}"))

        # Test project list view
        try:
            request = factory.get('/files/projects/')
            request.user = user
            
            start_time = time.time()
            response = project_list(request)
            view_time = (time.time() - start_time) * 1000
            
            if view_time > 300:
                self.stdout.write(self.style.ERROR(f"✗ Project list view slow: {view_time:.2f}ms"))
            else:
                self.stdout.write(f"✓ Project list view: {view_time:.2f}ms")
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"✗ Project list view error: {e}"))

    def test_middleware_performance(self, verbose=False):
        """Test middleware performance impact"""
        self.stdout.write('\n' + self.style.WARNING('Middleware Performance Analysis'))
        self.stdout.write('-' * 40)
        
        middleware_classes = settings.MIDDLEWARE
        self.stdout.write(f"Total middleware classes: {len(middleware_classes)}")
        
        for i, middleware in enumerate(middleware_classes, 1):
            self.stdout.write(f"  {i}. {middleware}")
        
        # Check for potentially slow middleware
        slow_middleware = [
            'debug_toolbar',
            'silk',
            'django_extensions',
        ]
        
        found_slow = []
        for middleware in middleware_classes:
            for slow in slow_middleware:
                if slow in middleware.lower():
                    found_slow.append(middleware)
        
        if found_slow:
            self.stdout.write(self.style.WARNING("\nPotentially slow middleware detected:"))
            for middleware in found_slow:
                self.stdout.write(f"  ⚠ {middleware}")
        else:
            self.stdout.write(self.style.SUCCESS("\n✓ No obviously slow middleware detected"))

        # Check custom middleware
        custom_middleware = [m for m in middleware_classes if 'fileshare.middleware' in m]
        if custom_middleware:
            self.stdout.write(f"\nCustom middleware found:")
            for middleware in custom_middleware:
                self.stdout.write(f"  • {middleware}")
