from django import forms
from django.contrib.auth.forms import UserCreationForm, UserChangeForm, AuthenticationForm, PasswordResetForm, SetPasswordForm
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from .models import CustomUser, DashboardSettings, Testimonial, Message


class CustomAuthenticationForm(AuthenticationForm):
    """Custom authentication form with enhanced styling and functionality"""
    username = forms.CharField(
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'Username or Email',
                'autofocus': True,
                'autocomplete': 'username',
            }
        )
    )
    password = forms.CharField(
        widget=forms.PasswordInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'Password',
                'autocomplete': 'current-password',
            }
        )
    )
    remember_me = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
            }
        )
    )

    error_messages = {
        'invalid_login': _(
            "Please enter a correct username/email and password. Note that both "
            "fields may be case-sensitive."
        ),
        'inactive': _("This account is inactive."),
    }

    def clean(self):
        cleaned_data = super().clean()

        # Check if there's an account locked message from the backend
        if hasattr(self.request, 'account_locked_message'):
            raise ValidationError(
                self.request.account_locked_message,
                code='account_locked'
            )

        # Check if there's a login attempts message from the backend
        if hasattr(self.request, 'login_attempts_message'):
            raise ValidationError(
                self.request.login_attempts_message,
                code='invalid_login'
            )

        return cleaned_data


class CustomUserCreationForm(UserCreationForm):
    """Form for user registration"""
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'Email Address',
                'autocomplete': 'email',
            }
        )
    )
    first_name = forms.CharField(
        required=True,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'First Name',
                'autocomplete': 'given-name',
            }
        )
    )
    last_name = forms.CharField(
        required=True,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'Last Name',
                'autocomplete': 'family-name',
            }
        )
    )
    username = forms.CharField(
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'Username',
                'autocomplete': 'username',
            }
        )
    )
    password1 = forms.CharField(
        widget=forms.PasswordInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'Password',
                'autocomplete': 'new-password',
            }
        )
    )
    password2 = forms.CharField(
        widget=forms.PasswordInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'Confirm Password',
                'autocomplete': 'new-password',
            }
        )
    )
    terms_agreement = forms.BooleanField(
        required=True,
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
            }
        )
    )

    class Meta:
        model = CustomUser
        fields = ('username', 'email', 'first_name', 'last_name',
                  'password1', 'password2', 'user_type')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make the user_type field not required for regular registration
        # Admins can be created via the admin interface
        self.fields['user_type'].initial = 'client'
        self.fields['user_type'].widget = forms.HiddenInput()

    def clean_username(self):
        """Validate username to prevent conflicts with admin account"""
        username = self.cleaned_data.get('username')
        if username:
            # Check for reserved admin username (case-insensitive)
            if username.lower() == 'completoplus':
                raise ValidationError(
                    "This username is reserved and cannot be used for registration. "
                    "Please choose a different username."
                )
            # Check if username already exists
            if CustomUser.objects.filter(username=username).exists():
                raise ValidationError("This username is already taken.")
        return username

    def clean_email(self):
        """Validate email to prevent conflicts with admin account"""
        email = self.cleaned_data.get('email')
        if email:
            # Check for reserved admin email (case-insensitive)
            if email.lower() == '<EMAIL>':
                raise ValidationError(
                    "This email address is reserved and cannot be used for registration. "
                    "Please use a different email address."
                )
            # Check if email already exists
            if CustomUser.objects.filter(email=email).exists():
                raise ValidationError("This email address is already in use.")
        return email

    def save(self, commit=True):
        """
        Note: This method is not used in the registration view anymore.
        We're keeping it for compatibility with other parts of the code.
        The registration view now creates users directly.
        """
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.company = ''
        user.position = ''
        user.receive_marketing_emails = False
        user.user_type = 'client'

        if commit:
            user.save()

        return user


class CustomUserChangeForm(UserChangeForm):
    """Form for updating user profile"""
    password = None  # Remove password field from the form

    class Meta:
        model = CustomUser
        fields = ('username', 'email', 'first_name', 'last_name', 'profile_picture',
                  'bio', 'phone_number', 'address', 'company', 'position')
        widgets = {
            'username': forms.TextInput(attrs={'class': 'form-control'}),
            'email': forms.EmailInput(attrs={'class': 'form-control'}),
            'first_name': forms.TextInput(attrs={'class': 'form-control'}),
            'last_name': forms.TextInput(attrs={'class': 'form-control'}),
            'profile_picture': forms.FileInput(attrs={'class': 'form-control'}),
            'bio': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control'}),
            'address': forms.Textarea(attrs={'class': 'form-control', 'rows': 2}),
            'company': forms.TextInput(attrs={'class': 'form-control'}),
            'position': forms.TextInput(attrs={'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add help texts
        self.fields['email'].help_text = 'Enter a valid email address'
        self.fields['first_name'].help_text = 'Enter your first name'
        self.fields['last_name'].help_text = 'Enter your last name'
        self.fields['profile_picture'].help_text = 'Upload a profile picture (JPG, PNG)'
        self.fields['bio'].help_text = 'Tell us about yourself'
        self.fields['phone_number'].help_text = 'Enter your contact number'
        self.fields['company'].help_text = 'Enter your company name'
        self.fields['position'].help_text = 'Enter your job title or position'


class CustomPasswordResetForm(PasswordResetForm):
    """Custom password reset form with enhanced styling"""
    email = forms.EmailField(
        widget=forms.EmailInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'Email Address',
                'autocomplete': 'email',
            }
        )
    )

    def get_users(self, email):
        """Given an email, return matching user(s) who should receive a reset.
        This allows subclasses to more easily customize the default behavior.

        Override to handle UUID conversion issues.
        """
        try:
            # Use filter instead of get to avoid UUID conversion issues
            active_users = CustomUser.objects.filter(
                email__iexact=email, is_active=True
            )
            # Log the users found for debugging
            print(f"Found {active_users.count()} users with email {email}")
            for user in active_users:
                print(
                    f"User: {user.username}, ID: {user.id}, Email: {user.email}")
            return active_users
        except Exception as e:
            print(f"Password reset error: {e}")
            print(f"Error in password reset get_users: {e}")
            # Return an empty queryset if there's an error
            return CustomUser.objects.none()


class CustomSetPasswordForm(SetPasswordForm):
    """Custom set password form with enhanced styling"""
    new_password1 = forms.CharField(
        widget=forms.PasswordInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'New Password',
                'autocomplete': 'new-password',
            }
        )
    )
    new_password2 = forms.CharField(
        widget=forms.PasswordInput(
            attrs={
                'class': 'form-control',
                'placeholder': 'Confirm New Password',
                'autocomplete': 'new-password',
            }
        )
    )


class EmailPreferencesForm(forms.ModelForm):
    """Form for updating user email preferences"""
    class Meta:
        model = CustomUser
        fields = ('receive_project_emails', 'receive_file_emails',
                  'receive_comment_emails', 'receive_marketing_emails')
        widgets = {
            'receive_project_emails': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'receive_file_emails': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'receive_comment_emails': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'receive_marketing_emails': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }


class DashboardSettingsForm(forms.ModelForm):
    """Form for customizing dashboard settings"""

    class Meta:
        model = DashboardSettings
        fields = [
            'layout', 'theme', 'color_scheme', 'items_per_page',
            'show_recent_files', 'show_projects',
            'show_statistics', 'show_activity',
            'email_notifications'
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Add Bootstrap classes to form fields
        for field_name, field in self.fields.items():
            if isinstance(field.widget, forms.CheckboxInput):
                field.widget.attrs['class'] = 'form-check-input'
            else:
                field.widget.attrs['class'] = 'form-control'

        # Add help texts
        self.fields['layout'].help_text = 'Choose how your dashboard is organized'
        self.fields['theme'].help_text = 'Choose light or dark mode, or follow system settings'
        self.fields['color_scheme'].help_text = 'Choose your preferred color scheme'
        self.fields['items_per_page'].help_text = 'Number of items to show per page in lists'

        # Group related fields
        self.widget_fields = ['show_recent_files', 'show_notifications',
                              'show_projects', 'show_statistics', 'show_activity']
        self.notification_fields = ['email_notifications']


class MessageForm(forms.ModelForm):
    """Form for sending messages between users"""

    recipient = forms.ModelChoiceField(
        queryset=CustomUser.objects.all(),
        widget=forms.Select(attrs={
            'class': 'form-select',
        }),
        required=True
    )

    class Meta:
        model = Message
        fields = ['recipient', 'subject', 'content']
        widgets = {
            'subject': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Message Subject',
            }),
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 5,
                'placeholder': 'Type your message here...',
            }),
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Add help texts
        self.fields['recipient'].help_text = 'Select the recipient of your message'
        self.fields['subject'].help_text = 'Enter a subject for your message'
        self.fields['content'].help_text = 'Type your message content'

        # Filter recipients to exclude the current user
        if user:
            self.fields['recipient'].queryset = CustomUser.objects.exclude(
                id=user.id)


class TestimonialForm(forms.ModelForm):
    """Form for clients to submit testimonials"""

    class Meta:
        model = Testimonial
        fields = ['content', 'rating', 'service_type']
        widgets = {
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Share your experience with CompletoPLUS...',
            }),
            'rating': forms.RadioSelect(attrs={
                'class': 'rating-input',
            }),
            'service_type': forms.Select(attrs={
                'class': 'form-select',
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add help texts
        self.fields['content'].help_text = 'Tell us about your experience with our services'
        self.fields['rating'].help_text = 'Rate your overall satisfaction'
        self.fields['service_type'].help_text = 'Select the type of service you received'
