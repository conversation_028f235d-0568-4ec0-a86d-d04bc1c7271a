from django.contrib.auth import get_user_model
from django.conf import settings
from files.models import Notification
from .utils import send_email_with_logging

User = get_user_model()


def notify_admins_of_new_testimonial(testimonial):
    """
    Notify all admin users about a new testimonial submission

    Args:
        testimonial: The newly created testimonial
    """
    # Get all admin users
    admin_users = User.objects.filter(user_type='admin')

    # Create a notification for each admin
    for admin in admin_users:
        # Create notification
        notification = Notification.objects.create(
            user=admin,
            project=None,  # Testimonials don't have an associated project
            message=f'New testimonial submitted by {testimonial.client.get_full_name() or testimonial.client.username}',
            notification_type='system',
            is_read=False,
            data={
                'testimonial_id': str(testimonial.id),
                'client_name': testimonial.client.get_full_name() or testimonial.client.username,
                'client_id': str(testimonial.client.id),
                'rating': testimonial.rating,
                'service_type': testimonial.get_service_type_display(),
                'content': testimonial.content,
                'created_at': testimonial.created_at.isoformat() if testimonial.created_at else None,
                'notification_type': 'new_testimonial'
            }
        )

        # Send email notification
        context = {
            'admin_name': admin.get_full_name() or admin.username,
            'client_name': testimonial.client.get_full_name() or testimonial.client.username,
            'testimonial_content': testimonial.content,
            'testimonial_rating': testimonial.rating,
            'testimonial_service': testimonial.get_service_type_display(),
            'admin_url': f"{settings.SITE_URL}/completoplus-admin/users/testimonial/{testimonial.id}/change/",
            'site_name': 'CompletoPLUS'
        }

        send_email_with_logging(
            recipient=admin,
            email_type='new_testimonial',
            subject=f'New testimonial submitted by {testimonial.client.get_full_name() or testimonial.client.username}',
            template_name='emails/new_testimonial_notification.html',
            context=context
        )
