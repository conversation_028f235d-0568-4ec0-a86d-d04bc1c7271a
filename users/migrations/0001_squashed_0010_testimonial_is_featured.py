# Generated manually

import uuid
from django.db import migrations, models
import django.db.models.deletion
from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
import django.utils.timezone


class Migration(migrations.Migration):

    replaces = [
        ('users', '0001_initial'),
        ('users', '0002_dashboardsettings'),
        ('users', '0003_remove_dashboardsettings_browser_notifications_and_more'),
        ('users', '0004_emaillog'),
        ('users', '0005_customuser_account_locked_and_more'),
        ('users', '0006_fix_uuid_field'),
        ('users', '0007_testimonial'),
        ('users', '0008_remove_dashboardsettings_show_notifications'),
        ('users', '0009_alter_emaillog_email_type'),
        ('users', '0010_testimonial_is_featured'),
    ]

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomUser',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('user_type', models.CharField(choices=[('admin', 'Administrator'), ('client', 'Client')], default='client', max_length=10, verbose_name='User Type')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
                ('account_locked', models.BooleanField(default=False, verbose_name='Account Locked')),
                ('account_locked_until', models.DateTimeField(blank=True, null=True, verbose_name='Account Locked Until')),
                ('account_verified', models.BooleanField(default=False, verbose_name='Account Verified')),
                ('address', models.TextField(blank=True, verbose_name='Address')),
                ('bio', models.TextField(blank=True, verbose_name='Bio')),
                ('company', models.CharField(blank=True, max_length=100, verbose_name='Company')),
                ('email_verified', models.BooleanField(default=False, verbose_name='Email Verified')),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='Last Login IP')),
                ('login_attempts', models.IntegerField(default=0, verbose_name='Login Attempts')),
                ('password_reset_expires', models.DateTimeField(blank=True, null=True, verbose_name='Password Reset Expires')),
                ('password_reset_token', models.CharField(blank=True, max_length=100, verbose_name='Password Reset Token')),
                ('phone_number', models.CharField(blank=True, max_length=20, verbose_name='Phone Number')),
                ('position', models.CharField(blank=True, max_length=100, verbose_name='Position')),
                ('profile_picture', models.ImageField(blank=True, null=True, upload_to='profile_pictures/', verbose_name='Profile Picture')),
                ('receive_comment_emails', models.BooleanField(default=True, verbose_name='Receive Comment Emails')),
                ('receive_file_emails', models.BooleanField(default=True, verbose_name='Receive File Emails')),
                ('receive_marketing_emails', models.BooleanField(default=False, verbose_name='Receive Marketing Emails')),
                ('receive_project_emails', models.BooleanField(default=True, verbose_name='Receive Project Emails')),
                ('verification_token', models.CharField(blank=True, max_length=100, verbose_name='Verification Token')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='DashboardSettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('layout', models.CharField(choices=[('default', 'Default'), ('compact', 'Compact'), ('expanded', 'Expanded')], default='default', max_length=20)),
                ('show_recent_files', models.BooleanField(default=True)),
                ('show_projects', models.BooleanField(default=True)),
                ('show_statistics', models.BooleanField(default=True)),
                ('show_activity', models.BooleanField(default=True)),
                ('theme', models.CharField(choices=[('light', 'Light'), ('dark', 'Dark'), ('auto', 'Auto (System)')], default='auto', max_length=20)),
                ('color_scheme', models.CharField(choices=[('blue', 'Blue'), ('green', 'Green'), ('purple', 'Purple'), ('orange', 'Orange'), ('red', 'Red')], default='blue', max_length=20)),
                ('items_per_page', models.IntegerField(choices=[(5, '5'), (10, '10'), (25, '25'), (50, '50'), (100, '100')], default=10)),
                ('email_notifications', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='dashboard_settings', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='EmailLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email_type', models.CharField(choices=[('welcome', 'Welcome Email'), ('progress_update', 'Progress Update'), ('password_reset', 'Password Reset'), ('account_change', 'Account Change'), ('project_invitation', 'Project Invitation'), ('file_shared', 'File Shared'), ('new_testimonial', 'New Testimonial'), ('other', 'Other')], max_length=20)),
                ('subject', models.CharField(max_length=255)),
                ('sent_at', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('success', 'Success'), ('failed', 'Failed')], max_length=20)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='received_emails', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-sent_at'],
            },
        ),
        migrations.CreateModel(
            name='Testimonial',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('content', models.TextField(verbose_name='Testimonial Content')),
                ('rating', models.IntegerField(choices=[(1, '1 - Poor'), (2, '2 - Fair'), (3, '3 - Good'), (4, '4 - Very Good'), (5, '5 - Excellent')], default=5)),
                ('service_type', models.CharField(choices=[('assignment', 'Assignment'), ('project', 'Project'), ('thesis', 'Thesis'), ('dissertation', 'Dissertation'), ('data_analysis', 'Data Analysis'), ('web_development', 'Web Development'), ('machine_learning', 'Machine Learning'), ('other', 'Other')], default='assignment', max_length=50)),
                ('is_approved', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='testimonials', to=settings.AUTH_USER_MODEL)),
                ('is_featured', models.BooleanField(default=False, verbose_name='Featured Testimonial')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
