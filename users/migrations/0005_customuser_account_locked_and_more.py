# Generated by Django 5.2 on 2025-04-15 04:31

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0004_emaillog'),
    ]

    operations = [
        migrations.AddField(
            model_name='customuser',
            name='account_locked',
            field=models.BooleanField(default=False, verbose_name='Account Locked'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='account_locked_until',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Account Locked Until'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='account_verified',
            field=models.BooleanField(default=False, verbose_name='Account Verified'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='address',
            field=models.TextField(blank=True, verbose_name='Address'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='bio',
            field=models.TextField(blank=True, verbose_name='Bio'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='company',
            field=models.CharField(blank=True, max_length=100, verbose_name='Company'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='email_verified',
            field=models.BooleanField(default=False, verbose_name='Email Verified'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='last_login_ip',
            field=models.GenericIPAddressField(blank=True, null=True, verbose_name='Last Login IP'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='login_attempts',
            field=models.IntegerField(default=0, verbose_name='Login Attempts'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='password_reset_expires',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Password Reset Expires'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='password_reset_token',
            field=models.CharField(blank=True, max_length=100, verbose_name='Password Reset Token'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='phone_number',
            field=models.CharField(blank=True, max_length=20, verbose_name='Phone Number'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='position',
            field=models.CharField(blank=True, max_length=100, verbose_name='Position'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='profile_picture',
            field=models.ImageField(blank=True, null=True, upload_to='profile_pictures/', verbose_name='Profile Picture'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='receive_comment_emails',
            field=models.BooleanField(default=True, verbose_name='Receive Comment Emails'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='receive_file_emails',
            field=models.BooleanField(default=True, verbose_name='Receive File Emails'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='receive_marketing_emails',
            field=models.BooleanField(default=False, verbose_name='Receive Marketing Emails'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='receive_project_emails',
            field=models.BooleanField(default=True, verbose_name='Receive Project Emails'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='verification_token',
            field=models.CharField(blank=True, max_length=100, verbose_name='Verification Token'),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='id',
            field=models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False),
        ),
    ]
