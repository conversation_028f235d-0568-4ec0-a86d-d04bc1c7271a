# Generated by Django 5.2 on 2025-04-13 00:20

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='DashboardSettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('layout', models.CharField(choices=[('default', 'Default'), ('compact', 'Compact'), ('expanded', 'Expanded')], default='default', max_length=20)),
                ('show_recent_files', models.BooleanField(default=True)),
                ('show_notifications', models.BooleanField(default=True)),
                ('show_projects', models.BooleanField(default=True)),
                ('show_statistics', models.BooleanField(default=True)),
                ('show_activity', models.BooleanField(default=True)),
                ('theme', models.CharField(choices=[('light', 'Light'), ('dark', 'Dark'), ('auto', 'Auto (System)')], default='auto', max_length=20)),
                ('color_scheme', models.CharField(choices=[('blue', 'Blue'), ('green', 'Green'), ('purple', 'Purple'), ('orange', 'Orange'), ('red', 'Red')], default='blue', max_length=20)),
                ('items_per_page', models.IntegerField(choices=[(5, '5'), (10, '10'), (25, '25'), (50, '50'), (100, '100')], default=10)),
                ('email_notifications', models.BooleanField(default=True)),
                ('browser_notifications', models.BooleanField(default=True)),
                ('notification_sounds', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='dashboard_settings', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
