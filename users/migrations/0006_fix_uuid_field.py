from django.db import migrations, models
import uuid


def convert_char_to_uuid(apps, schema_editor):
    """
    Convert char IDs to proper UUIDs
    """
    # Get the historical model
    CustomUser = apps.get_model('users', 'CustomUser')
    db_alias = schema_editor.connection.alias
    
    # For each user, ensure the ID is a valid UUID
    for user in CustomUser.objects.using(db_alias).all():
        try:
            # Try to parse the existing ID as UUID
            uuid_obj = uuid.UUID(user.id)
        except (ValueError, TypeError):
            # If it's not a valid UUID, generate a new one
            user.id = uuid.uuid4()
            user.save()


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0005_customuser_account_locked_and_more'),
    ]

    operations = [
        # First ensure the field is properly defined as UUID
        migrations.AlterField(
            model_name='customuser',
            name='id',
            field=models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False),
        ),
        # Then run the data migration to fix existing records
        migrations.RunPython(convert_char_to_uuid, migrations.RunPython.noop),
    ]
