# Generated by Django 5.2 on 2025-04-17 02:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0008_remove_dashboardsettings_show_notifications'),
    ]

    operations = [
        migrations.AlterField(
            model_name='emaillog',
            name='email_type',
            field=models.CharField(choices=[('welcome', 'Welcome Email'), ('progress_update', 'Progress Update'), ('password_reset', 'Password Reset'), ('account_change', 'Account Change'), ('project_invitation', 'Project Invitation'), ('file_shared', 'File Shared'), ('new_testimonial', 'New Testimonial'), ('other', 'Other')], max_length=20),
        ),
    ]
