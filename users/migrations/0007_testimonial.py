# Generated by Django 5.2 on 2025-04-17 02:27

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0006_fix_uuid_field'),
    ]

    operations = [
        migrations.CreateModel(
            name='Testimonial',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4,
                 editable=False, primary_key=True, serialize=False)),
                ('content', models.TextField(verbose_name='Testimonial Content')),
                ('rating', models.IntegerField(choices=[
                 (1, '1 - Poor'), (2, '2 - Fair'), (3, '3 - Good'), (4, '4 - Very Good'), (5, '5 - Excellent')], default=5)),
                ('service_type', models.CharField(choices=[('assignment', 'Assignment'), ('project', 'Project'), ('thesis', 'Thesis'), ('dissertation', 'Dissertation'), (
                    'data_analysis', 'Data Analysis'), ('web_development', 'Web Development'), ('machine_learning', 'Machine Learning'), ('other', 'Other')], default='assignment', max_length=50)),
                ('is_approved', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE,
                 related_name='testimonials', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
