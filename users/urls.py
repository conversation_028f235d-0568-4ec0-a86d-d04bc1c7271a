from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

urlpatterns = [
    # Authentication URLs
    path('', views.home, name='home'),
    path('login/', views.CustomLoginView.as_view(), name='login'),
    path('logout/', views.logout_view, name='logout'),
    # Use the improved registration view
    path('register/', views.register, name='register'),

    # Password Reset URLs - Using Django's built-in views
    path('password_reset/',
         auth_views.PasswordResetView.as_view(
             template_name='users/password_reset_form.html',
             html_email_template_name='emails/password_reset_standalone.html',
             email_template_name='emails/password_reset_text.html',
             subject_template_name='emails/password_reset_subject.txt',
             success_url='/password_reset/done/'),
         name='password_reset'),
    path('password_reset/done/',
         auth_views.PasswordResetDoneView.as_view(
             template_name='users/password_reset_done.html'),
         name='password_reset_done'),
    path('password_reset/confirm/<uidb64>/<token>/',
         auth_views.PasswordResetConfirmView.as_view(
             template_name='users/password_reset_confirm.html',
             success_url='/password_reset/complete/'),
         name='password_reset_confirm'),
    path('password_reset_complete/',
         auth_views.PasswordResetCompleteView.as_view(
             template_name='users/password_reset_complete.html'),
         name='password_reset_complete'),
    # Additional URL pattern for the correct path format
    path('password_reset/complete/',
         auth_views.PasswordResetCompleteView.as_view(
             template_name='users/password_reset_complete.html'),
         name='password_reset_complete_alt'),
    # Keep the old URLs for backward compatibility
    path('password-reset/',
         auth_views.PasswordResetView.as_view(
             template_name='users/password_reset_form.html',
             html_email_template_name='emails/password_reset_standalone.html',
             email_template_name='emails/password_reset_text.html',
             subject_template_name='emails/password_reset_subject.txt',
             success_url='/password-reset/done/'),
         name='password-reset'),
    path('password-reset/done/',
         auth_views.PasswordResetDoneView.as_view(
             template_name='users/password_reset_done.html'),
         name='password-reset-done'),
    path('password-reset/confirm/<uidb64>/<token>/',
         auth_views.PasswordResetConfirmView.as_view(
             template_name='users/password_reset_confirm.html',
             success_url='/password-reset/complete/'),
         name='password-reset-confirm'),
    path('password-reset-complete/',
         auth_views.PasswordResetCompleteView.as_view(
             template_name='users/password_reset_complete.html'),
         name='password-reset-complete'),

    # Dashboard URLs
    path('dashboard/', views.dashboard, name='dashboard'),
    path('profile/', views.profile, name='profile'),
    path('dashboard-settings/', views.save_dashboard_settings,
         name='save_dashboard_settings'),

    # Testimonial URLs
    path('testimonials/', views.all_testimonials, name='all_testimonials'),
    path('testimonials/add/', views.add_testimonial, name='add_testimonial'),
    path('testimonials/my/', views.my_testimonials, name='my_testimonials'),

    # Message URLs
    path('messages/', views.message_list, name='message_list'),
    path('messages/new/', views.message_create, name='message_create'),
    path('messages/<uuid:pk>/', views.message_detail, name='message_detail'),
]
