import os
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.conf import settings
from django.contrib.auth.tokens import default_token_generator
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes
from django.contrib.sites.shortcuts import get_current_site
from django.urls import reverse


def send_password_reset_email(request, user):
    """
    Send a password reset email to the user with a direct HTML email
    using direct SMTP connection to ensure proper formatting

    Args:
        request: The HTTP request object
        user: The user object

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    try:
        # Generate password reset token
        token = default_token_generator.make_token(user)
        uid = urlsafe_base64_encode(force_bytes(user.pk))

        # Get the current site
        current_site = get_current_site(request)
        site_name = current_site.name
        domain = current_site.domain

        # Determine protocol
        protocol = 'https' if request.is_secure() else 'http'

        # Context for the email template
        context = {
            'user': user,
            'protocol': protocol,
            'domain': domain,
            'site_name': site_name,
            'uid': uid,
            'token': token,
            'site_url': settings.SITE_URL,
        }

        # Create HTML content
        html_message = render_to_string(
            'emails/password_reset_standalone.html', context)

        # Create plain text content
        plain_message = strip_tags(html_message)

        # Create email subject
        subject = f"Reset your CompletoPLUS password"

        # Set up the SMTP server
        smtp_server = settings.EMAIL_HOST
        smtp_port = settings.EMAIL_PORT
        smtp_username = settings.EMAIL_HOST_USER
        smtp_password = settings.EMAIL_HOST_PASSWORD
        use_tls = settings.EMAIL_USE_TLS

        # Create message container
        msg = MIMEMultipart('alternative')
        msg['Subject'] = subject
        msg['From'] = settings.DEFAULT_FROM_EMAIL
        msg['To'] = user.email
        msg['Reply-To'] = settings.CONTACT_EMAIL

        # Attach parts
        part1 = MIMEText(plain_message, 'plain')
        part2 = MIMEText(html_message, 'html')

        # Plain text should be first, HTML second
        msg.attach(part1)
        msg.attach(part2)

        # Send the message via SMTP server
        server = smtplib.SMTP(smtp_server, smtp_port)
        if use_tls:
            server.starttls()
        server.login(smtp_username, smtp_password)
        server.sendmail(settings.DEFAULT_FROM_EMAIL, [
                        user.email], msg.as_string())
        server.quit()

        print(f"Password reset email sent to {user.email}")
        return True
    except Exception as e:
        print(f"Error sending password reset email: {e}")
        return False
