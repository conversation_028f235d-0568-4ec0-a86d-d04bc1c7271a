from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _
import uuid


class CustomUser(AbstractUser):
    USER_TYPE_CHOICES = (
        ('admin', 'Administrator'),
        ('client', 'Client'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user_type = models.CharField(
        max_length=10,
        choices=USER_TYPE_CHOICES,
        default='client',
        verbose_name=_('User Type')
    )
    profile_picture = models.ImageField(
        upload_to='profile_pictures/',
        blank=True,
        null=True,
        verbose_name=_('Profile Picture')
    )
    bio = models.TextField(blank=True, verbose_name=_('Bio'))
    phone_number = models.CharField(
        max_length=20, blank=True, verbose_name=_('Phone Number'))
    address = models.TextField(blank=True, verbose_name=_('Address'))
    company = models.CharField(
        max_length=100, blank=True, verbose_name=_('Company'))
    position = models.CharField(
        max_length=100, blank=True, verbose_name=_('Position'))
    last_login_ip = models.GenericIPAddressField(
        blank=True, null=True, verbose_name=_('Last Login IP'))
    account_verified = models.BooleanField(
        default=False, verbose_name=_('Account Verified'))
    verification_token = models.CharField(
        max_length=100, blank=True, verbose_name=_('Verification Token'))
    password_reset_token = models.CharField(
        max_length=100, blank=True, verbose_name=_('Password Reset Token'))
    password_reset_expires = models.DateTimeField(
        blank=True, null=True, verbose_name=_('Password Reset Expires'))
    login_attempts = models.IntegerField(
        default=0, verbose_name=_('Login Attempts'))
    account_locked = models.BooleanField(
        default=False, verbose_name=_('Account Locked'))
    account_locked_until = models.DateTimeField(
        blank=True, null=True, verbose_name=_('Account Locked Until'))

    # Email preferences
    email_verified = models.BooleanField(
        default=False, verbose_name=_('Email Verified'))
    receive_project_emails = models.BooleanField(
        default=True, verbose_name=_('Receive Project Emails'))
    receive_file_emails = models.BooleanField(
        default=True, verbose_name=_('Receive File Emails'))
    receive_comment_emails = models.BooleanField(
        default=True, verbose_name=_('Receive Comment Emails'))
    receive_marketing_emails = models.BooleanField(
        default=False, verbose_name=_('Receive Marketing Emails'))

    def is_admin_user(self):
        return self.user_type == 'admin' or self.is_staff

    def is_client_user(self):
        return self.user_type == 'client'

    def get_full_name(self):
        """Return the first_name plus the last_name, with a space in between."""
        full_name = f"{self.first_name} {self.last_name}"
        return full_name.strip() or self.username

    def get_profile_picture_url(self):
        """Return the URL of the user's profile picture or a default image."""
        if self.profile_picture and hasattr(self.profile_picture, 'url'):
            return self.profile_picture.url
        return '/static/img/default-profile.png'

    def lock_account(self, duration_minutes=30):
        """Lock the account for a specified duration."""
        from django.utils import timezone
        import datetime

        self.account_locked = True
        self.account_locked_until = timezone.now(
        ) + datetime.timedelta(minutes=duration_minutes)
        self.save(update_fields=['account_locked', 'account_locked_until'])

    def unlock_account(self):
        """Unlock the account and reset login attempts."""
        self.account_locked = False
        self.account_locked_until = None
        self.login_attempts = 0
        self.save(update_fields=['account_locked',
                  'account_locked_until', 'login_attempts'])

    def is_account_locked(self):
        """Check if the account is currently locked."""
        from django.utils import timezone

        if not self.account_locked:
            return False

        if self.account_locked_until and self.account_locked_until < timezone.now():
            self.unlock_account()
            return False

        return True

    def __str__(self):
        return self.username


class DashboardSettings(models.Model):
    """User dashboard settings for customization"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.OneToOneField(
        CustomUser, on_delete=models.CASCADE, related_name='dashboard_settings')

    # Layout settings
    layout = models.CharField(max_length=20, choices=[
        ('default', 'Default'),
        ('compact', 'Compact'),
        ('expanded', 'Expanded'),
    ], default='default')

    # Widget visibility settings
    show_recent_files = models.BooleanField(default=True)
    show_projects = models.BooleanField(default=True)
    show_statistics = models.BooleanField(default=True)
    show_activity = models.BooleanField(default=True)

    # Theme settings
    theme = models.CharField(max_length=20, choices=[
        ('light', 'Light'),
        ('dark', 'Dark'),
        ('auto', 'Auto (System)'),
    ], default='auto')

    # Color scheme
    color_scheme = models.CharField(max_length=20, choices=[
        ('blue', 'Blue'),
        ('green', 'Green'),
        ('purple', 'Purple'),
        ('orange', 'Orange'),
        ('red', 'Red'),
    ], default='blue')

    # Other settings
    items_per_page = models.IntegerField(default=10, choices=[
        (5, '5'),
        (10, '10'),
        (25, '25'),
        (50, '50'),
        (100, '100'),
    ])

    # Notification settings
    email_notifications = models.BooleanField(default=True)
    # Real-time notification settings removed

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}'s Dashboard Settings"


class EmailLog(models.Model):
    """Model to track emails sent by the system"""
    EMAIL_TYPES = (
        ('welcome', 'Welcome Email'),
        ('progress_update', 'Progress Update'),
        ('password_reset', 'Password Reset'),
        ('account_change', 'Account Change'),
        ('project_invitation', 'Project Invitation'),
        ('file_shared', 'File Shared'),
        ('new_testimonial', 'New Testimonial'),
        ('other', 'Other'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    recipient = models.ForeignKey(
        CustomUser, on_delete=models.CASCADE, related_name='received_emails')
    email_type = models.CharField(max_length=20, choices=EMAIL_TYPES)
    subject = models.CharField(max_length=255)
    sent_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=20, choices=[
        ('queued', 'Queued'),
        ('success', 'Success'),
        ('failed', 'Failed'),
    ])
    error_message = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ['-sent_at']

    def __str__(self):
        return f"{self.email_type} to {self.recipient.email} on {self.sent_at.strftime('%Y-%m-%d %H:%M')}"


class Message(models.Model):
    """Model for messages between users"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    sender = models.ForeignKey(
        CustomUser, on_delete=models.CASCADE, related_name='sent_messages')
    recipient = models.ForeignKey(
        CustomUser, on_delete=models.CASCADE, related_name='received_messages')
    subject = models.CharField(max_length=255)
    content = models.TextField()
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Message from {self.sender.username} to {self.recipient.username}: {self.subject}"


class Testimonial(models.Model):
    """Model for client testimonials and ratings"""
    RATING_CHOICES = (
        (1, '1 - Poor'),
        (2, '2 - Fair'),
        (3, '3 - Good'),
        (4, '4 - Very Good'),
        (5, '5 - Excellent'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    client = models.ForeignKey(
        CustomUser, on_delete=models.CASCADE, related_name='testimonials')
    content = models.TextField(verbose_name=_('Testimonial Content'))
    rating = models.IntegerField(choices=RATING_CHOICES, default=5)
    service_type = models.CharField(
        max_length=50,
        choices=[
            ('assignment', 'Assignment'),
            ('project', 'Project'),
            ('thesis', 'Thesis'),
            ('dissertation', 'Dissertation'),
            ('data_analysis', 'Data Analysis'),
            ('web_development', 'Web Development'),
            ('machine_learning', 'Machine Learning'),
            ('other', 'Other'),
        ],
        default='assignment'
    )
    is_approved = models.BooleanField(default=False)
    is_featured = models.BooleanField(
        default=False, verbose_name=_('Featured Testimonial'))
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Testimonial by {self.client.get_full_name()} - {self.rating} stars"
