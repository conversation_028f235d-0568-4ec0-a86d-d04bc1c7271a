{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "NIXPACKS", "buildCommand": "python manage.py collectstatic --noinput"}, "deploy": {"startCommand": "python manage.py migrate && gunicorn fileshare.wsgi", "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10}, "services": [{"name": "web", "startCommand": "python manage.py migrate && gunicorn fileshare.wsgi", "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10}]}