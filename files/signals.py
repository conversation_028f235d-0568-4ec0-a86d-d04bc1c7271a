import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone
from django.conf import settings
from .models import ProgressNote, Notification, Project
from users.utils import send_email_with_logging

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Notification)
def process_notification_email(sender, instance, created, **kwargs):
    """
    Signal handler to send emails for new notifications directly
    """
    logger.info(
        f"Notification signal triggered: id={instance.id}, type={instance.notification_type}, created={created}")

    if not created:
        # Only process newly created notifications
        logger.info(
            f"Skipping email for notification {instance.id} (not newly created)")
        return

    # Skip if the notification has already been processed
    if getattr(instance, '_email_sent', False):
        logger.info(
            f"Skipping email for notification {instance.id} (already processed)")
        return

    # Check for recent notifications of the same type to prevent duplicates
    if instance.notification_type == 'progress_update':
        import datetime
        time_threshold = timezone.now() - datetime.timedelta(seconds=10)

        # Count recent notifications of the same type for the same user and project
        # Exclude the current notification
        recent_notifications = Notification.objects.filter(
            user=instance.user,
            project=instance.project,
            notification_type=instance.notification_type,
            created_at__gte=time_threshold
        ).exclude(id=instance.id)

        recent_notifications_count = recent_notifications.count()

        if recent_notifications_count > 0:
            logger.info(
                f"Skipping email for notification {instance.id} (duplicate detected within 10 seconds)")
            logger.info(
                f"Recent notifications: {[n.id for n in recent_notifications]}")
            instance._email_sent = True
            return

    # Mark the notification as processed to prevent duplicate emails
    instance._email_sent = True

    # Process the notification email directly
    try:
        # Import here to avoid circular imports
        from files.notifications import send_notification_email

        # Send the notification email directly
        send_notification_email(instance)

        logger.info(
            f"Notification email sent directly for notification {instance.id}")
    except Exception as e:
        logger.error(f"Error sending notification email: {e}")
        # Continue even if email sending fails


@receiver(post_save, sender=ProgressNote)
def create_notification_for_progress_note(sender, instance, created, **kwargs):
    """
    Signal handler to create notifications for progress notes
    """
    # Skip if this is not a new progress note or if it's being processed by a migration
    if not created or kwargs.get('raw', False):
        return

    # Skip if the notification has already been created
    if getattr(instance, '_notification_created', False):
        return

    # Mark the progress note as processed to prevent duplicate notifications
    instance._notification_created = True

    # Skip automatic notification if this is marked as a silent update
    if getattr(instance, 'silent_update', False):
        return

    # Check for recent progress notes to prevent duplicate notifications
    import datetime
    time_threshold = timezone.now() - datetime.timedelta(seconds=10)

    # Count recent progress notes for the same project
    # Exclude the current note
    recent_notes_count = ProgressNote.objects.filter(
        project=instance.project,
        created_by=instance.created_by,
        created_at__gte=time_threshold
    ).exclude(id=instance.id).count()

    if recent_notes_count > 0:
        logger.info(
            f"Skipping notification for progress note {instance.id} (duplicate detected within 10 seconds)")
        return

    try:
        # Only create notifications for admin-created notes to be sent to clients
        if instance.created_by.is_admin_user() and instance.project.client != instance.created_by:
            # Check for recent notifications to prevent duplicates
            recent_notifications = Notification.objects.filter(
                user=instance.project.client,
                project=instance.project,
                notification_type='progress_update',
                created_at__gte=time_threshold
            )

            if recent_notifications.exists():
                logger.info(
                    f"Skipping duplicate notification for project {instance.project.id}")
                return

            # Create the notification directly
            notification = Notification.objects.create(
                user=instance.project.client,
                project=instance.project,
                message=f'New progress update for project "{instance.project.name}"',
                notification_type='progress_update',
                data={
                    'project_name': instance.project.name,
                    'project_id': str(instance.project.id),
                    'note_text': instance.note,
                    'created_by': instance.created_by.get_full_name() or instance.created_by.username,
                    'created_by_id': str(instance.created_by.id),
                    'created_at': instance.created_at.isoformat() if hasattr(instance, 'created_at') else None
                }
            )

            logger.info(
                f"Notification created directly for progress note {instance.id}")
    except Exception as e:
        logger.error(
            f"Failed to create notification for progress note: {str(e)}")
