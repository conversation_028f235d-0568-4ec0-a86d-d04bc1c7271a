import os
import zipfile
import tempfile
import uuid
import logging
from django.core.files.base import ContentFile
from django.conf import settings

# Set up logger
logger = logging.getLogger(__name__)

def handle_file_upload(uploaded_files, file_name, notes, owner, project, file_type):
    """
    Handle file upload and automatic zipping
    
    Args:
        uploaded_files: List of uploaded files
        file_name: Name for the file(s)
        notes: Notes for the file(s)
        owner: User who uploaded the file(s)
        project: Project the file(s) belong to
        file_type: Type of file upload (client_upload or admin_upload)
        
    Returns:
        File instance
    """
    from .models import File
    
    # If no file name is provided, use a default or the first file's name
    if not file_name or file_name.strip() == '':
        if len(uploaded_files) == 1:
            file_name = os.path.splitext(uploaded_files[0].name)[0]
        else:
            file_name = f"Multiple Files ({len(uploaded_files)})"
    
    # Create a zip file containing all uploaded files
    with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as temp_zip:
        with zipfile.ZipFile(temp_zip, 'w', compression=zipfile.ZIP_DEFLATED) as zip_file:
            # Add each file to the zip
            for uploaded_file in uploaded_files:
                zip_file.writestr(uploaded_file.name, uploaded_file.read())
    
    # Create a File instance for the zip file
    with open(temp_zip.name, 'rb') as zip_file:
        # Create file instance
        original_file_names = ', '.join([f.name for f in uploaded_files])
        # Truncate if too long for the database field
        if len(original_file_names) > 255:
            original_file_names = original_file_names[:252] + '...'
        
        file_instance = File(
            project=project,
            file=ContentFile(zip_file.read(), name=f"{file_name}.zip"),
            file_name=file_name,
            owner=owner,
            notes=notes,
            file_type=file_type,
            is_zipped=True,
            original_file_name=original_file_names
        )
        
        # Save the file
        file_instance.save()
    
    # Clean up the temporary file
    os.unlink(temp_zip.name)
    
    return file_instance

def get_accepted_file_types():
    """
    Get a dictionary of accepted file types grouped by category
    
    Returns:
        Dictionary of file types
    """
    return {
        'Documents': [
            {'extension': 'pdf', 'description': 'PDF Document'},
            {'extension': 'doc', 'description': 'Word Document (Legacy)'},
            {'extension': 'docx', 'description': 'Word Document'},
            {'extension': 'xls', 'description': 'Excel Spreadsheet (Legacy)'},
            {'extension': 'xlsx', 'description': 'Excel Spreadsheet'},
            {'extension': 'ppt', 'description': 'PowerPoint (Legacy)'},
            {'extension': 'pptx', 'description': 'PowerPoint'},
            {'extension': 'txt', 'description': 'Text File'},
            {'extension': 'rtf', 'description': 'Rich Text Format'},
            {'extension': 'odt', 'description': 'OpenDocument Text'},
            {'extension': 'ods', 'description': 'OpenDocument Spreadsheet'},
            {'extension': 'odp', 'description': 'OpenDocument Presentation'},
        ],
        'Images': [
            {'extension': 'jpg', 'description': 'JPEG Image'},
            {'extension': 'jpeg', 'description': 'JPEG Image'},
            {'extension': 'png', 'description': 'PNG Image'},
            {'extension': 'gif', 'description': 'GIF Image'},
            {'extension': 'svg', 'description': 'SVG Image'},
            {'extension': 'webp', 'description': 'WebP Image'},
            {'extension': 'tiff', 'description': 'TIFF Image'},
            {'extension': 'bmp', 'description': 'Bitmap Image'},
        ],
        'Archives': [
            {'extension': 'zip', 'description': 'ZIP Archive'},
            {'extension': 'rar', 'description': 'RAR Archive'},
            {'extension': '7z', 'description': '7-Zip Archive'},
            {'extension': 'tar', 'description': 'TAR Archive'},
            {'extension': 'gz', 'description': 'Gzip Archive'},
        ],
        'Audio/Video': [
            {'extension': 'mp3', 'description': 'MP3 Audio'},
            {'extension': 'mp4', 'description': 'MP4 Video'},
            {'extension': 'wav', 'description': 'WAV Audio'},
            {'extension': 'avi', 'description': 'AVI Video'},
            {'extension': 'mov', 'description': 'QuickTime Video'},
            {'extension': 'wmv', 'description': 'Windows Media Video'},
        ],
        'Code': [
            {'extension': 'py', 'description': 'Python Source Code'},
            {'extension': 'js', 'description': 'JavaScript Source Code'},
            {'extension': 'html', 'description': 'HTML Document'},
            {'extension': 'css', 'description': 'CSS Stylesheet'},
            {'extension': 'json', 'description': 'JSON Data'},
            {'extension': 'xml', 'description': 'XML Document'},
            {'extension': 'csv', 'description': 'CSV Data'},
            {'extension': 'md', 'description': 'Markdown Document'},
        ],
        'Data Analysis': [
            {'extension': 'ipynb', 'description': 'Jupyter Notebook'},
            {'extension': 'r', 'description': 'R Script'},
            {'extension': 'rmd', 'description': 'R Markdown'},
            {'extension': 'sav', 'description': 'SPSS Data File'},
            {'extension': 'dta', 'description': 'Stata Data File'},
            {'extension': 'sas7bdat', 'description': 'SAS Data File'},
        ]
    }

def get_file_icon_class(file_extension):
    """
    Get the Font Awesome icon class for a file extension
    
    Args:
        file_extension: File extension
        
    Returns:
        Font Awesome icon class
    """
    extension = file_extension.lower().strip('.')
    
    # Image files
    if extension in ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'tiff', 'bmp']:
        return 'fa-file-image'
    
    # Document files
    elif extension in ['doc', 'docx', 'odt', 'rtf']:
        return 'fa-file-word'
    elif extension in ['xls', 'xlsx', 'ods']:
        return 'fa-file-excel'
    elif extension in ['ppt', 'pptx', 'odp']:
        return 'fa-file-powerpoint'
    elif extension in ['pdf']:
        return 'fa-file-pdf'
    elif extension in ['txt', 'md', 'rtf']:
        return 'fa-file-alt'
        
    # Archive files
    elif extension in ['zip', 'rar', '7z', 'tar', 'gz']:
        return 'fa-file-archive'
        
    # Audio files
    elif extension in ['mp3', 'wav', 'ogg', 'flac', 'aac']:
        return 'fa-file-audio'
        
    # Video files
    elif extension in ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv']:
        return 'fa-file-video'
        
    # Code files
    elif extension in ['html', 'css', 'js', 'py', 'java', 'c', 'cpp', 'php', 'xml', 'json']:
        return 'fa-file-code'
        
    # Data files
    elif extension in ['csv', 'tsv', 'xlsx', 'xls', 'sav', 'dta', 'sas7bdat']:
        return 'fa-table'
        
    # Default
    else:
        return 'fa-file'

def extract_file_from_zip(zip_file_path, file_name=None, index=0):
    """
    Extract a file from a zip archive
    
    Args:
        zip_file_path: Path to the zip file
        file_name: Name of the file to extract (optional)
        index: Index of the file to extract if file_name is not provided (default: 0)
        
    Returns:
        Tuple of (file_content, file_name, content_type)
    """
    import mimetypes
    
    with zipfile.ZipFile(zip_file_path, 'r') as zip_file:
        # Get the list of files in the zip
        file_list = zip_file.namelist()
        
        if not file_list:
            return None, None, None
            
        # If file_name is provided, try to find it in the zip
        if file_name:
            if file_name in file_list:
                # Extract the file
                file_content = zip_file.read(file_name)
                content_type = mimetypes.guess_type(file_name)[0] or 'application/octet-stream'
                return file_content, file_name, content_type
            else:
                # Try to find a file with a similar name
                for name in file_list:
                    if file_name.lower() in name.lower():
                        file_content = zip_file.read(name)
                        content_type = mimetypes.guess_type(name)[0] or 'application/octet-stream'
                        return file_content, name, content_type
        
        # If file_name is not provided or not found, use the index
        if index < len(file_list):
            file_name = file_list[index]
            file_content = zip_file.read(file_name)
            content_type = mimetypes.guess_type(file_name)[0] or 'application/octet-stream'
            return file_content, file_name, content_type
            
    return None, None, None
