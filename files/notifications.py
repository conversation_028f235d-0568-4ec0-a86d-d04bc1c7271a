import logging
from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.contrib.auth import get_user_model
from .models import Notification, Project, File, ProgressNote

User = get_user_model()
logger = logging.getLogger(__name__)


def send_notification_email(notification):
    """
    Send an email notification based on a Notification object

    Args:
        notification: The Notification object to send an email for
    """
    try:
        # Get user's email preferences
        if not notification.user.receive_project_emails and notification.notification_type in ['status_change', 'progress_update']:
            logger.info(
                f"Skipping email for {notification.notification_type} notification to {notification.user.email} (user opted out)")
            return

        if not notification.user.receive_file_emails and notification.notification_type == 'file_upload':
            logger.info(
                f"Skipping email for file_upload notification to {notification.user.email} (user opted out)")
            return

        # Prepare context for the email template
        context = {
            'notification': notification,
            'user': notification.user,
            'project': notification.project,
            'site_url': settings.SITE_URL,
            'site_name': 'CompletoPLUS',
            'data': notification.data or {},
        }

        # Determine email subject and template based on notification type
        if notification.notification_type == 'file_upload':
            subject = f"New file uploaded to {notification.project.name}"
            template_name = 'emails/file_upload_notification.html'
        elif notification.notification_type == 'status_change':
            subject = f"Status update for project {notification.project.name}"
            template_name = 'emails/status_change_notification.html'
        elif notification.notification_type == 'progress_update':
            subject = f"Progress update for project {notification.project.name}"
            template_name = 'emails/progress_update_notification.html'
        else:
            subject = f"Notification from CompletoPLUS: {notification.message}"
            template_name = 'emails/general_notification.html'

        # Render HTML content
        html_content = render_to_string(template_name, context)
        text_content = strip_tags(html_content)

        # Create email message
        email = EmailMultiAlternatives(
            subject=subject,
            body=text_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[notification.user.email],
            reply_to=[settings.CONTACT_EMAIL],
        )

        # Attach HTML content
        email.attach_alternative(html_content, "text/html")

        # Send email
        email.send()

        # Log email sent
        logger.info(
            f"Email notification sent to {notification.user.email} for {notification.notification_type}")

        # Record email in EmailLog if the model exists
        try:
            from users.models import EmailLog
            EmailLog.objects.create(
                recipient=notification.user,
                email_type=notification.notification_type,
                subject=subject,
                status='success'
            )
        except ImportError:
            pass

    except Exception as e:
        logger.error(f"Failed to send email notification: {str(e)}")

        # Record failed email in EmailLog if the model exists
        try:
            from users.models import EmailLog
            EmailLog.objects.create(
                recipient=notification.user,
                email_type=notification.notification_type,
                subject=subject,
                status='failed',
                error_message=str(e)
            )
        except (ImportError, UnboundLocalError):
            pass


def create_file_upload_notification(request, project, file_instance):
    """
    Create notification for file upload
    Email will be sent asynchronously via signal handler

    Args:
        request: The HTTP request
        project: The project the file was uploaded to
        file_instance: The uploaded file instance
    """
    # Create notification for client if uploaded by admin
    if request.user.is_admin_user():
        notification = Notification.objects.create(
            user=project.client,
            project=project,
            message=f'New file "{file_instance.file_name}" uploaded by administrator.',
            notification_type='file_upload',
            data={
                'file_id': str(file_instance.id),
                'file_name': file_instance.file_name,
                'file_type': file_instance.file_type,
                'file_url': file_instance.file.url if file_instance.file else None,
                'uploaded_by': request.user.get_full_name() or request.user.username,
                'uploaded_by_id': str(request.user.id),
                'project_name': project.name,
                'project_id': str(project.id)
            }
        )

        # Email will be sent by signal handler

    # Create notification for admins if uploaded by client
    else:
        # Get the first admin user to create a notification for
        admin_users = User.objects.filter(user_type='admin')
        if admin_users.exists():
            # Create a single notification for the first admin
            first_admin = admin_users.first()
            notification = Notification.objects.create(
                user=first_admin,
                project=project,
                message=f'New file "{file_instance.file_name}" uploaded by {request.user.get_full_name() or request.user.username}.',
                notification_type='file_upload',
                data={
                    'file_id': str(file_instance.id),
                    'file_name': file_instance.file_name,
                    'file_type': file_instance.file_type,
                    'file_url': file_instance.file.url if file_instance.file else None,
                    'uploaded_by': request.user.get_full_name() or request.user.username,
                    'uploaded_by_id': str(request.user.id),
                    'project_name': project.name,
                    'project_id': str(project.id),
                    'client_name': project.client.get_full_name() or project.client.username,
                    'client_id': str(project.client.id),
                    'admin_count': admin_users.count()
                }
            )

            # Create notifications for other admins without triggering emails
            for admin in admin_users.exclude(id=first_admin.id):
                # Create notification but mark it as already processed to prevent email
                notification = Notification.objects.create(
                    user=admin,
                    project=project,
                    message=f'New file "{file_instance.file_name}" uploaded by {request.user.get_full_name() or request.user.username}.',
                    notification_type='file_upload',
                    data={
                        'file_id': str(file_instance.id),
                        'file_name': file_instance.file_name,
                        'file_type': file_instance.file_type,
                        'file_url': file_instance.file.url if file_instance.file else None,
                        'uploaded_by': request.user.get_full_name() or request.user.username,
                        'uploaded_by_id': str(request.user.id),
                        'project_name': project.name,
                        'project_id': str(project.id),
                        'client_name': project.client.get_full_name() or project.client.username,
                        'client_id': str(project.client.id)
                    }
                )
                # Mark as already processed to prevent email
                notification._email_sent = True


def create_status_change_notification(project, old_status, updated_by):
    """
    Create notification for project status change
    Email will be sent asynchronously via signal handler

    Args:
        project: The project that had its status changed
        old_status: The previous status of the project
        updated_by: The user who updated the status
    """
    notification = Notification.objects.create(
        user=project.client,
        project=project,
        message=f'Project "{project.name}" status updated to {project.get_status_display()}.',
        notification_type='status_change',
        data={
            'project_name': project.name,
            'project_id': str(project.id),
            'old_status': old_status,
            'new_status': project.get_status_display(),
            'updated_by': updated_by.get_full_name() or updated_by.username,
            'updated_by_id': str(updated_by.id)
        }
    )

    # Email will be sent by signal handler


def create_progress_note_notification(project, note, created_by):
    """
    Create notification for progress note
    Email will be sent asynchronously via signal handler

    Args:
        project: The project the progress note is for
        note: The progress note content
        created_by: The user who created the note
    """
    logger.info(
        f"Creating progress note notification for project {project.id}")
    logger.info(f"Note text: {note[:100]}...")
    logger.info(f"Created by: {created_by.username}")

    notification = Notification.objects.create(
        user=project.client,
        project=project,
        message=f'New progress update for project "{project.name}"',
        notification_type='progress_update',
        data={
            'project_name': project.name,
            'project_id': str(project.id),
            'note_text': note,
            'created_by': created_by.get_full_name() or created_by.username,
            'created_by_id': str(created_by.id),
            'created_at': created_by.created_at.isoformat() if hasattr(created_by, 'created_at') else None
        }
    )

    logger.info(f"Created notification with ID {notification.id}")

    # Email will be sent by signal handler


def create_welcome_notification(user):
    """
    Create welcome notification for new user.

    This function creates a notification that will trigger the email
    sending via the notification signal handler to avoid duplicate emails.

    Args:
        user: The newly created user
    """
    try:
        # Check if a welcome notification already exists for this user
        existing_notification = Notification.objects.filter(
            user=user,
            notification_type='welcome'
        ).first()

        if existing_notification:
            logger.info(
                f"Welcome notification already exists for user {user.username}")
            return existing_notification

        # Create a welcome notification without requiring a project
        notification = Notification.objects.create(
            user=user,
            project=None,  # Welcome notification doesn't need a project
            message=f"Welcome to CompletoPLUS, {user.username}!",
            notification_type='welcome',
            data={
                'username': user.username,
                'email': user.email,
                'user_id': str(user.id),
                'first_name': user.first_name,
                'last_name': user.last_name
            }
        )
        logger.info(f"Welcome notification created for user {user.username}")

        # The notification signal handler will take care of sending the email
        # This prevents duplicate email sending

        return notification
    except Exception as e:
        logger.error(
            f"Error creating welcome notification for user {user.username}: {e}")
        return None
