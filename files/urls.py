from django.urls import path
from . import views

urlpatterns = [
    # Project URLs
    path('projects/', views.project_list, name='project_list'),
    path('projects/create/', views.project_create, name='project_create'),
    path('projects/<uuid:pk>/', views.project_detail, name='project_detail'),
    path('projects/<uuid:pk>/update/',
         views.project_update, name='project_update'),
    path('projects/<uuid:pk>/delete/',
         views.project_delete, name='project_delete'),

    # File URLs
    path('upload/<uuid:project_id>/', views.file_upload, name='file_upload'),
    path('download/<uuid:file_id>/', views.file_download, name='file_download'),
    path('preview/<uuid:file_id>/', views.file_preview, name='file_preview'),
    path('files/<uuid:file_id>/delete/', views.file_delete, name='file_delete'),

    # Batch Operations
    path('batch-download/', views.batch_download, name='batch_download'),
    path('batch-delete/', views.batch_delete, name='batch_delete'),

    # Progress Notes URLs
    path('projects/<uuid:project_id>/add-note/',
         views.add_progress_note, name='add_progress_note'),

    # Notification URLs
    path('notifications/', views.notification_list, name='notification_list'),
    path('notifications/mark-read/<uuid:pk>/',
         views.mark_notification_read, name='mark_notification_read'),
    path('notifications/mark-all-read/',
         views.mark_all_notifications_read, name='mark_all_notifications_read'),

    # My Files URL
    path('my-files/', views.my_files, name='my_files'),
]
