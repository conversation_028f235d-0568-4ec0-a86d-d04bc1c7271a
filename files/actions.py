import csv
import json
import zipfile
import tempfile
import os
from django.http import HttpResponse
from django.utils import timezone
from django.contrib import messages

def export_as_csv(modeladmin, request, queryset):
    """
    Export selected objects as CSV
    """
    meta = modeladmin.model._meta
    field_names = [field.name for field in meta.fields if field.name != 'file']
    
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename={meta.verbose_name_plural.lower()}-{timezone.now().strftime("%Y%m%d-%H%M%S")}.csv'
    
    writer = csv.writer(response)
    writer.writerow(field_names)
    for obj in queryset:
        row = []
        for field in field_names:
            value = getattr(obj, field)
            if hasattr(value, 'isoformat'):  # Handle datetime objects
                value = value.isoformat()
            row.append(str(value))
        writer.writerow(row)
    
    return response

export_as_csv.short_description = "Export selected items to CSV"

def export_as_json(modeladmin, request, queryset):
    """
    Export selected objects as JSON
    """
    meta = modeladmin.model._meta
    field_names = [field.name for field in meta.fields if field.name != 'file']
    
    response = HttpResponse(content_type='application/json')
    response['Content-Disposition'] = f'attachment; filename={meta.verbose_name_plural.lower()}-{timezone.now().strftime("%Y%m%d-%H%M%S")}.json'
    
    data = []
    for obj in queryset:
        item = {}
        for field in field_names:
            value = getattr(obj, field)
            if hasattr(value, 'isoformat'):  # Handle datetime objects
                value = value.isoformat()
            elif hasattr(value, 'id'):  # Handle foreign keys
                value = str(value.id)
            else:
                value = str(value)
            item[field] = value
        data.append(item)
    
    json.dump(data, response, indent=4)
    return response

export_as_json.short_description = "Export selected items to JSON"

def download_files_as_zip(modeladmin, request, queryset):
    """
    Download selected files as a ZIP archive
    """
    # Only applicable to File model
    if modeladmin.model.__name__ != 'File':
        messages.error(request, "This action is only available for files.")
        return
    
    # Create a temporary file
    temp_file = tempfile.NamedTemporaryFile(delete=False)
    temp_file.close()
    
    # Create a zip file
    with zipfile.ZipFile(temp_file.name, 'w') as zip_file:
        for file_obj in queryset:
            if file_obj.file and hasattr(file_obj.file, 'path') and os.path.exists(file_obj.file.path):
                # Add file to zip with a clean filename
                zip_file.write(file_obj.file.path, arcname=file_obj.file_name)
    
    # Prepare response
    with open(temp_file.name, 'rb') as f:
        response = HttpResponse(f.read(), content_type='application/zip')
        response['Content-Disposition'] = f'attachment; filename=files-{timezone.now().strftime("%Y%m%d-%H%M%S")}.zip'
    
    # Clean up the temporary file
    os.unlink(temp_file.name)
    
    return response

download_files_as_zip.short_description = "Download selected files as ZIP"
