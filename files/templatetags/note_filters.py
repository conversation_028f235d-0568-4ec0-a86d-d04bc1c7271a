from django import template
import re

register = template.Library()


@register.filter
def split_progress_note(note_text):
    """
    Split a progress note into its components:
    - note_text: The main note content
    - progress_text: The progress update text (if any)
    - status_text: The status update text (if any)
    """
    result = {
        'note_text': '',
        'progress_text': '',
        'status_text': ''
    }

    # Check if the note contains progress or status updates
    progress_pattern = r'\n\nProject progress updated from (\d+)% to (\d+)%\.'
    status_pattern = r'\n\nProject status updated from (\w+) to (\w+)\.'

    # Extract progress update
    progress_match = re.search(progress_pattern, note_text)
    if progress_match:
        # Clean up the progress text to remove newlines
        progress_text = progress_match.group(0).strip()
        progress_text = progress_text.replace('\n\n', '')
        result['progress_text'] = progress_text
        # Remove the progress update from the note text
        note_text = note_text.replace(progress_match.group(0), '')

    # Extract status update
    status_match = re.search(status_pattern, note_text)
    if status_match:
        # Clean up the status text to remove newlines
        status_text = status_match.group(0).strip()
        status_text = status_text.replace('\n\n', '')
        result['status_text'] = status_text
        # Remove the status update from the note text
        note_text = note_text.replace(status_match.group(0), '')

    # The remaining text is the main note content
    result['note_text'] = note_text.strip()

    return result
