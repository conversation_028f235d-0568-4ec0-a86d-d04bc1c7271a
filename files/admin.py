from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.db.models import Count
from .models import Project, File, Notification, ProgressNote, FileDownload
from .actions import export_as_csv, export_as_json, download_files_as_zip
from users.admin_actions import optimized_bulk_delete_projects, optimized_bulk_delete_files, chunked_bulk_delete


class FileInline(admin.TabularInline):
    model = File
    extra = 0
    fields = ('file_name', 'file_type', 'owner', 'uploaded_at', 'file_preview')
    readonly_fields = ('uploaded_at', 'file_preview')

    def file_preview(self, obj):
        if obj.file and hasattr(obj.file, 'url'):
            file_url = obj.file.url
            if obj.file_type in ['image/jpeg', 'image/png', 'image/gif']:
                return format_html('<a href="{}" target="_blank"><img src="{}" width="100" /></a>', file_url, file_url)
            else:
                return format_html('<a href="{}" target="_blank">View File</a>', file_url)
        return "No file"

    file_preview.short_description = 'Preview'


class ProgressNoteInline(admin.TabularInline):
    model = ProgressNote
    extra = 0
    fields = ('note', 'created_by', 'created_at')
    readonly_fields = ('created_at',)


@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    def has_module_permission(self, request):
        # Allow staff and admin users to access the module
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_view_permission(self, request, obj=None):
        # Allow staff and admin users to view all projects
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_add_permission(self, request):
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_change_permission(self, request, obj=None):
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_delete_permission(self, request, obj=None):
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())
    list_display = ('name', 'client_link', 'status',
                    'progress_bar', 'created_at', 'file_count')
    list_filter = ('status', 'created_at', 'client')
    search_fields = ('name', 'description',
                     'client__username', 'client__email')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [FileInline, ProgressNoteInline]
    actions = [export_as_csv, export_as_json,
               optimized_bulk_delete_projects, chunked_bulk_delete]
    fieldsets = (
        ('Project Information', {
            'fields': ('name', 'description', 'client', 'status', 'progress')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def client_link(self, obj):
        if obj.client:
            url = reverse('admin:users_customuser_change',
                          args=[obj.client.id])
            return format_html('<a href="{}">{}</a>', url, obj.client.username)
        return "-"

    client_link.short_description = 'Client'

    def progress_bar(self, obj):
        return format_html(
            '<div style="width:100px; background-color:#f8f9fa; border-radius:3px;">'
            '<div style="height:20px; border-radius:3px; width:{}%; background-color:{};">'
            '<div style="padding-left:5px; color:white;">{}</div>'
            '</div></div>',
            obj.progress,
            self._get_progress_color(obj.progress),
            f"{obj.progress}%"
        )

    progress_bar.short_description = 'Progress'

    def _get_progress_color(self, progress):
        if progress < 30:
            return '#dc3545'  # Red
        elif progress < 70:
            return '#ffc107'  # Yellow
        else:
            return '#28a745'  # Green

    def file_count(self, obj):
        return obj.files.count()

    file_count.short_description = 'Files'

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.annotate(file_count=Count('files'))
        return queryset


@admin.register(File)
class FileAdmin(admin.ModelAdmin):
    def has_module_permission(self, request):
        # Allow staff and admin users to access the module
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_view_permission(self, request, obj=None):
        # Allow staff and admin users to view all files
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_add_permission(self, request):
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_change_permission(self, request, obj=None):
        if obj and obj.owner == request.user:
            return True
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_delete_permission(self, request, obj=None):
        if obj and obj.owner == request.user:
            return True
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())
    list_display = ('file_name', 'project_link', 'file_type',
                    'owner_link', 'uploaded_at', 'file_preview')
    list_filter = ('file_type', 'uploaded_at', 'owner')
    search_fields = ('file_name', 'project__name', 'owner__username')
    readonly_fields = ('uploaded_at', 'file_preview', 'file_size')
    actions = [export_as_csv, export_as_json, download_files_as_zip,
               optimized_bulk_delete_files, chunked_bulk_delete]
    fieldsets = (
        ('File Information', {
            'fields': ('file', 'file_name', 'file_type', 'project', 'owner')
        }),
        ('File Details', {
            'fields': ('file_preview', 'file_size', 'uploaded_at')
        }),
    )

    def project_link(self, obj):
        if obj.project:
            url = reverse('admin:files_project_change', args=[obj.project.id])
            return format_html('<a href="{}">{}</a>', url, obj.project.name)
        return "-"

    project_link.short_description = 'Project'

    def owner_link(self, obj):
        if obj.owner:
            url = reverse('admin:users_customuser_change', args=[obj.owner.id])
            return format_html('<a href="{}">{}</a>', url, obj.owner.username)
        return "-"

    owner_link.short_description = 'Owner'

    def file_preview(self, obj):
        if obj.file and hasattr(obj.file, 'url'):
            file_url = obj.file.url
            if obj.file_type in ['image/jpeg', 'image/png', 'image/gif']:
                return format_html('<a href="{}" target="_blank"><img src="{}" width="200" /></a>', file_url, file_url)
            else:
                return format_html('<a href="{}" target="_blank" class="button">View File</a>', file_url)
        return "No file"

    file_preview.short_description = 'Preview'

    def file_size(self, obj):
        if obj.file and hasattr(obj.file, 'size'):
            size_bytes = obj.file.size
            if size_bytes < 1024:
                return f"{size_bytes} bytes"
            elif size_bytes < 1024 * 1024:
                return f"{size_bytes / 1024:.2f} KB"
            else:
                return f"{size_bytes / (1024 * 1024):.2f} MB"
        return "Unknown"

    file_size.short_description = 'File Size'


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    def has_module_permission(self, request):
        # Allow staff and admin users to access the module
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_view_permission(self, request, obj=None):
        if obj and obj.user == request.user:
            return True
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_add_permission(self, request):
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_change_permission(self, request, obj=None):
        if obj and obj.user == request.user:
            return True
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_delete_permission(self, request, obj=None):
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())
    list_display = ('message', 'user_link', 'project_link',
                    'notification_type', 'is_read', 'created_at')
    list_filter = ('is_read', 'notification_type', 'created_at')
    search_fields = ('message', 'user__username', 'project__name')
    readonly_fields = ('created_at',)
    list_editable = ('is_read',)
    actions = ['mark_as_read', 'mark_as_unread',
               export_as_csv, export_as_json, chunked_bulk_delete]

    def user_link(self, obj):
        if obj.user:
            url = reverse('admin:users_customuser_change', args=[obj.user.id])
            return format_html('<a href="{}">{}</a>', url, obj.user.username)
        return "-"

    user_link.short_description = 'User'

    def project_link(self, obj):
        if obj.project:
            url = reverse('admin:files_project_change', args=[obj.project.id])
            return format_html('<a href="{}">{}</a>', url, obj.project.name)
        return "-"

    project_link.short_description = 'Project'

    def mark_as_read(self, request, queryset):
        updated = queryset.update(is_read=True)
        self.message_user(request, f"{updated} notifications marked as read.")

    mark_as_read.short_description = "Mark selected notifications as read"

    def mark_as_unread(self, request, queryset):
        updated = queryset.update(is_read=False)
        self.message_user(
            request, f"{updated} notifications marked as unread.")

    mark_as_unread.short_description = "Mark selected notifications as unread"


@admin.register(FileDownload)
class FileDownloadAdmin(admin.ModelAdmin):
    def has_module_permission(self, request):
        # Allow staff and admin users to access the module
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_view_permission(self, request, obj=None):
        # Allow staff and admin users to view all file downloads
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_add_permission(self, request):
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_change_permission(self, request, obj=None):
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_delete_permission(self, request, obj=None):
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    list_display = ('file_link', 'user_link', 'ip_address', 'downloaded_at')
    list_filter = ('downloaded_at', 'user')
    search_fields = ('file__file_name', 'user__username', 'ip_address')
    readonly_fields = ('downloaded_at', 'ip_address', 'user_agent')

    def file_link(self, obj):
        if obj.file:
            url = reverse('admin:files_file_change', args=[obj.file.id])
            return format_html('<a href="{}">{}</a>', url, obj.file.file_name)
        return "-"

    file_link.short_description = 'File'

    def user_link(self, obj):
        if obj.user:
            url = reverse('admin:users_customuser_change', args=[obj.user.id])
            return format_html('<a href="{}">{}</a>', url, obj.user.username)
        return "-"

    user_link.short_description = 'User'


@admin.register(ProgressNote)
class ProgressNoteAdmin(admin.ModelAdmin):
    def has_module_permission(self, request):
        # Allow staff and admin users to access the module
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_view_permission(self, request, obj=None):
        # Allow staff and admin users to view all progress notes
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_add_permission(self, request):
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_change_permission(self, request, obj=None):
        if obj and obj.created_by == request.user:
            return True
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    def has_delete_permission(self, request, obj=None):
        if obj and obj.created_by == request.user:
            return True
        return request.user.is_staff or request.user.is_superuser or (hasattr(request.user, 'is_admin_user') and request.user.is_admin_user())

    list_display = ('note_preview', 'project_link',
                    'created_by_link', 'created_at')
    list_filter = ('created_at', 'created_by')
    search_fields = ('note', 'project__name', 'created_by__username')
    readonly_fields = ('created_at',)

    def note_preview(self, obj):
        if len(obj.note) > 50:
            return f"{obj.note[:50]}..."
        return obj.note

    note_preview.short_description = 'Note'

    def project_link(self, obj):
        if obj.project:
            url = reverse('admin:files_project_change', args=[obj.project.id])
            return format_html('<a href="{}">{}</a>', url, obj.project.name)
        return "-"

    project_link.short_description = 'Project'

    def created_by_link(self, obj):
        if obj.created_by:
            url = reverse('admin:users_customuser_change',
                          args=[obj.created_by.id])
            return format_html('<a href="{}">{}</a>', url, obj.created_by.username)
        return "-"

    created_by_link.short_description = 'Created By'
