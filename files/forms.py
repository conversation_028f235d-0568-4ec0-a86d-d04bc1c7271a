from django import forms
from .models import Project, File, ProgressNote


class ProjectForm(forms.ModelForm):
    """Form for creating and updating projects"""
    class Meta:
        model = Project
        fields = ['name', 'description', 'client', 'status']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 4}),
        }

    # Progress is now automatically calculated based on status

    def __init__(self, *args, is_admin=False, **kwargs):
        super().__init__(*args, **kwargs)
        # Add help texts
        self.fields['name'].help_text = 'Enter a name for the project'
        self.fields['description'].help_text = 'Enter a description for the project'

        # Handle fields based on user role
        if not is_admin:
            # For clients, remove client, status, and progress fields
            if 'client' in self.fields:
                self.fields.pop('client')
            if 'status' in self.fields:
                self.fields.pop('status')
            if 'progress' in self.fields:
                self.fields.pop('progress')
        else:
            # For admins, add help texts
            if 'status' in self.fields:
                self.fields['status'].help_text = 'Select the current status of the project'
            if 'client' in self.fields:
                self.fields['client'].help_text = 'Select the client for this project'
                self.fields['client'].required = True
            if 'progress' in self.fields:
                self.fields[
                    'progress'].help_text = 'Enter the current progress percentage (0-100)'


class ProgressNoteForm(forms.ModelForm):
    class Meta:
        model = ProgressNote
        fields = ['note']
        widgets = {
            'note': forms.Textarea(attrs={'rows': 3, 'class': 'form-control', 'placeholder': 'Add a progress update for the client...'}),
        }


class FileUploadForm(forms.ModelForm):
    """Form for uploading files"""
    class Meta:
        model = File
        fields = ['file', 'file_name', 'notes']
        widgets = {
            'notes': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add help texts
        self.fields['file'].help_text = 'Select a file to upload (max size: 50MB). All file formats are supported. Files will be automatically compressed.'
        self.fields['file_name'].help_text = 'Enter a name for the file or folder (optional - will use original name if left blank)'
        self.fields['notes'].help_text = 'Add any notes or instructions for this file or folder'

    def clean_file(self):
        """Validate file size only"""
        file = self.cleaned_data.get('file')
        if file:
            # Limit file size to 50MB
            if file.size > 52428800:  # 50MB in bytes
                raise forms.ValidationError('File size must be under 50MB')

            # No file format restrictions - all formats are allowed

        return file

    # All file formats are now supported
