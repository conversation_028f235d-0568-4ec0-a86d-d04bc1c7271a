from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('files', '0004_notification_data_notification_notification_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='file',
            name='is_zipped',
            field=models.<PERSON><PERSON>anField(default=True, help_text='Indicates if the file is compressed as a ZIP archive'),
        ),
        migrations.AddField(
            model_name='file',
            name='original_file_name',
            field=models.CharField(blank=True, help_text='Original file name before zipping', max_length=255, null=True),
        ),
    ]
