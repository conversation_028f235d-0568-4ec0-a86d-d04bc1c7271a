# Generated by Django 5.2 on 2025-04-13 00:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('files', '0003_filedownload'),
    ]

    operations = [
        migrations.AddField(
            model_name='notification',
            name='data',
            field=models.JSONField(blank=True, help_text='Additional data for the notification', null=True),
        ),
        migrations.AddField(
            model_name='notification',
            name='notification_type',
            field=models.CharField(choices=[('file_upload', 'File Upload'), ('status_change', 'Status Change'), ('progress_update', 'Progress Update'), ('comment', 'Comment'), ('system', 'System Notification')], default='system', max_length=20),
        ),
    ]
