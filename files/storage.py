from django.conf import settings
from cloudinary_storage.storage import MediaCloudinaryStorage
import cloudinary
import cloudinary.uploader
import cloudinary.api


class CompletoPLUSCloudinaryStorage(MediaCloudinaryStorage):
    """
    Custom storage class for Cloudinary that adds additional functionality
    specific to CompletoPLUS.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Configure Cloudinary
        cloudinary.config(
            cloud_name=settings.CLOUDINARY_STORAGE['CLOUD_NAME'],
            api_key=settings.CLOUDINARY_STORAGE['API_KEY'],
            api_secret=settings.CLOUDINARY_STORAGE['API_SECRET']
        )

    def create_user_folder(self, user):
        """
        Create a folder for the user in Cloudinary if it doesn't exist.
        This can be called when a user is created or when they first upload a file.
        """
        if user and hasattr(user, 'id'):
            username = user.username if hasattr(
                user, 'username') else f'user_{user.id}'
            folder_name = f'{username}_{user.id}'

            # First, check if the main COMPLETOPLUS folder exists
            try:
                # Try to access the main folder
                main_folder_result = cloudinary.api.root_folders()
                main_folder_exists = any(
                    folder['name'] == 'COMPLETOPLUS' for folder in main_folder_result['folders'])

                if not main_folder_exists:
                    # Log that we're using an existing folder
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.info(
                        "Using existing COMPLETOPLUS folder in Cloudinary")
            except Exception as e:
                # Log the error but continue
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error checking main COMPLETOPLUS folder: {e}")

            # Now create the user folder inside COMPLETOPLUS
            try:
                # Check if the user folder exists
                try:
                    result = cloudinary.api.subfolders("COMPLETOPLUS")
                    user_folder_exists = any(
                        folder['name'] == folder_name for folder in result['folders'])

                    if user_folder_exists:
                        return True
                except Exception as e:
                    # Subfolder check failed, but we'll try to create it anyway
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"Error checking user subfolder: {e}")

                # Create the user folder by uploading a placeholder file
                import tempfile
                with tempfile.NamedTemporaryFile(suffix='.txt') as temp:
                    temp.write(b'placeholder')
                    temp.flush()

                    # Upload to COMPLETOPLUS/username_userid/.placeholder
                    placeholder = cloudinary.uploader.upload(
                        temp.name,
                        # Specify the folder path
                        folder=f"COMPLETOPLUS/{folder_name}",
                        public_id=".placeholder",  # Just the filename
                        resource_type="raw",
                        use_filename=False,
                        unique_filename=False,
                        overwrite=True
                    )

                # Log success
                import logging
                logger = logging.getLogger(__name__)
                logger.info(f"Created user folder COMPLETOPLUS/{folder_name}")

                return True
            except Exception as e:
                # Log the error but don't raise it
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error creating user folder in Cloudinary: {e}")
                return False
        return False

    def delete_user_folder(self, user):
        """
        Delete a user's folder in Cloudinary.
        This should be called when a user is deleted from the system.

        Args:
            user: The user whose folder should be deleted

        Returns:
            bool: True if the folder was deleted successfully, False otherwise
        """
        import logging
        logger = logging.getLogger(__name__)

        if user and hasattr(user, 'id'):
            username = user.username if hasattr(
                user, 'username') else f'user_{user.id}'
            folder_name = f'{username}_{user.id}'
            folder_path = f"COMPLETOPLUS/{folder_name}"

            logger.info(
                f"Attempting to delete Cloudinary folder: {folder_path}")

            try:
                # First check if the folder exists
                try:
                    result = cloudinary.api.subfolders("COMPLETOPLUS")
                    user_folder_exists = any(
                        folder['name'] == folder_name for folder in result['folders'])

                    if not user_folder_exists:
                        logger.info(
                            f"Folder {folder_path} does not exist, nothing to delete")
                        return True
                except Exception as e:
                    logger.warning(
                        f"Error checking if user folder exists: {e}")
                    # Continue anyway to try deletion

                # Delete all resources in the folder first
                try:
                    # Get all resources in the folder - try different resource types
                    for resource_type in ["image", "raw", "video"]:
                        try:
                            result = cloudinary.api.resources(
                                resource_type=resource_type,
                                type="upload",
                                prefix=folder_path,
                                max_results=500
                            )

                            # Delete each resource
                            if 'resources' in result and result['resources']:
                                for resource in result['resources']:
                                    public_id = resource['public_id']
                                    cloudinary.uploader.destroy(
                                        public_id, resource_type=resource_type)
                                    logger.info(
                                        f"Deleted {resource_type} resource: {public_id}")
                        except Exception as e:
                            logger.warning(
                                f"Error deleting {resource_type} resources in folder: {e}")
                except Exception as e:
                    logger.warning(f"Error in resource deletion process: {e}")

                # Since Cloudinary doesn't have a direct "delete folder" API,
                # we consider the operation successful if we deleted at least one resource
                # or if the folder doesn't exist anymore
                try:
                    # Check if any resources were deleted
                    resources_deleted = False

                    # Try to create a dummy file and then delete it to force folder removal
                    try:
                        import tempfile
                        with tempfile.NamedTemporaryFile(suffix='.txt') as temp:
                            temp.write(b'delete_folder')
                            temp.flush()

                            # Upload a dummy file to the folder
                            dummy_file = cloudinary.uploader.upload(
                                temp.name,
                                folder=folder_path,
                                public_id="delete_me",
                                resource_type="raw",
                                use_filename=False,
                                unique_filename=False,
                                overwrite=True
                            )

                            # Delete the dummy file
                            if 'public_id' in dummy_file:
                                cloudinary.uploader.destroy(
                                    dummy_file['public_id'], resource_type="raw")
                                logger.info(
                                    f"Created and deleted dummy file to force folder removal")
                                resources_deleted = True
                    except Exception as e:
                        logger.warning(f"Error with dummy file approach: {e}")

                    # Check if the folder still exists
                    try:
                        result = cloudinary.api.subfolders("COMPLETOPLUS")
                        user_folder_exists = any(
                            folder['name'] == folder_name for folder in result['folders'])

                        if not user_folder_exists:
                            logger.info(
                                f"Successfully deleted folder {folder_path}")
                            return True
                        elif resources_deleted:
                            # If we deleted resources but the folder still exists,
                            # consider it a success since Cloudinary might have a delay
                            # in removing empty folders
                            logger.info(
                                f"Resources deleted from {folder_path}, folder may be removed later")
                            return True
                        else:
                            logger.warning(
                                f"Folder {folder_path} still exists and no resources were deleted")
                            return False
                    except Exception as e:
                        if resources_deleted:
                            # If we can't check but we deleted resources, consider it a success
                            logger.info(
                                f"Resources deleted, but couldn't check folder status: {e}")
                            return True
                        else:
                            logger.error(
                                f"Error checking if folder was deleted: {e}")
                            return False
                except Exception as e:
                    logger.error(f"Error in folder deletion process: {e}")
                    return False

            except Exception as e:
                logger.error(f"Error deleting user folder in Cloudinary: {e}")
                return False

        return False

    def delete_folder_by_path(self, folder_path):
        """
        Delete all files in a specific folder path from Cloudinary.
        This method can be used for both individual and batch deletions.
        """
        import logging
        logger = logging.getLogger(__name__)

        logger.info(f"Attempting to delete Cloudinary folder: {folder_path}")

        try:
            # Delete all resources in the folder - try different resource types
            resources_deleted = False
            for resource_type in ["image", "raw", "video"]:
                try:
                    result = cloudinary.api.resources(
                        resource_type=resource_type,
                        type="upload",
                        prefix=folder_path,
                        max_results=500
                    )

                    # Delete each resource
                    if 'resources' in result and result['resources']:
                        for resource in result['resources']:
                            public_id = resource['public_id']
                            cloudinary.uploader.destroy(
                                public_id, resource_type=resource_type)
                            logger.info(
                                f"Deleted {resource_type} resource: {public_id}")
                            resources_deleted = True
                except Exception as e:
                    logger.warning(
                        f"Error deleting {resource_type} resources in folder: {e}")

            # Try to create a dummy file and then delete it to force folder removal
            try:
                import tempfile
                with tempfile.NamedTemporaryFile(suffix='.txt') as temp:
                    temp.write(b'delete_folder')
                    temp.flush()

                    # Upload a dummy file to the folder
                    dummy_file = cloudinary.uploader.upload(
                        temp.name,
                        folder=folder_path,
                        public_id="delete_me",
                        resource_type="raw",
                        use_filename=False,
                        unique_filename=False,
                        overwrite=True
                    )

                    # Delete the dummy file
                    if 'public_id' in dummy_file:
                        cloudinary.uploader.destroy(
                            dummy_file['public_id'], resource_type="raw")
                        logger.info(
                            f"Created and deleted dummy file to force folder removal")
                        resources_deleted = True
            except Exception as e:
                logger.warning(f"Error with dummy file approach: {e}")

            if resources_deleted:
                logger.info(
                    f"Resources deleted from {folder_path}, folder may be removed later")
                return True
            else:
                logger.warning(
                    f"No resources found to delete in {folder_path}")
                return True  # Consider it successful if folder is empty

        except Exception as e:
            logger.error(f"Error deleting folder {folder_path}: {e}")
            return False

    def url(self, name):
        """
        Return the URL for the file. For Cloudinary, this will be the secure URL.
        """
        url = super().url(name)
        return url

    def get_available_name(self, name, max_length=None):
        """
        Return a filename that's free on the target storage system.
        For Cloudinary, we'll use the original name but ensure it's unique.
        """
        return super().get_available_name(name, max_length)

    def _save(self, name, content):
        """
        Save the file to Cloudinary with additional options.
        """
        # Extract folder structure from the name
        # The name should already include the COMPLETOPLUS/username_userid/ structure
        # from the user_directory_path function

        # Parse the path to extract folder and filename
        import os
        path_parts = name.split('/')

        # Handle different path formats
        if name.startswith('COMPLETOPLUS/'):
            # Format: COMPLETOPLUS/username_userid/filename
            if len(path_parts) >= 3:
                # Everything except the filename
                folder = '/'.join(path_parts[:-1])
                filename = path_parts[-1]           # Just the filename
            else:
                # Not enough parts, use default
                folder = 'COMPLETOPLUS/default'
                filename = os.path.basename(name)
        else:
            # Not starting with COMPLETOPLUS, use default folder
            folder = 'COMPLETOPLUS/default'
            filename = os.path.basename(name)

        # Log what we're doing
        import logging
        logger = logging.getLogger(__name__)
        logger.info(
            f"Uploading file to Cloudinary: folder={folder}, filename={filename}")

        # Configure Cloudinary
        cloudinary.config(
            cloud_name=settings.CLOUDINARY_STORAGE['CLOUD_NAME'],
            api_key=settings.CLOUDINARY_STORAGE['API_KEY'],
            api_secret=settings.CLOUDINARY_STORAGE['API_SECRET']
        )

        # Upload directly using cloudinary.uploader
        try:
            # For file-like objects, we need to handle them specially
            if hasattr(content, 'file') and hasattr(content.file, 'name'):
                # Django's UploadedFile or similar
                file_to_upload = content.file.name
            elif hasattr(content, 'name'):
                # File-like object with name
                file_to_upload = content.name
            elif hasattr(content, 'temporary_file_path'):
                # TemporaryUploadedFile
                file_to_upload = content.temporary_file_path()
            else:
                # Last resort: write to a temporary file
                import tempfile
                with tempfile.NamedTemporaryFile(delete=False) as temp:
                    for chunk in content.chunks():
                        temp.write(chunk)
                    file_to_upload = temp.name

            # Upload the file
            result = cloudinary.uploader.upload(
                file_to_upload,
                folder=folder,
                # Filename without extension
                public_id=os.path.splitext(filename)[0],
                resource_type="auto",
                use_filename=True,
                unique_filename=True,
                overwrite=False
            )

            # Return the public_id which is used as the name
            return result['public_id']

        except Exception as e:
            # Log the error and fall back to the default implementation
            logger.error(f"Error uploading to Cloudinary directly: {e}")
            # Fall back to the parent class implementation
            return super()._save(name, content)
