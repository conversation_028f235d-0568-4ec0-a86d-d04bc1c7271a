from django.db import models
from django.conf import settings
import os
import uuid
import json
from django.utils import timezone


def user_directory_path(instance, filename):
    """
    File will be uploaded to COMPLETOPLUS/username_userid/filename
    This ensures files are stored in the existing COMPLETOPLUS folder in Cloudinary
    """
    # Create a clean filename to avoid path traversal issues
    import re
    clean_filename = re.sub(r'[^\w\.-]', '_', filename)

    # Get the username to create a more readable folder name
    username = instance.owner.username if hasattr(
        instance.owner, 'username') else f'user_{instance.owner.id}'

    # Format: COMPLETOPLUS/username_userid/filename
    # This will place files in the existing COMPLETOPLUS folder
    return f'COMPLETOPLUS/{username}_{instance.owner.id}/{clean_filename}'


class Project(models.Model):
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    client = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='projects')
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    progress = models.IntegerField(
        default=0, help_text="Progress percentage (0-100)")

    def __str__(self):
        return self.name

    def update_progress_from_status(self):
        """Update progress based on status"""
        if self.status == 'pending':
            self.progress = 0
        elif self.status == 'in_progress':
            # If already in progress but progress is still 0, set to 50%
            # Otherwise, keep the current progress
            if self.progress == 0:
                self.progress = 50
        elif self.status == 'completed':
            self.progress = 100
        return self.progress

    def save(self, *args, **kwargs):
        """Override save to update progress based on status"""
        self.update_progress_from_status()
        super().save(*args, **kwargs)


class File(models.Model):
    FILE_TYPE_CHOICES = (
        ('client_upload', 'Client Upload'),
        ('admin_upload', 'Admin Upload'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    project = models.ForeignKey(
        Project, on_delete=models.CASCADE, related_name='files')
    file = models.FileField(upload_to=user_directory_path)
    file_name = models.CharField(max_length=255)
    file_type = models.CharField(max_length=20, choices=FILE_TYPE_CHOICES)
    owner = models.ForeignKey(settings.AUTH_USER_MODEL,
                              on_delete=models.CASCADE, related_name='files')
    notes = models.TextField(blank=True, null=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    is_zipped = models.BooleanField(
        default=True, help_text="Indicates if the file is compressed as a ZIP archive")
    original_file_name = models.CharField(
        max_length=255, blank=True, null=True, help_text="Original file name before zipping")

    def __str__(self):
        return self.file_name

    def get_file_extension(self):
        return os.path.splitext(self.file.name)[1]

    def get_file_size(self):
        if self.file and hasattr(self.file, 'size'):
            return self.file.size
        return 0

    def get_size_display(self):
        """Return a human-readable file size."""
        size_bytes = self.get_file_size()
        if size_bytes < 1024:
            return f"{size_bytes} bytes"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"

    @property
    def icon_class(self):
        """Return the appropriate Font Awesome icon class based on file extension."""
        extension = self.get_file_extension().lower()

        # Document types
        if extension in ['.doc', '.docx']:
            return 'fas fa-file-word'
        elif extension in ['.xls', '.xlsx']:
            return 'fas fa-file-excel'
        elif extension in ['.ppt', '.pptx']:
            return 'fas fa-file-powerpoint'
        elif extension == '.pdf':
            return 'fas fa-file-pdf'
        elif extension in ['.txt', '.rtf']:
            return 'fas fa-file-alt'

        # Image types
        elif extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg']:
            return 'fas fa-file-image'

        # Archive types
        elif extension in ['.zip', '.rar', '.7z', '.tar', '.gz']:
            return 'fas fa-file-archive'

        # Code types
        elif extension in ['.html', '.css', '.js', '.py', '.java', '.php', '.c', '.cpp', '.h']:
            return 'fas fa-file-code'

        # Audio/Video types
        elif extension in ['.mp3', '.wav', '.ogg', '.flac']:
            return 'fas fa-file-audio'
        elif extension in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']:
            return 'fas fa-file-video'

        # Default
        return 'fas fa-file'

    @property
    def can_preview(self):
        """Determine if the file can be previewed in the browser."""
        extension = self.get_file_extension().lower()

        # Previewable document types
        if extension in ['.pdf', '.doc', '.docx']:
            return True

        # Image types
        if extension in ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.bmp']:
            return True

        # Text types
        if extension in ['.txt', '.md', '.html', '.css', '.js', '.py', '.java', '.c', '.cpp', '.h', '.json', '.xml', '.csv', '.rtf']:
            return True

        # Audio/Video types
        if extension in ['.mp3', '.wav', '.ogg', '.mp4', '.webm', '.avi', '.mov', '.flv', '.mkv']:
            return True

        # Spreadsheet and presentation types
        if extension in ['.xls', '.xlsx', '.ppt', '.pptx']:
            return True

        # Always return True to allow preview for all files
        # If the file can't be previewed directly, we'll show a download prompt
        return True


class ProgressNote(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    project = models.ForeignKey(
        Project, on_delete=models.CASCADE, related_name='progress_notes')
    note = models.TextField()
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='progress_notes')
    created_at = models.DateTimeField(auto_now_add=True)
    silent_update = models.BooleanField(
        default=False, help_text="If True, no notification will be sent for this note")
    is_progress_update = models.BooleanField(
        default=False, help_text="If True, this note is a progress update")

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Progress note for {self.project.name} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"


class Notification(models.Model):
    NOTIFICATION_TYPES = (
        ('file_upload', 'File Upload'),
        ('status_change', 'Status Change'),
        ('progress_update', 'Progress Update'),
        ('comment', 'Comment'),
        ('system', 'System Notification'),
        ('welcome', 'Welcome Notification'),
    )

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='notifications')
    project = models.ForeignKey(
        Project, on_delete=models.CASCADE, related_name='notifications', null=True, blank=True)
    message = models.CharField(max_length=255)
    notification_type = models.CharField(
        max_length=20, choices=NOTIFICATION_TYPES, default='system')
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    data = models.JSONField(blank=True, null=True,
                            help_text="Additional data for the notification")

    def __str__(self):
        return f"{self.message} - {self.user.username}"

    # Standard save method without WebSocket notification
    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-created_at']


class FileDownload(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    file = models.ForeignKey(
        File, on_delete=models.CASCADE, related_name='downloads')
    user = models.ForeignKey(settings.AUTH_USER_MODEL,
                             on_delete=models.CASCADE, related_name='downloads')
    downloaded_at = models.DateTimeField(auto_now_add=True)
    ip_address = models.GenericIPAddressField(blank=True, null=True)
    user_agent = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.file.file_name} downloaded by {self.user.username} on {self.downloaded_at.strftime('%Y-%m-%d %H:%M')}"

    class Meta:
        ordering = ['-downloaded_at']
