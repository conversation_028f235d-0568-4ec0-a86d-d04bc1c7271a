#!/usr/bin/env python
"""
Test script to verify email uniqueness validation in the CompletoPLUS registration system.
"""

from users.forms import CustomUserCreationForm
from django.contrib.auth import get_user_model
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append('/Users/<USER>/Desktop/CompletoPLUS')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'fileshare.settings')
django.setup()


User = get_user_model()


def test_email_uniqueness():
    """Test email uniqueness validation"""
    print("Testing email uniqueness validation...")

    # Test 1: Create a user with a unique email
    print("\n1. Testing unique email registration...")
    test_email = "<EMAIL>"

    # Clean up any existing test user
    User.objects.filter(email__iexact=test_email).delete()

    form_data = {
        'username': 'testuser1',
        'email': test_email,
        'first_name': 'Test',
        'last_name': 'User',
        'password1': 'ComplexPassword123!',
        'password2': 'ComplexPassword123!',
        'user_type': 'client',
        'terms_agreement': True,
    }

    form = CustomUserCreationForm(data=form_data)
    if form.is_valid():
        user = form.save()
        print(f"✓ Successfully created user with email: {user.email}")
    else:
        print(f"✗ Form validation failed: {form.errors}")
        return False

    # Test 2: Try to create another user with the same email (exact case)
    print("\n2. Testing duplicate email (exact case)...")
    form_data['username'] = 'testuser2'

    form = CustomUserCreationForm(data=form_data)
    if not form.is_valid() and 'email' in form.errors:
        print(f"✓ Correctly rejected duplicate email: {form.errors['email']}")
    else:
        print(
            f"✗ Should have rejected duplicate email but didn't: {form.errors}")
        return False

    # Test 3: Try to create another user with the same email (different case)
    print("\n3. Testing duplicate email (case-insensitive)...")
    form_data['email'] = test_email.upper()  # Change to uppercase
    form_data['username'] = 'testuser3'

    form = CustomUserCreationForm(data=form_data)
    if not form.is_valid() and 'email' in form.errors:
        print(
            f"✓ Correctly rejected case-insensitive duplicate email: {form.errors['email']}")
    else:
        print(
            f"✗ Should have rejected case-insensitive duplicate email: {form.errors}")
        return False

    # Test 4: Test reserved admin email
    print("\n4. Testing reserved admin email...")
    form_data['email'] = '<EMAIL>'
    form_data['username'] = 'testuser4'

    form = CustomUserCreationForm(data=form_data)
    if not form.is_valid() and 'email' in form.errors:
        print(
            f"✓ Correctly rejected reserved admin email: {form.errors['email']}")
    else:
        print(f"✗ Should have rejected reserved admin email: {form.errors}")
        return False

    # Test 5: Test reserved admin email (case-insensitive)
    print("\n5. Testing reserved admin email (case-insensitive)...")
    form_data['email'] = '<EMAIL>'
    form_data['username'] = 'testuser5'

    form = CustomUserCreationForm(data=form_data)
    if not form.is_valid() and 'email' in form.errors:
        print(
            f"✓ Correctly rejected reserved admin email (case-insensitive): {form.errors['email']}")
    else:
        print(
            f"✗ Should have rejected reserved admin email (case-insensitive): {form.errors}")
        return False

    # Clean up test user
    User.objects.filter(email__iexact=test_email).delete()
    print("\n✓ All email uniqueness tests passed!")
    return True


def test_username_uniqueness():
    """Test username uniqueness validation"""
    print("\nTesting username uniqueness validation...")

    # Test 1: Create a user with a unique username
    print("\n1. Testing unique username registration...")
    test_username = "testuser_unique"

    # Clean up any existing test user
    User.objects.filter(username=test_username).delete()

    form_data = {
        'username': test_username,
        'email': '<EMAIL>',
        'first_name': 'Test',
        'last_name': 'User',
        'password1': 'ComplexPassword123!',
        'password2': 'ComplexPassword123!',
        'user_type': 'client',
        'terms_agreement': True,
    }

    form = CustomUserCreationForm(data=form_data)
    if form.is_valid():
        user = form.save()
        print(f"✓ Successfully created user with username: {user.username}")
    else:
        print(f"✗ Form validation failed: {form.errors}")
        return False

    # Test 2: Try to create another user with the same username
    print("\n2. Testing duplicate username...")
    form_data['email'] = '<EMAIL>'

    form = CustomUserCreationForm(data=form_data)
    if not form.is_valid() and 'username' in form.errors:
        print(
            f"✓ Correctly rejected duplicate username: {form.errors['username']}")
    else:
        print(f"✗ Should have rejected duplicate username: {form.errors}")
        return False

    # Test 3: Test reserved admin username
    print("\n3. Testing reserved admin username...")
    form_data['username'] = 'completoplus'
    form_data['email'] = '<EMAIL>'

    form = CustomUserCreationForm(data=form_data)
    if not form.is_valid() and 'username' in form.errors:
        print(
            f"✓ Correctly rejected reserved admin username: {form.errors['username']}")
    else:
        print(f"✗ Should have rejected reserved admin username: {form.errors}")
        return False

    # Clean up test user
    User.objects.filter(username=test_username).delete()
    print("\n✓ All username uniqueness tests passed!")
    return True


if __name__ == "__main__":
    print("CompletoPLUS Email Uniqueness Validation Test")
    print("=" * 50)

    try:
        email_test_passed = test_email_uniqueness()
        username_test_passed = test_username_uniqueness()

        if email_test_passed and username_test_passed:
            print("\n🎉 All validation tests passed successfully!")
            print("Email uniqueness validation is working correctly.")
        else:
            print("\n❌ Some tests failed. Please check the implementation.")
            sys.exit(1)

    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
