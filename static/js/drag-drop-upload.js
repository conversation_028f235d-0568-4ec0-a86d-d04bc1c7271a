// Enhanced drag and drop file upload functionality
document.addEventListener("DOMContentLoaded", function () {
  const fileUploadAreas = document.querySelectorAll(".file-upload-area");

  fileUploadAreas.forEach((uploadArea) => {
    const fileInput = uploadArea.querySelector("input#id_file");
    const fileNameInput = document.getElementById("id_file_name");
    const uploadForm = uploadArea.closest("form");
    const projectId = uploadArea.dataset.projectId;
    const uploadProgress = document.getElementById("upload-progress");
    const progressBar = uploadProgress
      ? uploadProgress.querySelector(".progress-bar")
      : null;

    // Function to show upload progress
    function showProgress(percent) {
      if (uploadProgress && progressBar) {
        uploadProgress.classList.remove("d-none");
        progressBar.style.width = percent + "%";
        progressBar.setAttribute("aria-valuenow", percent);

        if (percent === 100) {
          progressBar.classList.add("bg-success");
          progressBar.textContent = "Processing...";
        } else {
          progressBar.textContent = percent + "%";
        }
      }
    }

    // Function to reset progress bar
    function resetProgress() {
      if (uploadProgress && progressBar) {
        uploadProgress.classList.add("d-none");
        progressBar.style.width = "0%";
        progressBar.setAttribute("aria-valuenow", 0);
        progressBar.classList.remove("bg-success");
        progressBar.textContent = "";
      }
    }

    // Add a hidden input to indicate this is a file upload that should be zipped
    let isZipFileInput = uploadForm.querySelector('input[name="zip_file"]');
    if (!isZipFileInput) {
      isZipFileInput = document.createElement("input");
      isZipFileInput.type = "hidden";
      isZipFileInput.name = "zip_file";
      isZipFileInput.value = "true";
      uploadForm.appendChild(isZipFileInput);
    }

    if (!fileInput) return;

    // Handle drag and drop events
    ["dragenter", "dragover", "dragleave", "drop"].forEach((eventName) => {
      uploadArea.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Add visual feedback for drag events
    ["dragenter", "dragover"].forEach((eventName) => {
      uploadArea.addEventListener(eventName, highlight, false);
    });

    ["dragleave", "drop"].forEach((eventName) => {
      uploadArea.addEventListener(eventName, unhighlight, false);
    });

    function highlight() {
      uploadArea.classList.add("border-primary");
      uploadArea.classList.add("bg-light");
      uploadArea.querySelector(".drag-text").classList.add("text-primary");
    }

    function unhighlight() {
      uploadArea.classList.remove("border-primary");
      uploadArea.classList.remove("bg-light");
      uploadArea.querySelector(".drag-text").classList.remove("text-primary");
    }

    // Handle dropped files
    uploadArea.addEventListener("drop", handleDrop, false);

    function handleDrop(e) {
      const dt = e.dataTransfer;
      const files = dt.files;

      if (files.length > 0) {
        fileInput.files = files;

        // Update file name input if it exists and is empty
        if (fileNameInput && fileNameInput.value === "") {
          if (files.length === 1) {
            // Single file - use its name
            const fileName = files[0].name.replace(/\.[^/.]+$/, "");
            fileNameInput.value = fileName;
          } else {
            // Multiple files - use a generic name
            fileNameInput.value = "Multiple Files";
          }
        }

        // Add a hidden input to indicate this is a file upload that should be zipped
        let isZipFileInput = uploadForm.querySelector('input[name="zip_file"]');
        if (!isZipFileInput) {
          isZipFileInput = document.createElement("input");
          isZipFileInput.type = "hidden";
          isZipFileInput.name = "zip_file";
          uploadForm.appendChild(isZipFileInput);
        }
        isZipFileInput.value = "true";

        // Update the upload button text
        const uploadButton = document.getElementById("upload-button");
        if (uploadButton) {
          uploadButton.innerHTML =
            '<i class="fas fa-upload me-2"></i>Upload File';
        }

        // Show file info
        updateFileInfo(files[0]);

        // Auto-submit if configured to do so
        if (uploadArea.dataset.autoSubmit === "true") {
          uploadForm.submit();
        }
      }
    }

    // Also handle regular file input change
    fileInput.addEventListener("change", function () {
      if (this.files.length > 0) {
        // Update file name input if it exists and is empty
        if (fileNameInput && fileNameInput.value === "") {
          if (this.files.length === 1) {
            // Single file - use its name
            const fileName = this.files[0].name.replace(/\.[^/.]+$/, "");
            fileNameInput.value = fileName;
          } else {
            // Multiple files - use a generic name
            fileNameInput.value = "Multiple Files";
          }
        }

        // Add a hidden input to indicate this is a file upload that should be zipped
        let isZipFileInput = uploadForm.querySelector('input[name="zip_file"]');
        if (!isZipFileInput) {
          isZipFileInput = document.createElement("input");
          isZipFileInput.type = "hidden";
          isZipFileInput.name = "zip_file";
          uploadForm.appendChild(isZipFileInput);
        }
        isZipFileInput.value = "true";

        // Update the upload button text
        const uploadButton = document.getElementById("upload-button");
        if (uploadButton) {
          uploadButton.innerHTML =
            '<i class="fas fa-upload me-2"></i>Upload File';
        }

        // Show file info
        updateFileInfo(this.files[0]);
      }
    });

    // Function to display file information
    function updateFileInfo(file) {
      // If file is actually a FileList (multiple files), handle differently
      if (
        file instanceof FileList ||
        (file.length !== undefined && file.length > 1)
      ) {
        updateMultipleFilesInfo(file);
        return;
      }

      // Create or update file info display
      let fileInfoDiv = document.getElementById("file-info");
      if (!fileInfoDiv) {
        fileInfoDiv = document.createElement("div");
        fileInfoDiv.id = "file-info";
        fileInfoDiv.className = "alert alert-info mt-3";
        uploadArea.parentNode.insertBefore(fileInfoDiv, uploadArea.nextSibling);
      }

      // Format file size
      const fileSize = formatFileSize(file.size);

      // Determine file icon based on extension
      const extension = file.name.split(".").pop().toLowerCase();
      let fileIcon = "fa-file";

      if (["jpg", "jpeg", "png", "gif", "svg", "webp"].includes(extension)) {
        fileIcon = "fa-file-image";
      } else if (["doc", "docx"].includes(extension)) {
        fileIcon = "fa-file-word";
      } else if (["xls", "xlsx"].includes(extension)) {
        fileIcon = "fa-file-excel";
      } else if (["ppt", "pptx"].includes(extension)) {
        fileIcon = "fa-file-powerpoint";
      } else if (["pdf"].includes(extension)) {
        fileIcon = "fa-file-pdf";
      } else if (["zip", "rar", "7z", "tar", "gz"].includes(extension)) {
        fileIcon = "fa-file-archive";
      } else if (["mp3", "wav", "ogg"].includes(extension)) {
        fileIcon = "fa-file-audio";
      } else if (["mp4", "avi", "mov", "wmv"].includes(extension)) {
        fileIcon = "fa-file-video";
      } else if (["txt", "md"].includes(extension)) {
        fileIcon = "fa-file-alt";
      } else if (
        ["html", "css", "js", "py", "java", "c", "cpp", "php"].includes(
          extension
        )
      ) {
        fileIcon = "fa-file-code";
      }

      // Update file info content
      fileInfoDiv.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas ${fileIcon} fa-2x text-primary"></i>
                    </div>
                    <div>
                        <h5 class="mb-1">${file.name}</h5>
                        <p class="mb-0 text-muted">${fileSize}</p>
                    </div>
                    <button type="button" class="btn-close ms-auto" aria-label="Close" id="clear-file"></button>
                </div>
            `;

      // Add clear button functionality
      const clearButton = document.getElementById("clear-file");
      if (clearButton) {
        clearButton.addEventListener("click", function () {
          fileInput.value = "";
          fileInfoDiv.remove();
          if (fileNameInput) {
            fileNameInput.value = "";
          }
        });
      }
    }

    // Function to display information for multiple files
    function updateMultipleFilesInfo(files) {
      // Create or update file info display
      let fileInfoDiv = document.getElementById("file-info");
      if (!fileInfoDiv) {
        fileInfoDiv = document.createElement("div");
        fileInfoDiv.id = "file-info";
        fileInfoDiv.className = "alert alert-info mt-3";
        uploadArea.parentNode.insertBefore(fileInfoDiv, uploadArea.nextSibling);
      }

      // Calculate total size
      let totalSize = 0;
      for (let i = 0; i < files.length; i++) {
        totalSize += files[i].size;
      }

      // Format total size
      const totalSizeFormatted = formatFileSize(totalSize);

      // Update file info content
      fileInfoDiv.innerHTML = `
        <div class="d-flex align-items-center">
          <div class="me-3">
            <i class="fas fa-file-archive fa-2x text-primary"></i>
          </div>
          <div>
            <h5 class="mb-1">Multiple Files Selected</h5>
            <p class="mb-0 text-muted">${files.length} files (${totalSizeFormatted})</p>
          </div>
          <button type="button" class="btn-close ms-auto" aria-label="Close" id="clear-files"></button>
        </div>
      `;

      // Add clear button functionality
      const clearButton = document.getElementById("clear-files");
      if (clearButton) {
        clearButton.addEventListener("click", function () {
          fileInput.value = "";
          fileInfoDiv.remove();
          if (fileNameInput) {
            fileNameInput.value = "";
          }
        });
      }
    }

    // Handle form submission
    if (uploadForm) {
      uploadForm.addEventListener("submit", function (e) {
        e.preventDefault();

        // Show progress indicator
        resetProgress();
        showProgress(10);

        // Check if we should zip the file
        const shouldZipFile =
          uploadForm.querySelector('input[name="zip_file"]')?.value === "true";
        const formData = new FormData(uploadForm);

        // If we have files to zip
        if (fileInput && fileInput.files.length > 0 && shouldZipFile) {
          // Get the files
          const files = fileInput.files;

          // Set the file name if not already set
          if (!formData.has("file_name") || formData.get("file_name") === "") {
            if (files.length === 1) {
              formData.set("file_name", files[0].name.replace(/\.[^/.]+$/, ""));
            } else {
              formData.set("file_name", "Multiple Files");
            }
          }

          // Create a zip file client-side using JSZip
          const jszip = new JSZip();

          // Add files to the zip
          const addFilesToZip = async () => {
            // Update progress
            showProgress(20);

            // Process each file
            for (let i = 0; i < files.length; i++) {
              const file = files[i];
              const fileName = file.name;

              // Update progress based on file count
              const progressPercent = Math.floor(20 + (i / files.length) * 30);
              showProgress(progressPercent);

              // Read the file and add it to the zip
              const fileContent = await readFileAsync(file);
              jszip.file(fileName, fileContent);
            }

            // Generate the zip file
            showProgress(60);
            const zipBlob = await jszip.generateAsync(
              {
                type: "blob",
                compression: "DEFLATE",
                compressionOptions: { level: 6 },
              },
              function (metadata) {
                // Update progress during compression
                const percent = Math.floor(60 + metadata.percent * 0.2);
                showProgress(percent);
              }
            );
            showProgress(80);

            // Create a File object from the Blob
            const zipFileName = formData.get("file_name");
            const zipFile = new File([zipBlob], zipFileName + ".zip", {
              type: "application/zip",
            });

            // Remove the original files from the form data
            formData.delete("file");

            // Add the zip file to the form data
            formData.append("file", zipFile);
            formData.append("is_zipped", "true");

            // Send the form data
            const xhr = new XMLHttpRequest();
            xhr.open("POST", uploadForm.action, true);
            sendFormData(formData, xhr);
          };

          // Helper function to read a file as ArrayBuffer
          const readFileAsync = (file) => {
            return new Promise((resolve, reject) => {
              const reader = new FileReader();
              reader.onload = () => resolve(reader.result);
              reader.onerror = reject;
              reader.readAsArrayBuffer(file);
            });
          };

          // Load JSZip library if not already loaded
          if (typeof JSZip === "undefined") {
            const script = document.createElement("script");
            script.src =
              "https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js";
            script.onload = () => addFilesToZip();
            document.head.appendChild(script);
          } else {
            addFilesToZip();
          }

          return; // Don't proceed with the normal form submission
        }

        // Send the form data via AJAX for non-zipped files (fallback)
        const xhr = new XMLHttpRequest();
        xhr.open("POST", uploadForm.action, true);

        // Send the form data
        sendFormData(formData, xhr);
      });
    }

    // Function to send form data via AJAX
    function sendFormData(formData, xhr) {
      // Track upload progress
      xhr.upload.onprogress = function (e) {
        if (e.lengthComputable) {
          const percentComplete = Math.round((e.loaded / e.total) * 90);
          showProgress(percentComplete);
        }
      };

      xhr.onload = function () {
        if (xhr.status === 200) {
          showProgress(100);
          // Reload the page after a short delay to show the uploaded file
          setTimeout(function () {
            window.location.reload();
          }, 1000);
        } else {
          alert("Upload failed. Please try again.");
          resetProgress();
        }
      };

      xhr.onerror = function () {
        alert("Upload failed. Please try again.");
        resetProgress();
      };

      xhr.send(formData);
    }
  });

  // Helper function to format file size
  function formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  // Helper function to read a file as ArrayBuffer
  function readFileAsync(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsArrayBuffer(file);
    });
  }

  // Batch file operations
  const selectAllCheckbox = document.getElementById("select-all-files");
  if (selectAllCheckbox) {
    selectAllCheckbox.addEventListener("change", function () {
      const fileCheckboxes = document.querySelectorAll(".file-checkbox");
      fileCheckboxes.forEach((checkbox) => {
        checkbox.checked = this.checked;
      });

      updateBatchOperationsVisibility();
    });

    // Individual checkboxes
    const fileCheckboxes = document.querySelectorAll(".file-checkbox");
    fileCheckboxes.forEach((checkbox) => {
      checkbox.addEventListener("change", updateBatchOperationsVisibility);
    });

    // Update batch operations visibility
    function updateBatchOperationsVisibility() {
      const batchOperations = document.getElementById("batch-operations");
      if (!batchOperations) return;

      const checkedBoxes = document.querySelectorAll(".file-checkbox:checked");

      if (checkedBoxes.length > 0) {
        batchOperations.classList.remove("d-none");

        // Update selected count
        const selectedCount = batchOperations.querySelector(".selected-count");
        if (selectedCount) {
          selectedCount.textContent = checkedBoxes.length;
        }
      } else {
        batchOperations.classList.add("d-none");
      }
    }

    // Batch download button
    const batchDownloadBtn = document.getElementById("batch-download");
    if (batchDownloadBtn) {
      batchDownloadBtn.addEventListener("click", function () {
        const checkedBoxes = document.querySelectorAll(
          ".file-checkbox:checked"
        );
        const fileIds = Array.from(checkedBoxes).map(
          (checkbox) => checkbox.value
        );

        if (fileIds.length > 0) {
          // Create a form to submit the file IDs
          const form = document.createElement("form");
          form.method = "POST";
          form.action = "/files/batch-download/";
          form.style.display = "none";

          // Add CSRF token
          const csrfToken = document.querySelector(
            "[name=csrfmiddlewaretoken]"
          ).value;
          const csrfInput = document.createElement("input");
          csrfInput.type = "hidden";
          csrfInput.name = "csrfmiddlewaretoken";
          csrfInput.value = csrfToken;
          form.appendChild(csrfInput);

          // Add file IDs
          fileIds.forEach((fileId) => {
            const input = document.createElement("input");
            input.type = "hidden";
            input.name = "file_ids";
            input.value = fileId;
            form.appendChild(input);
          });

          // Submit the form
          document.body.appendChild(form);
          form.submit();
        }
      });
    }

    // Batch delete button
    const batchDeleteBtn = document.getElementById("batch-delete");
    if (batchDeleteBtn) {
      batchDeleteBtn.addEventListener("click", function () {
        const checkedBoxes = document.querySelectorAll(
          ".file-checkbox:checked"
        );
        const fileIds = Array.from(checkedBoxes).map(
          (checkbox) => checkbox.value
        );

        if (fileIds.length > 0) {
          // Show confirmation modal
          const modal = new bootstrap.Modal(
            document.getElementById("batch-delete-modal")
          );

          // Update file count in modal
          const fileCountSpan = document.getElementById("batch-delete-count");
          if (fileCountSpan) {
            fileCountSpan.textContent = fileIds.length;
          }

          // Set file IDs in the confirm button data attribute
          const confirmBtn = document.getElementById("confirm-batch-delete");
          if (confirmBtn) {
            confirmBtn.setAttribute("data-file-ids", JSON.stringify(fileIds));

            // Add click handler for confirm button
            confirmBtn.addEventListener(
              "click",
              function () {
                const fileIds = JSON.parse(this.getAttribute("data-file-ids"));

                // Create a form to submit the file IDs
                const form = document.createElement("form");
                form.method = "POST";
                form.action = "/files/batch-delete/";
                form.style.display = "none";

                // Add CSRF token
                const csrfToken = document.querySelector(
                  "[name=csrfmiddlewaretoken]"
                ).value;
                const csrfInput = document.createElement("input");
                csrfInput.type = "hidden";
                csrfInput.name = "csrfmiddlewaretoken";
                csrfInput.value = csrfToken;
                form.appendChild(csrfInput);

                // Add file IDs
                fileIds.forEach((fileId) => {
                  const input = document.createElement("input");
                  input.type = "hidden";
                  input.name = "file_ids";
                  input.value = fileId;
                  form.appendChild(input);
                });

                // Submit the form
                document.body.appendChild(form);
                form.submit();
              },
              { once: true }
            ); // Use once to prevent multiple event handlers
          }

          modal.show();
        }
      });
    }
  }
});
