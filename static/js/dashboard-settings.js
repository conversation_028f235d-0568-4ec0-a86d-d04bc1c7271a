// Dashboard settings and customization
document.addEventListener('DOMContentLoaded', function() {
    // Apply theme based on user settings or system preference
    applyTheme();
    
    // Apply color scheme
    applyColorScheme();
    
    // Apply layout
    applyLayout();
    
    // Listen for system theme changes
    if (window.matchMedia) {
        const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        darkModeMediaQuery.addEventListener('change', applyTheme);
    }
    
    // Function to apply theme
    function applyTheme() {
        const body = document.body;
        const storedTheme = localStorage.getItem('theme');
        
        // Remove existing theme classes
        body.classList.remove('theme-light', 'theme-dark');
        
        if (storedTheme === 'light') {
            // Light theme
            body.classList.add('theme-light');
        } else if (storedTheme === 'dark') {
            // Dark theme
            body.classList.add('theme-dark');
        } else {
            // Auto (system preference)
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                body.classList.add('theme-dark');
            } else {
                body.classList.add('theme-light');
            }
        }
    }
    
    // Function to apply color scheme
    function applyColorScheme() {
        const body = document.body;
        const storedColorScheme = localStorage.getItem('colorScheme');
        
        // Remove existing color scheme classes
        body.classList.remove('color-blue', 'color-green', 'color-purple', 'color-orange', 'color-red');
        
        // Apply stored color scheme or default to blue
        if (storedColorScheme) {
            body.classList.add(`color-${storedColorScheme}`);
        } else {
            body.classList.add('color-blue');
        }
    }
    
    // Function to apply layout
    function applyLayout() {
        const body = document.body;
        const storedLayout = localStorage.getItem('layout');
        
        // Remove existing layout classes
        body.classList.remove('layout-default', 'layout-compact', 'layout-expanded');
        
        // Apply stored layout or default
        if (storedLayout) {
            body.classList.add(`layout-${storedLayout}`);
        } else {
            body.classList.add('layout-default');
        }
    }
});
