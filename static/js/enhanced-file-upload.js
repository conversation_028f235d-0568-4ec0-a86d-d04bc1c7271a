/**
 * Enhanced File Upload with <PERSON><PERSON> and Drop
 *
 * Features:
 * - Drag and drop file upload
 * - Multiple file selection
 * - File preview
 * - Progress tracking
 * - Client-side zipping
 * - Validation
 */

document.addEventListener("DOMContentLoaded", function () {
  // Elements
  const fileUploadAreas = document.querySelectorAll(".file-upload-area");
  const fileListContainer = document.getElementById("file-list-container");
  const fileList = document.getElementById("file-list");
  const uploadForm = document.querySelector("form.file-upload-form");
  const fileNameInput = document.getElementById("id_file_name");
  const notesInput = document.getElementById("id_notes");
  const uploadButton = document.getElementById("upload-button");
  const uploadProgress = document.getElementById("upload-progress");
  const progressBar = uploadProgress
    ? uploadProgress.querySelector(".progress-bar")
    : null;
  const maxFileSize = 100 * 1024 * 1024; // 100MB

  // File collection
  let selectedFiles = [];

  // Initialize
  if (fileUploadAreas.length === 0) return;

  fileUploadAreas.forEach((uploadArea) => {
    const fileInput = uploadArea.querySelector('input[type="file"]');
    const projectId = uploadArea.dataset.projectId;

    if (!fileInput) return;

    // Add a hidden input to indicate this is a file upload that should be zipped
    let isZipFileInput = uploadForm.querySelector('input[name="is_zipped"]');
    if (!isZipFileInput) {
      isZipFileInput = document.createElement("input");
      isZipFileInput.type = "hidden";
      isZipFileInput.name = "is_zipped";
      isZipFileInput.value = "true";
      uploadForm.appendChild(isZipFileInput);
    }

    // Handle drag and drop events
    ["dragenter", "dragover", "dragleave", "drop"].forEach((eventName) => {
      uploadArea.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
      e.preventDefault();
      e.stopPropagation();
    }

    // Add visual feedback for drag events
    ["dragenter", "dragover"].forEach((eventName) => {
      uploadArea.addEventListener(eventName, highlight, false);
    });

    ["dragleave", "drop"].forEach((eventName) => {
      uploadArea.addEventListener(eventName, unhighlight, false);
    });

    function highlight() {
      uploadArea.classList.add("drag-highlight");
    }

    function unhighlight() {
      uploadArea.classList.remove("drag-highlight");
    }

    // Handle dropped files
    uploadArea.addEventListener("drop", handleDrop, false);

    function handleDrop(e) {
      const dt = e.dataTransfer;
      const files = dt.files;

      handleFiles(files);
    }

    // Handle file input change
    fileInput.addEventListener("change", function () {
      handleFiles(this.files);
    });

    // Handle files
    function handleFiles(files) {
      if (!files || files.length === 0) return;

      // Check total size
      let totalSize = 0;
      for (let i = 0; i < files.length; i++) {
        totalSize += files[i].size;
      }

      if (totalSize > maxFileSize) {
        showError("Total file size must be under 100MB");
        return;
      }

      // Add files to collection
      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        // Check if file already exists in the collection
        const fileExists = selectedFiles.some(
          (f) =>
            f.name === file.name &&
            f.size === file.size &&
            f.lastModified === file.lastModified
        );

        if (!fileExists) {
          selectedFiles.push(file);
        }
      }

      // Update file list
      updateFileList();

      // Update file name if not set
      if (fileNameInput && !fileNameInput.value.trim()) {
        if (selectedFiles.length === 1) {
          fileNameInput.value = selectedFiles[0].name.replace(/\.[^/.]+$/, "");
        } else {
          fileNameInput.value = "Multiple Files";
        }
      }

      // Update upload button
      updateUploadButton();
    }

    // Update file list
    function updateFileList() {
      if (!fileList) return;

      // Clear file list
      fileList.innerHTML = "";

      // Show file list container
      if (fileListContainer) {
        fileListContainer.style.display =
          selectedFiles.length > 0 ? "block" : "none";
      }

      // Add files to list
      selectedFiles.forEach((file, index) => {
        const fileItem = document.createElement("div");
        fileItem.className =
          "file-item d-flex align-items-center p-2 border-bottom";

        // File icon based on type
        let fileIcon = "fa-file";
        if (file.type.includes("image")) {
          fileIcon = "fa-file-image text-info";
        } else if (file.type.includes("pdf")) {
          fileIcon = "fa-file-pdf text-danger";
        } else if (
          file.type.includes("word") ||
          file.name.endsWith(".doc") ||
          file.name.endsWith(".docx")
        ) {
          fileIcon = "fa-file-word text-primary";
        } else if (
          file.type.includes("excel") ||
          file.name.endsWith(".xls") ||
          file.name.endsWith(".xlsx")
        ) {
          fileIcon = "fa-file-excel text-success";
        } else if (
          file.type.includes("zip") ||
          file.name.endsWith(".zip") ||
          file.name.endsWith(".rar")
        ) {
          fileIcon = "fa-file-archive text-warning";
        } else if (file.type.includes("video")) {
          fileIcon = "fa-file-video text-danger";
        } else if (file.type.includes("audio")) {
          fileIcon = "fa-file-audio text-warning";
        } else if (file.type.includes("text")) {
          fileIcon = "fa-file-alt text-secondary";
        }

        // Format file size
        const fileSize = formatFileSize(file.size);

        fileItem.innerHTML = `
                    <div class="me-2"><i class="fas ${fileIcon}"></i></div>
                    <div class="flex-grow-1">
                        <div class="fw-bold">${file.name}</div>
                        <div class="small text-muted">${fileSize}</div>
                    </div>
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-danger remove-file" data-index="${index}">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;

        fileList.appendChild(fileItem);
      });

      // Add event listeners to remove buttons
      document.querySelectorAll(".remove-file").forEach((button) => {
        button.addEventListener("click", function () {
          const index = parseInt(this.dataset.index);
          selectedFiles.splice(index, 1);
          updateFileList();
          updateUploadButton();
        });
      });
    }

    // Update upload button
    function updateUploadButton() {
      if (!uploadButton) return;

      if (selectedFiles.length > 0) {
        uploadButton.disabled = false;
        uploadButton.innerHTML = `<i class="fas fa-upload me-2"></i>Upload ${
          selectedFiles.length
        } ${selectedFiles.length === 1 ? "File" : "Files"}`;
      } else {
        uploadButton.disabled = true;
        uploadButton.innerHTML =
          '<i class="fas fa-upload me-2"></i>Upload Files';
      }
    }

    // Format file size
    function formatFileSize(bytes) {
      if (bytes === 0) return "0 Bytes";

      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));

      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    }

    // Show error
    function showError(message) {
      const errorContainer = document.getElementById("file-upload-error");
      if (errorContainer) {
        errorContainer.textContent = message;
        errorContainer.style.display = "block";

        // Hide after 5 seconds
        setTimeout(() => {
          errorContainer.style.display = "none";
        }, 5000);
      } else {
        alert(message);
      }
    }

    // Reset progress
    function resetProgress() {
      if (uploadProgress && progressBar) {
        uploadProgress.style.display = "none";
        progressBar.style.width = "0%";
        progressBar.setAttribute("aria-valuenow", 0);
        progressBar.textContent = "0%";
      }
    }

    // Show progress
    function showProgress(percent) {
      if (uploadProgress && progressBar) {
        uploadProgress.style.display = "block";
        progressBar.style.width = percent + "%";
        progressBar.setAttribute("aria-valuenow", percent);
        progressBar.textContent = percent + "%";
      }
    }

    // Flag to prevent duplicate submissions
    let isUploading = false;

    // Handle form submission
    if (uploadForm) {
      // Handle click on the upload button instead of form submission
      uploadButton.addEventListener("click", function (e) {
        e.preventDefault();

        // Prevent duplicate submissions
        if (isUploading) {
          console.log(
            "Upload already in progress, preventing duplicate submission"
          );
          return;
        }

        if (selectedFiles.length === 0) {
          showError("Please select at least one file to upload");
          return;
        }

        // Set uploading flag to prevent duplicate submissions
        isUploading = true;
        uploadButton.disabled = true;

        // Show progress indicator
        resetProgress();
        showProgress(5);

        // Create a new FormData object instead of using the form directly
        // This prevents the file input from being submitted twice
        const formData = new FormData();

        // Add all form fields except the file input
        const formElements = uploadForm.elements;
        for (let i = 0; i < formElements.length; i++) {
          const element = formElements[i];
          if (element.name && element.name !== "file") {
            formData.append(element.name, element.value);
          }
        }

        // Add CSRF token
        const csrfToken = document.querySelector(
          'input[name="csrfmiddlewaretoken"]'
        ).value;
        formData.append("csrfmiddlewaretoken", csrfToken);

        // Add a unique upload ID to prevent duplicate uploads
        const uploadId =
          Date.now() + "-" + Math.random().toString(36).substr(2, 9);
        formData.append("upload_id", uploadId);

        // Set the file name if not already set
        if (!formData.has("file_name") || formData.get("file_name") === "") {
          if (selectedFiles.length === 1) {
            formData.set(
              "file_name",
              selectedFiles[0].name.replace(/\.[^/.]+$/, "")
            );
          } else {
            formData.set("file_name", "Multiple Files");
          }
        }

        // Always create a zip file for all uploads
        // Set the is_zipped flag to true
        formData.set("is_zipped", "true");

        // Load JSZip if not already loaded
        if (typeof JSZip === "undefined") {
          const script = document.createElement("script");
          script.src =
            "https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js";
          script.onload = () => createAndSendZip();
          document.head.appendChild(script);
        } else {
          createAndSendZip();
        }

        // Create and send zip file
        async function createAndSendZip() {
          try {
            showProgress(10);

            // Create a new zip file
            const zip = new JSZip();

            // Add files to zip
            for (let i = 0; i < selectedFiles.length; i++) {
              const file = selectedFiles[i];
              const fileData = await readFileAsync(file);
              zip.file(file.name, fileData);

              // Update progress
              showProgress(10 + Math.round((i / selectedFiles.length) * 40));
            }

            // Generate zip file
            showProgress(50);
            const zipBlob = await zip.generateAsync({
              type: "blob",
              compression: "DEFLATE",
            });

            // Create a File object from the Blob
            const zipFileName = formData.get("file_name") || "files";
            const zipFile = new File([zipBlob], zipFileName + ".zip", {
              type: "application/zip",
            });

            // Add the zip file to the form data
            formData.append("file", zipFile);
            formData.set("is_zipped", "true");

            // Send the form data
            showProgress(60);
            sendFormData(formData);
          } catch (error) {
            console.error("Error creating zip file:", error);
            showError("Error creating zip file. Please try again.");
            resetProgress();
          }
        }

        // Helper function to read a file as ArrayBuffer
        function readFileAsync(file) {
          return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsArrayBuffer(file);
          });
        }

        // Send form data
        function sendFormData(formData) {
          // Create AJAX request
          const xhr = new XMLHttpRequest();
          xhr.open("POST", uploadForm.action, true);
          xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");

          // Track upload progress
          xhr.upload.onprogress = function (e) {
            if (e.lengthComputable) {
              const percentComplete =
                60 + Math.round((e.loaded / e.total) * 30);
              showProgress(percentComplete);
            }
          };

          // Handle response
          xhr.onload = function () {
            if (xhr.status === 200) {
              try {
                const response = JSON.parse(xhr.responseText);

                if (response.status === "success") {
                  showProgress(100);

                  // Show success message
                  const successContainer = document.getElementById(
                    "file-upload-success"
                  );
                  if (successContainer) {
                    successContainer.textContent = response.message;
                    successContainer.style.display = "block";
                  }

                  // Reset uploading flag
                  isUploading = false;
                  uploadButton.disabled = false;

                  // Redirect after a short delay
                  setTimeout(() => {
                    if (response.redirect) {
                      window.location.href = response.redirect;
                    } else {
                      window.location.reload();
                    }
                  }, 1500);
                } else {
                  showError(
                    response.message || "Upload failed. Please try again."
                  );
                  resetProgress();
                  // Reset uploading flag on error
                  isUploading = false;
                  uploadButton.disabled = false;
                }
              } catch (e) {
                // If the response is not JSON, assume it's a redirect
                showProgress(100);
                setTimeout(() => {
                  window.location.reload();
                }, 1000);
              }
            } else {
              try {
                const response = JSON.parse(xhr.responseText);
                showError(
                  response.message || "Upload failed. Please try again."
                );
              } catch (e) {
                showError("Upload failed. Please try again.");
              }
              resetProgress();
              // Reset uploading flag on error
              isUploading = false;
              uploadButton.disabled = false;
            }
          };

          xhr.onerror = function () {
            showError("Network error. Please try again.");
            resetProgress();
            // Reset uploading flag on network error
            isUploading = false;
            uploadButton.disabled = false;
          };

          xhr.send(formData);
        }
      });
    }
  });

  // Initialize file list
  updateFileList();

  // Update file list
  function updateFileList() {
    if (!fileList) return;

    // Clear file list
    fileList.innerHTML = "";

    // Show file list container
    if (fileListContainer) {
      fileListContainer.style.display =
        selectedFiles.length > 0 ? "block" : "none";
    }

    // Add files to list
    selectedFiles.forEach((file, index) => {
      const fileItem = document.createElement("div");
      fileItem.className =
        "file-item d-flex align-items-center p-2 border-bottom";

      // File icon based on type
      let fileIcon = "fa-file";
      if (file.type.includes("image")) {
        fileIcon = "fa-file-image text-info";
      } else if (file.type.includes("pdf")) {
        fileIcon = "fa-file-pdf text-danger";
      } else if (
        file.type.includes("word") ||
        file.name.endsWith(".doc") ||
        file.name.endsWith(".docx")
      ) {
        fileIcon = "fa-file-word text-primary";
      } else if (
        file.type.includes("excel") ||
        file.name.endsWith(".xls") ||
        file.name.endsWith(".xlsx")
      ) {
        fileIcon = "fa-file-excel text-success";
      } else if (
        file.type.includes("zip") ||
        file.name.endsWith(".zip") ||
        file.name.endsWith(".rar")
      ) {
        fileIcon = "fa-file-archive text-warning";
      } else if (file.type.includes("video")) {
        fileIcon = "fa-file-video text-danger";
      } else if (file.type.includes("audio")) {
        fileIcon = "fa-file-audio text-warning";
      } else if (file.type.includes("text")) {
        fileIcon = "fa-file-alt text-secondary";
      }

      // Format file size
      const fileSize = formatFileSize(file.size);

      fileItem.innerHTML = `
                <div class="me-2"><i class="fas ${fileIcon}"></i></div>
                <div class="flex-grow-1">
                    <div class="fw-bold">${file.name}</div>
                    <div class="small text-muted">${fileSize}</div>
                </div>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-file" data-index="${index}">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

      fileList.appendChild(fileItem);
    });

    // Add event listeners to remove buttons
    document.querySelectorAll(".remove-file").forEach((button) => {
      button.addEventListener("click", function () {
        const index = parseInt(this.dataset.index);
        selectedFiles.splice(index, 1);
        updateFileList();
        updateUploadButton();
      });
    });
  }

  // Update upload button
  function updateUploadButton() {
    if (!uploadButton) return;

    if (selectedFiles.length > 0) {
      uploadButton.disabled = false;
      uploadButton.innerHTML = `<i class="fas fa-upload me-2"></i>Upload ${
        selectedFiles.length
      } ${selectedFiles.length === 1 ? "File" : "Files"}`;
    } else {
      uploadButton.disabled = true;
      uploadButton.innerHTML = '<i class="fas fa-upload me-2"></i>Upload Files';
    }
  }

  // Format file size
  function formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }
});
