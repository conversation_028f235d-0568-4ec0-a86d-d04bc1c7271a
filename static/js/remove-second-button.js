// <PERSON>ript to remove only the second Upload Folder button
document.addEventListener("DOMContentLoaded", function() {
  // Function to remove the second Upload Folder button
  function removeSecondUploadFolderButton() {
    // Find all buttons with text containing "Upload Folder"
    const uploadFolderButtons = Array.from(document.querySelectorAll('button')).filter(btn => 
      btn.textContent.includes('Upload Folder') || 
      btn.innerHTML.includes('Upload Folder')
    );
    
    console.log("Found " + uploadFolderButtons.length + " upload folder buttons");
    
    // If we have more than one button, keep only the first one
    if (uploadFolderButtons.length > 1) {
      // Keep the first button (index 0) and remove the second one (index 1)
      const secondButton = uploadFolderButtons[1];
      console.log("Removing second button:", secondButton);
      if (secondButton && secondButton.parentNode) {
        secondButton.parentNode.removeChild(secondButton);
      }
    }
  }
  
  // Run immediately
  removeSecondUploadFolderButton();
  
  // Also run after a short delay to catch any buttons added by other scripts
  setTimeout(removeSecondUploadFolderButton, 500);
  
  // And run again after the page is fully loaded
  window.addEventListener('load', function() {
    removeSecondUploadFolderButton();
  });
});
