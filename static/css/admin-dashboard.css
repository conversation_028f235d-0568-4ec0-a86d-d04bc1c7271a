/* CompletoPLUS Admin Dashboard Styles */

:root {
  --primary-color: #4361ee;
  --accent-color: #3a0ca3;
  --success-color: #06d6a0;
  --warning-color: #ffd166;
  --danger-color: #ef476f;
  --info-color: #118ab2;
  --dark-color: #1a1a2e;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  --transition-speed: 0.3s;
  --border-radius: 10px;
  --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  /* Light mode specific variables */
  --body-bg: #f8f9fa;
  --body-text: #212529;
  --card-bg: #ffffff;
  --card-border: var(--gray-200);
  --stat-label: var(--gray-700);
  --section-title: var(--dark-color);
  --chart-label: var(--gray-700);
}

.admin-dashboard {
  padding: 20px 0;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

/* Stats Overview */
.stats-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: var(--border-radius);
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: var(--box-shadow);
  transition: transform var(--transition-speed) ease,
    box-shadow var(--transition-speed) ease;
  border: 1px solid var(--gray-200);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--accent-color)
  );
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: 15px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--dark-color);
  line-height: 1.2;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--gray-600);
  font-weight: 500;
}

/* Dashboard Sections */
.dashboard-section {
  background: white;
  border-radius: var(--border-radius);
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: var(--box-shadow);
  border: 1px solid var(--gray-200);
}

.section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
}

.section-title i {
  margin-right: 10px;
  color: var(--primary-color);
}

/* Status Cards */
.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.status-card {
  padding: 15px;
  border-radius: 8px;
  display: flex;
  align-items: center;
}

.status-card.pending {
  background-color: rgba(255, 209, 102, 0.2);
}

.status-card.in-progress {
  background-color: rgba(67, 97, 238, 0.2);
}

.status-card.completed {
  background-color: rgba(6, 214, 160, 0.2);
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  margin-right: 12px;
}

.status-card.pending .status-icon {
  background-color: var(--warning-color);
  color: var(--dark-color);
}

.status-card.in-progress .status-icon {
  background-color: var(--primary-color);
  color: white;
}

.status-card.completed .status-icon {
  background-color: var(--success-color);
  color: white;
}

.status-value {
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1.2;
}

.status-card.pending .status-value {
  color: #e6b800;
}

.status-card.in-progress .status-value {
  color: var(--primary-color);
}

.status-card.completed .status-value {
  color: var(--success-color);
}

.status-label {
  font-size: 0.8rem;
  font-weight: 500;
}

/* Activity Chart */
.activity-chart {
  padding: 15px 0;
}

.chart-header {
  margin-bottom: 20px;
}

.chart-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--gray-700);
}

.chart-body {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  height: 200px;
  margin-bottom: 15px;
}

.chart-bar {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.chart-label {
  font-size: 0.8rem;
  color: var(--gray-600);
  margin-top: 10px;
}

.chart-column {
  width: 40px;
  height: 100%;
  display: flex;
  flex-direction: column-reverse;
  justify-content: flex-start;
  align-items: center;
}

.chart-value {
  width: 100%;
  border-radius: 4px 4px 0 0;
  transition: height 0.5s ease;
}

.chart-value.uploads {
  background-color: var(--primary-color);
  margin-bottom: 2px;
}

.chart-value.downloads {
  background-color: var(--info-color);
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 10px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 15px;
  height: 15px;
  border-radius: 3px;
}

.legend-color.uploads {
  background-color: var(--primary-color);
}

.legend-color.downloads {
  background-color: var(--info-color);
}

.legend-label {
  font-size: 0.8rem;
  color: var(--gray-700);
}

/* App List Styling */
.app-list {
  margin-top: 15px;
}

.app-item {
  margin-bottom: 25px;
  background: white;
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
  border: 1px solid var(--gray-200);
}

.app-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
}

.app-name i {
  margin-right: 10px;
  color: var(--primary-color);
}

.model-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.model-item {
  background-color: var(--gray-100);
  border-radius: 8px;
  padding: 15px;
  transition: all var(--transition-speed) ease;
  border: 1px solid transparent;
}

.model-item:hover {
  background-color: white;
  transform: translateY(-3px);
  border-color: var(--primary-color);
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.1);
}

.model-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--primary-color);
  font-weight: 500;
  gap: 10px;
}

.model-link i {
  font-size: 0.9rem;
}

/* Recent Actions */
.actionlist {
  list-style: none;
  padding: 0;
  margin: 0;
}

.actionlist-item {
  padding: 10px 0;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  flex-direction: column;
}

.action-icon {
  margin-right: 8px;
  color: var(--primary-color);
}

.actionlist-item.addition .action-icon {
  color: var(--success-color);
}

.actionlist-item.change .action-icon {
  color: var(--warning-color);
}

.actionlist-item.deletion .action-icon {
  color: var(--danger-color);
}

.mini {
  font-size: 0.8rem;
  color: var(--gray-600);
}

.action-time {
  font-size: 0.75rem;
  color: var(--gray-500);
  margin-top: 5px;
}

/* Quick Links */
.quick-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.quick-links li {
  margin-bottom: 10px;
}

.quick-links a {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 6px;
  background-color: var(--gray-100);
  color: var(--gray-800);
  text-decoration: none;
  transition: all 0.3s ease;
}

.quick-links a:hover {
  background-color: var(--gray-200);
  transform: translateY(-2px);
}

.quick-links i {
  color: var(--primary-color);
}

/* Dark Mode Support */
[data-theme="dark"] {
  --body-bg: #121212;
  --body-text: #e0e0e0;
  --card-bg: #1e1e1e;
  --card-border: #333;
  --stat-label: #adb5bd;
  --section-title: #e0e0e0;
  --chart-label: #aaa;
}

[data-theme="dark"] .stat-card,
[data-theme="dark"] .dashboard-section {
  background-color: var(--card-bg);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  border-color: var(--card-border);
}

[data-theme="dark"] .stat-value {
  color: var(--body-text);
}

[data-theme="dark"] .stat-label {
  color: var(--stat-label);
}

[data-theme="dark"] .section-title {
  color: var(--section-title);
  border-bottom-color: var(--card-border);
}

[data-theme="dark"] .status-card.pending {
  background-color: rgba(255, 209, 102, 0.1);
}

[data-theme="dark"] .status-card.in-progress {
  background-color: rgba(67, 97, 238, 0.1);
}

[data-theme="dark"] .status-card.completed {
  background-color: rgba(6, 214, 160, 0.1);
}

[data-theme="dark"] .chart-title {
  color: #d0d0d0;
}

[data-theme="dark"] .chart-label {
  color: var(--chart-label);
}

[data-theme="dark"] .legend-label {
  color: #d0d0d0;
}

[data-theme="dark"] .app-name {
  color: var(--body-text);
}

[data-theme="dark"] .app-item {
  background-color: var(--card-bg);
  border-color: var(--card-border);
}

[data-theme="dark"] .model-item {
  background-color: #2a2a2a;
}

[data-theme="dark"] .model-item:hover {
  background-color: #333;
  border-color: var(--primary-color);
}

[data-theme="dark"] .model-link {
  color: #6d8fff;
}

[data-theme="dark"] .actionlist-item {
  border-bottom-color: var(--card-border);
}

[data-theme="dark"] .mini {
  color: #aaa;
}

[data-theme="dark"] .action-time {
  color: #888;
}

[data-theme="dark"] .quick-links a {
  background-color: #2a2a2a;
  color: var(--body-text);
}

[data-theme="dark"] .quick-links a:hover {
  background-color: #333;
}

/* System preference based dark mode as fallback */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]) {
    --body-bg: #121212;
    --body-text: #e0e0e0;
    --card-bg: #1e1e1e;
    --card-border: #333;
    --stat-label: #adb5bd;
    --section-title: #e0e0e0;
    --chart-label: #aaa;
  }
}

/* Responsive Adjustments */
@media (max-width: 767px) {
  .stats-overview,
  .status-cards {
    grid-template-columns: 1fr;
  }

  .chart-body {
    height: 150px;
  }

  .chart-column {
    width: 20px;
  }

  .chart-label {
    font-size: 0.7rem;
    transform: rotate(-45deg);
  }

  .model-list {
    grid-template-columns: 1fr;
  }
}
