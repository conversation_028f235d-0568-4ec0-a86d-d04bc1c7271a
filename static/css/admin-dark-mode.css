/* Custom Dark Mode CSS for Admin Dashboard */

/* Dark mode variables */
html[data-theme="dark"] {
  /* Base colors */
  --primary-color: #3498db; /* Bright blue for better visibility */
  --secondary-color: #2c3e50; /* Dark blue for contrast */
  --success-color: #2ecc71; /* Bright green for better visibility */
  --info-color: #3498db; /* Bright blue for better visibility */
  --warning-color: #f39c12; /* Orange for better visibility */
  --danger-color: #e74c3c; /* Red for better visibility */

  /* Chart.js text colors */
  --chart-text-color: #ffffff;
  --chart-grid-color: rgba(255, 255, 255, 0.1);
  --chart-title-color: #ffffff;
  --chart-legend-color: #ffffff;

  /* Module colors */
  --module-bg: #2c2c2c;
  --module-header-bg: #3498db; /* Bright blue for better visibility */
  --module-header-text: #ffffff;
  --module-border: #444444;

  /* Card colors */
  --card-bg: #2c2c2c;
  --card-border: #444444;
  --card-text: #ffffff;
  --card-hover-shadow: rgba(0, 0, 0, 0.5);

  /* Text colors */
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-muted: #999999;

  /* Header colors */
  --header-bg: #2c2c2c;
  --header-text: #ffffff;
  --header-link: #3498db; /* Bright blue for better visibility */
  --header-link-hover: #ffffff;

  /* Sidebar colors */
  --sidebar-bg: #2c2c2c;
  --sidebar-text: #ffffff;
  --sidebar-link: #3498db; /* Bright blue for better visibility */
  --sidebar-link-hover: #ffffff;
  --sidebar-section-title: #3498db; /* Bright blue for better visibility */
}

/* Dark mode header styles */
html[data-theme="dark"] #header {
  background-color: var(--header-bg);
  color: var(--header-text);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.4);
}

html[data-theme="dark"] #branding h1 a {
  color: var(--header-text) !important;
}

html[data-theme="dark"] #header a:link,
html[data-theme="dark"] #header a:visited {
  color: var(--header-link) !important;
}

html[data-theme="dark"] #header a:hover {
  color: var(--header-link-hover) !important;
}

html[data-theme="dark"] div.breadcrumbs {
  background-color: var(--module-header-bg);
  color: var(--header-text) !important;
  border-bottom: 1px solid var(--module-border);
}

html[data-theme="dark"] div.breadcrumbs a {
  color: var(--header-link) !important;
}

html[data-theme="dark"] div.breadcrumbs a:hover {
  color: var(--header-link-hover) !important;
}

/* Dark mode sidebar styles */
html[data-theme="dark"] #nav-sidebar {
  background-color: var(--sidebar-bg);
  border-right: 1px solid var(--module-border);
}

html[data-theme="dark"] #nav-sidebar .module h2,
html[data-theme="dark"] #nav-sidebar .module caption {
  background: var(--module-header-bg);
  color: var(--module-header-text) !important;
  border-bottom: 1px solid var(--module-border);
}

html[data-theme="dark"] #nav-sidebar .section:link,
html[data-theme="dark"] #nav-sidebar .section:visited {
  color: var(--sidebar-section-title) !important;
  font-weight: 700;
}

html[data-theme="dark"] #nav-sidebar .section:hover {
  color: var(--sidebar-link-hover) !important;
}

html[data-theme="dark"] #nav-sidebar a:link,
html[data-theme="dark"] #nav-sidebar a:visited {
  color: var(--sidebar-link) !important;
}

html[data-theme="dark"] #nav-sidebar a:hover {
  color: var(--sidebar-link-hover) !important;
  text-decoration: none;
}

html[data-theme="dark"] #nav-sidebar .current-app .section:link,
html[data-theme="dark"] #nav-sidebar .current-app .section:visited {
  color: var(--sidebar-section-title) !important;
  font-weight: 700;
}

html[data-theme="dark"] #nav-sidebar .current-model {
  background: rgba(52, 152, 219, 0.2);
}

html[data-theme="dark"] #nav-sidebar td {
  color: var(--sidebar-text) !important;
}

/* Dark mode sidebar filter */
html[data-theme="dark"] #nav-filter {
  background-color: #3a3a3a;
  color: #ffffff;
  border: 1px solid #555555;
}

html[data-theme="dark"] #nav-filter:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.25);
}

/* Dark mode module styles */
html[data-theme="dark"] .module {
  background-color: var(--module-bg);
  border: 1px solid var(--module-border);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

html[data-theme="dark"] .module h2 {
  background-color: var(--module-header-bg);
  color: var(--module-header-text);
  border-bottom: 1px solid var(--module-border);
}

html[data-theme="dark"] .module .form-row {
  border-bottom: 1px solid #3a3a3a;
}

html[data-theme="dark"] .module label {
  color: #ffffff;
}

html[data-theme="dark"] .module p {
  color: #cccccc;
}

html[data-theme="dark"] .stat-card {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
  color: var(--card-text);
}

html[data-theme="dark"] .stat-card:hover {
  box-shadow: 0 4px 8px var(--card-hover-shadow);
}

html[data-theme="dark"] .stat-value {
  color: var(--text-primary);
}

html[data-theme="dark"] .stat-label {
  color: var(--text-secondary);
}

html[data-theme="dark"] .app-item {
  background-color: var(--card-bg);
  border: 1px solid var(--card-border);
}

html[data-theme="dark"] .app-item h3 {
  background-color: var(--module-header-bg);
  border-bottom: 1px solid var(--module-border);
}

html[data-theme="dark"] .app-item h3 a {
  color: var(--text-primary) !important;
}

html[data-theme="dark"] .model-list li a {
  color: var(--sidebar-link) !important;
}

html[data-theme="dark"] .model-list li a:hover {
  color: var(--sidebar-link-hover) !important;
}

html[data-theme="dark"] .actionlist {
  color: var(--text-primary);
}

html[data-theme="dark"] .actionlist a {
  color: var(--sidebar-link) !important;
}

html[data-theme="dark"] .actionlist a:hover {
  color: var(--sidebar-link-hover) !important;
}

html[data-theme="dark"] #recent-actions-module ul.actionlist li {
  border-bottom: 1px solid #3a3a3a;
}

html[data-theme="dark"] #recent-actions-module .mini {
  color: #aaaaaa;
}

html[data-theme="dark"] #recent-actions-module .addlink {
  color: #2ecc71 !important;
}

html[data-theme="dark"] #recent-actions-module .changelink {
  color: #f39c12 !important;
}

html[data-theme="dark"] #recent-actions-module .deletelink {
  color: #e74c3c !important;
}

/* Chart.js dark mode styles */
html[data-theme="dark"] .chart-container {
  background-color: var(--module-bg);
}

/* Override Chart.js text colors for dark mode */
html[data-theme="dark"] canvas {
  filter: brightness(0.8) contrast(1.2);
}

/* Ensure chart legends are visible in dark mode */
html[data-theme="dark"] .chartjs-render-monitor {
  color: var(--chart-text-color);
}

/* Improve contrast for chart labels */
html[data-theme="dark"] .chart-title,
html[data-theme="dark"] .chart-legend {
  color: var(--chart-text-color) !important;
}

/* Light mode improvements */
.stat-value {
  color: #333333;
}

.stat-label {
  color: #555555;
  font-weight: 600;
}

/* Chart.js global text color improvements */
.chart-title,
.chart-legend {
  color: #333333;
  font-weight: 600;
}

/* Ensure chart text is visible in both modes */
canvas {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
}
