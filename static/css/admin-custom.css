/* Custom Admin Styles */

/* Variables */
:root {
  /* Base colors */
  --primary-color: #3498db; /* Bright blue for better visibility */
  --secondary-color: #2c3e50; /* Dark blue for contrast */
  --success-color: #2ecc71; /* Bright green for better visibility */
  --info-color: #3498db; /* Bright blue for better visibility */
  --warning-color: #f39c12; /* Orange for better visibility */
  --danger-color: #e74c3c; /* Red for better visibility */
  --light-color: #f8f9fa;
  --dark-color: #212529;
  --gray-color: #6c757d;
  --border-color: #dee2e6;
  --text-color: #333333;

  /* Chart colors */
  --chart-text-color: #333333;
  --chart-grid-color: rgba(0, 0, 0, 0.1);
  --chart-title-color: #333333;
  --chart-legend-color: #333333;

  /* Header colors */
  --header-bg: #3498db; /* Bright blue for header background */
  --header-text: #ffffff; /* White text for header */
  --header-link: #ecf0f1; /* Light gray for header links */
  --header-link-hover: #ffffff; /* White for header link hover */

  /* Sidebar colors - Light Mode */
  --sidebar-bg: #f8f9fa; /* Light background for sidebar */
  --sidebar-text: #2c3e50; /* Dark blue text for sidebar */
  --sidebar-link: #3498db; /* Bright blue for sidebar links */
  --sidebar-link-hover: #2980b9; /* Darker blue for sidebar link hover */
  --sidebar-section-title: #3498db; /* Bright blue for sidebar section titles */

  /* Module colors */
  --module-bg: #ffffff;
  --module-header-bg: #3498db;
  --module-header-text: #ffffff;
  --module-border: #dee2e6;
}

/* Header */
#header {
  background: var(--header-bg);
  color: var(--header-text);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

#branding h1 {
  font-weight: 700;
}

#branding h1 a {
  display: flex;
  align-items: center;
  color: var(--header-text) !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Header links */
#header a:link,
#header a:visited {
  color: var(--header-link) !important;
}

#header a:hover {
  color: var(--header-link-hover) !important;
}

/* User tools */
#user-tools {
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.brand-logo {
  margin-right: 10px;
}

/* Module headers */
.module h2,
.module caption,
.inline-group h2 {
  background: var(--primary-color);
  color: #ffffff !important;
  font-weight: 700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  padding: 12px 15px;
  font-size: 16px;
  letter-spacing: 0.5px;
  border-radius: 4px 4px 0 0;
  margin: 0;
}

/* Module styling */
.module {
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
  overflow: hidden;
}

/* Module content */
.module .form-row {
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.module .form-row:last-child {
  border-bottom: none;
}

.module label {
  color: #333333;
  font-weight: 600;
}

.module p {
  color: #333333;
}

div.breadcrumbs {
  background: var(--secondary-color);
  padding: 12px 15px;
  color: #ffffff !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  font-weight: 500;
}

div.breadcrumbs a {
  color: var(--header-link) !important;
}

div.breadcrumbs a:hover {
  color: var(--header-link-hover) !important;
  text-decoration: underline;
}

/* Buttons */
.button,
input[type="submit"],
input[type="button"],
.submit-row input,
a.button {
  background: var(--primary-color);
  color: white;
  border-radius: 4px;
  transition: background 0.3s;
}

.button:hover,
input[type="submit"]:hover,
input[type="button"]:hover,
.submit-row input:hover,
a.button:hover {
  background: var(--secondary-color);
}

.button.default,
input[type="submit"].default,
.submit-row input.default {
  background: var(--success-color);
}

.button.default:hover,
input[type="submit"].default:hover,
.submit-row input.default:hover {
  background: var(--info-color);
}

/* Links */
a:link,
a:visited {
  color: var(--primary-color);
  transition: color 0.3s;
}

a:hover {
  color: var(--secondary-color);
}

/* Tables */
table thead th {
  background: var(--light-color);
  color: var(--dark-color);
  font-weight: 600;
}

.row1 {
  background: white;
}

.row2 {
  background: var(--light-color);
}

/* Admin shortcuts */
.admin-shortcuts {
  display: flex;
  gap: 15px;
  margin-top: 15px;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.shortcut-link {
  color: white !important;
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  border-radius: 4px;
  transition: background 0.3s;
}

.shortcut-link:hover {
  background: rgba(255, 255, 255, 0.2);
  text-decoration: none;
}

/* Dashboard */
.dashboard .module table th {
  width: 100%;
}

.dashboard .module table td {
  white-space: nowrap;
}

.dashboard .module h2 {
  background: var(--primary-color);
  color: #ffffff !important;
  font-weight: 700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  padding: 12px 15px;
  font-size: 16px;
  letter-spacing: 0.5px;
  border-radius: 4px 4px 0 0;
  margin: 0;
}

/* Form elements */
.selector-chosen h2 {
  background: var(--primary-color) !important;
}

.selector-available h2 {
  background: var(--gray-color) !important;
}

.calendar td.selected a,
.calendar td a:hover {
  background: var(--primary-color) !important;
}

.calendar td.nonday {
  background: var(--light-color);
}

/* Messages */
.messagelist li.success {
  background: var(--success-color);
}

.messagelist li.warning {
  background: var(--warning-color);
}

.messagelist li.error {
  background: var(--danger-color);
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .admin-shortcuts {
    flex-direction: column;
    gap: 5px;
  }

  #header {
    padding: 10px 20px;
  }

  #branding h1 {
    font-size: 18px;
  }

  .brand-logo {
    height: 24px;
  }
}

/* Jazzmin-specific styles */

/* Improve text visibility */
.card-body,
.card-header,
.card-title,
.card-text,
.table,
.table th,
.table td,
.form-control,
.form-group label,
.select2-container--default
  .select2-selection--single
  .select2-selection__rendered,
.select2-results__option,
.breadcrumb-item,
.breadcrumb-item a,
.nav-link,
.nav-item,
.user-panel,
.info,
.dropdown-menu,
.dropdown-item,
.page-link,
.paginator,
.help-block,
.help-text,
.form-text,
.text-muted,
.alert,
.alert-info,
.alert-success,
.alert-warning,
.alert-danger,
.list-group,
.list-group-item {
  color: var(--text-color) !important;
}

/* Dark sidebar with light text */
.sidebar-dark-primary .nav-sidebar > .nav-item > .nav-link.active,
.sidebar-dark-primary .nav-sidebar > .nav-item > .nav-link,
.sidebar-dark-primary .nav-treeview > .nav-item > .nav-link,
.sidebar-dark-primary .brand-link,
.sidebar-dark-primary .user-panel .info a {
  color: #fff !important;
}

/* Improve form field visibility */
.form-control {
  background-color: #fff !important;
  color: var(--text-color) !important;
  border: 1px solid var(--border-color) !important;
}

/* Improve select field visibility */
.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple {
  background-color: #fff !important;
  border: 1px solid var(--border-color) !important;
}

/* Improve table visibility */
.table {
  background-color: #fff !important;
}

.table thead th {
  background-color: var(--light-color) !important;
  color: var(--text-color) !important;
}

/* Improve card visibility */
.card {
  background-color: #fff !important;
  border: 1px solid var(--border-color) !important;
}

.card-header {
  background-color: var(--light-color) !important;
}

/* Improve button visibility */
.btn {
  color: #fff !important;
}

.btn-secondary {
  color: var(--text-color) !important;
}

/* Improve link visibility */
a {
  color: var(--primary-color) !important;
}

a:hover {
  color: var(--secondary-color) !important;
}

/* Sidebar styling */
#nav-sidebar {
  background-color: var(--sidebar-bg);
  border-right: 1px solid var(--border-color);
}

#nav-sidebar .module h2,
#nav-sidebar .module caption {
  background: var(--primary-color);
  color: #ffffff !important;
  font-weight: 700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

#nav-sidebar .section:link,
#nav-sidebar .section:visited {
  color: var(--sidebar-section-title) !important;
  font-weight: 700;
}

#nav-sidebar .section:hover {
  color: var(--sidebar-link-hover) !important;
}

#nav-sidebar a:link,
#nav-sidebar a:visited {
  color: var(--sidebar-link) !important;
  font-weight: 500;
}

#nav-sidebar a:hover {
  color: var(--sidebar-link-hover) !important;
  text-decoration: none;
}

#nav-sidebar .current-app .section:link,
#nav-sidebar .current-app .section:visited {
  color: var(--primary-color) !important;
  font-weight: 700;
}

#nav-sidebar .current-model {
  background: rgba(52, 152, 219, 0.1);
}

#nav-sidebar td {
  color: var(--sidebar-text) !important;
}

/* Style the sidebar filter */
#nav-filter {
  background-color: #ffffff;
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 6px 10px;
  margin: 10px 15px;
  width: calc(100% - 30px);
  box-sizing: border-box;
}

#nav-filter:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.25);
}

/* Style the app list */
.app-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 15px;
}

.app-item {
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.app-item h3 {
  margin: 0;
  padding: 12px 15px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-size: 14px;
}

.app-item h3 a {
  color: var(--primary-color) !important;
  font-weight: 600;
}

.model-list {
  list-style: none;
  padding: 15px;
  margin: 0;
}

.model-list li {
  margin-bottom: 8px;
  padding: 5px 0;
}

.model-list li:last-child {
  margin-bottom: 0;
}

/* Improve breadcrumb visibility */
.breadcrumb {
  background-color: var(--light-color) !important;
}

/* Improve pagination visibility */
.page-item.active .page-link {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: #fff !important;
}

/* Improve alert visibility */
.alert-info {
  color: #0c5460 !important;
  background-color: #d1ecf1 !important;
  border-color: #bee5eb !important;
}

.alert-success {
  color: #155724 !important;
  background-color: #d4edda !important;
  border-color: #c3e6cb !important;
}

.alert-warning {
  color: #856404 !important;
  background-color: #fff3cd !important;
  border-color: #ffeeba !important;
}

.alert-danger {
  color: #721c24 !important;
  background-color: #f8d7da !important;
  border-color: #f5c6cb !important;
}

/* Improve text visibility in admin dashboard */
.module h2 {
  color: #ffffff !important;
  font-weight: 700 !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
  background-color: var(--primary-color) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.module-title {
  font-size: 16px !important;
  letter-spacing: 0.5px !important;
  padding: 12px 15px !important;
}

.stat-card {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
}

.stat-value {
  font-size: 28px !important;
  font-weight: bold !important;
  color: #333333 !important;
}

.stat-label {
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #555555 !important;
}

/* Style the recent actions module */
#recent-actions-module .actionlist {
  padding: 15px;
}

#recent-actions-module ul.actionlist {
  list-style: none;
  margin: 0;
  padding: 0 15px 15px 15px;
}

#recent-actions-module ul.actionlist li {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

#recent-actions-module ul.actionlist li:last-child {
  border-bottom: none;
}

#recent-actions-module .mini {
  font-size: 12px;
  color: #666;
  font-style: italic;
}

#recent-actions-module .addlink {
  color: #28a745 !important;
}

#recent-actions-module .changelink {
  color: #ffc107 !important;
}

#recent-actions-module .deletelink {
  color: #dc3545 !important;
}

/* Ensure chart text is visible */
.chart-title,
.chart-legend {
  color: #333333 !important;
  font-weight: 600 !important;
}

/* Dark mode overrides */
html[data-theme="dark"] .stat-value {
  color: #ffffff !important;
}

html[data-theme="dark"] .stat-label {
  color: #cccccc !important;
}

html[data-theme="dark"] .chart-title,
html[data-theme="dark"] .chart-legend {
  color: #ffffff !important;
}

html[data-theme="dark"] .module {
  background-color: #1e1e1e !important;
  border: 1px solid #333333 !important;
}

html[data-theme="dark"] .module h2 {
  background-color: #2c2c2c !important;
  color: #ffffff !important;
  border-bottom: 1px solid #333333 !important;
}

html[data-theme="dark"] .stat-card {
  background-color: #2c2c2c !important;
  border: 1px solid #3a3a3a !important;
}

html[data-theme="dark"] .app-item {
  background-color: #2c2c2c !important;
  border: 1px solid #3a3a3a !important;
}

html[data-theme="dark"] .app-item h3 {
  background-color: #2c2c2c !important;
  border-bottom: 1px solid #333333 !important;
}

html[data-theme="dark"] .app-item h3 a {
  color: #ffffff !important;
}

html[data-theme="dark"] .model-list li a {
  color: #81d4fa !important;
}
