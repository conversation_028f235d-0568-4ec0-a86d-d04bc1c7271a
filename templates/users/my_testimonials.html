{% extends 'base/base.html' %}

{% block title %}My Testimonials - CompletoPLUS{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-md-10">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">My Testimonials</h1>
        <a href="{% url 'add_testimonial' %}" class="btn btn-primary">
          <i class="fas fa-plus me-2"></i> Add New Testimonial
        </a>
      </div>

      {% if testimonials %}
        <div class="row">
          {% for testimonial in testimonials %}
            <div class="col-md-6 mb-4">
              <div class="card h-100 border-0 shadow-sm {% if not testimonial.is_approved %}border-warning{% endif %}">
                <div class="card-body">
                  <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                      <div class="d-flex align-items-center mb-1">
                        <div class="text-warning me-2">
                          {% for i in "12345" %}
                            {% if forloop.counter <= testimonial.rating %}
                              <i class="fas fa-star"></i>
                            {% else %}
                              <i class="far fa-star"></i>
                            {% endif %}
                          {% endfor %}
                        </div>
                        <span class="small text-muted">{{ testimonial.rating }}.0</span>
                      </div>
                      <div class="small text-muted">
                        {{ testimonial.get_service_type_display }}
                      </div>
                    </div>
                    <div>
                      {% if testimonial.is_approved %}
                        <span class="badge bg-success">Published</span>
                      {% else %}
                        <span class="badge bg-warning text-dark">Pending Review</span>
                      {% endif %}
                      {% if testimonial.is_featured %}
                        <span class="badge bg-warning text-dark ms-1"><i class="fas fa-award me-1"></i>Featured</span>
                      {% endif %}
                    </div>
                  </div>

                  <p class="card-text fst-italic">{{ testimonial.content }}</p>

                  <div class="d-flex justify-content-between align-items-center mt-3">
                    <div class="d-flex align-items-center">
                      <div class="avatar-circle bg-primary text-white me-2">
                        {{ request.user.get_initials }}
                      </div>
                      <div>
                        <p class="mb-0 fw-medium">{{ request.user.get_full_name }}</p>
                      </div>
                    </div>
                    <small class="text-muted">{{ testimonial.created_at|date:"M d, Y" }}</small>
                  </div>
                </div>
              </div>
            </div>
          {% endfor %}
        </div>
      {% else %}
        <div class="card border-0 shadow-sm">
          <div class="card-body text-center py-5">
            <div class="mb-4">
              <i class="fas fa-comment-dots fa-4x text-muted"></i>
            </div>
            <h2 class="h4 mb-3">No Testimonials Yet</h2>
            <p class="text-muted mb-4">
              You haven't shared any testimonials about your experience with CompletoPLUS.
              Your feedback helps us improve and helps other clients make informed decisions.
            </p>
            <a href="{% url 'add_testimonial' %}" class="btn btn-primary">
              <i class="fas fa-plus me-2"></i> Add Your First Testimonial
            </a>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
