{% extends 'base/base.html' %}

{% block title %}New Message - CompletoPLUS{% endblock %}

{% block content %}
<div class="container py-4">
  <!-- Breadcrumb -->
  <nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">Dashboard</a></li>
      <li class="breadcrumb-item"><a href="{% url 'message_list' %}">Messages</a></li>
      <li class="breadcrumb-item active" aria-current="page">New Message</li>
    </ol>
  </nav>

  <!-- Message Form Card -->
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-light py-3">
          <h1 class="h4 mb-0"><i class="fas fa-pen-to-square me-2"></i>Compose New Message</h1>
        </div>
        <div class="card-body p-4">
          <form method="post" action="{% url 'message_create' %}">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
              <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                  {{ error }}
                {% endfor %}
              </div>
            {% endif %}
            
            <!-- Recipient Field -->
            <div class="mb-3">
              <label for="{{ form.recipient.id_for_label }}" class="form-label">{{ form.recipient.label }}</label>
              {{ form.recipient }}
              {% if form.recipient.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.recipient.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
              {% if form.recipient.help_text %}
                <div class="form-text">{{ form.recipient.help_text }}</div>
              {% endif %}
            </div>
            
            <!-- Subject Field -->
            <div class="mb-3">
              <label for="{{ form.subject.id_for_label }}" class="form-label">{{ form.subject.label }}</label>
              {{ form.subject }}
              {% if form.subject.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.subject.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
              {% if form.subject.help_text %}
                <div class="form-text">{{ form.subject.help_text }}</div>
              {% endif %}
            </div>
            
            <!-- Content Field -->
            <div class="mb-4">
              <label for="{{ form.content.id_for_label }}" class="form-label">{{ form.content.label }}</label>
              {{ form.content }}
              {% if form.content.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.content.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
              {% if form.content.help_text %}
                <div class="form-text">{{ form.content.help_text }}</div>
              {% endif %}
            </div>
            
            <!-- Form Actions -->
            <div class="d-flex justify-content-end">
              <a href="{% url 'message_list' %}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-times me-1"></i> Cancel
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-paper-plane me-1"></i> Send Message
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
