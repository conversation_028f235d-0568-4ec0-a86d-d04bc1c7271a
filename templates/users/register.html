{% extends 'base/base.html' %}

{% block title %}Register - CompletoPLUS{% endblock %}

{% block content %}
<div class="container py-4">
  <div class="row justify-content-center">
    <div class="col-md-6">
      <div class="card shadow border-0 rounded-4 overflow-hidden">
        <div
          class="card-header text-white text-center py-4"
          style="
            background: linear-gradient(
              135deg,
              var(--completoplus-blue),
              var(--completoplus-indigo)
            );
          "
        >
          <h2 class="mb-1 fw-bold">Create Your Account</h2>
          <p class="mb-0 opacity-75">
            Join CompletoPLUS and experience academic excellence
          </p>
        </div>

        <div class="card-body p-4">
          {% if form.non_field_errors %}
          <div
            class="alert alert-danger alert-dismissible fade show"
            role="alert"
          >
            <div class="d-flex align-items-center">
              <i class="fas fa-exclamation-circle me-2"></i>
              <div>
                {% for error in form.non_field_errors %} 
                  {{ error }} 
                {% endfor %}
              </div>
            </div>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="alert"
              aria-label="Close"
            ></button>
          </div>
          {% endif %}

          <form method="post" novalidate>
            {% csrf_token %}
            <!-- novalidate attribute added to ensure server-side validation is used -->

            <div class="mb-3">
              <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-user"></i></span>
                <input
                  type="text"
                  name="{{ form.username.name }}"
                  id="{{ form.username.id_for_label }}"
                  class="form-control {% if form.username.errors %}is-invalid{% endif %}"
                  required
                />
              </div>
              {% if form.username.errors %}
              <div class="invalid-feedback d-block">
                {% for error in form.username.errors %} 
                  {{ error }} 
                {% endfor %}
              </div>
              <small class="form-text text-muted">
                Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.
              </small>
              {% endif %}
            </div>

            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name</label>
                <div class="input-group">
                  <span class="input-group-text"><i class="fas fa-user"></i></span>
                  <input
                    type="text"
                    name="{{ form.first_name.name }}"
                    id="{{ form.first_name.id_for_label }}"
                    class="form-control {% if form.first_name.errors %}is-invalid{% endif %}"
                    required
                  />
                </div>
                {% if form.first_name.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.first_name.errors %} 
                    {{ error }} 
                  {% endfor %}
                </div>
                {% endif %}
              </div>

              <div class="col-md-6 mb-3">
                <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name</label>
                <div class="input-group">
                  <span class="input-group-text"><i class="fas fa-user"></i></span>
                  <input
                    type="text"
                    name="{{ form.last_name.name }}"
                    id="{{ form.last_name.id_for_label }}"
                    class="form-control {% if form.last_name.errors %}is-invalid{% endif %}"
                    required
                  />
                </div>
                {% if form.last_name.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.last_name.errors %} 
                    {{ error }} 
                  {% endfor %}
                </div>
                {% endif %}
              </div>
            </div>

            <div class="mb-3">
              <label for="{{ form.email.id_for_label }}" class="form-label">Email</label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                <input
                  type="email"
                  name="{{ form.email.name }}"
                  id="{{ form.email.id_for_label }}"
                  class="form-control {% if form.email.errors %}is-invalid{% endif %}"
                  required
                />
              </div>
              {% if form.email.errors %}
              <div class="invalid-feedback d-block">
                {% for error in form.email.errors %} 
                  {{ error }} 
                {% endfor %}
              </div>
              {% endif %}
            </div>

            <div class="mb-3">
              <label for="{{ form.password1.id_for_label }}" class="form-label">Password</label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                <input
                  type="password"
                  name="{{ form.password1.name }}"
                  id="{{ form.password1.id_for_label }}"
                  class="form-control {% if form.password1.errors %}is-invalid{% endif %}"
                  required
                />
              </div>
              {% if form.password1.errors %}
              <div class="invalid-feedback d-block">
                {% for error in form.password1.errors %} 
                  {{ error }} 
                {% endfor %}
              </div>
              {% endif %}
            </div>

            <div
              id="password-requirements"
              class="bg-light p-3 rounded-3 mb-3"
              style="display: none"
            >
              <h6 class="mb-2">
                <i class="fas fa-shield-alt me-2"></i>Password Requirements
              </h6>
              <ul class="mb-0 ps-3 text-muted small">
                <li>Must be at least 8 characters long</li>
                <li>Cannot be too similar to your personal information</li>
                <li>Cannot be a commonly used password</li>
                <li>Cannot be entirely numeric</li>
              </ul>
            </div>

            <div class="mb-4">
              <label for="{{ form.password2.id_for_label }}" class="form-label">Confirm Password</label>
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                <input
                  type="password"
                  name="{{ form.password2.name }}"
                  id="{{ form.password2.id_for_label }}"
                  class="form-control {% if form.password2.errors %}is-invalid{% endif %}"
                  required
                />
              </div>
              {% if form.password2.errors %}
              <div class="invalid-feedback d-block">
                {% for error in form.password2.errors %} 
                  {{ error }} 
                {% endfor %}
              </div>
              {% endif %}
            </div>

            <div class="mb-3 form-check">
              <input
                type="checkbox"
                name="terms_agreement"
                id="id_terms_agreement"
                class="form-check-input {% if form.terms_agreement.errors %}is-invalid{% endif %}"
                required
              />
              <label class="form-check-label" for="id_terms_agreement">
                I agree to the
                <a href="#" target="_blank">Terms of Service</a> and
                <a href="#" target="_blank">Privacy Policy</a>
              </label>
              {% if form.terms_agreement.errors %}
              <div class="invalid-feedback d-block">
                {% for error in form.terms_agreement.errors %} 
                  {{ error }} 
                {% endfor %}
              </div>
              {% endif %}
            </div>

            <!-- Hidden field for user_type -->
            {{ form.user_type }}

            <div class="d-grid">
              <button
                type="submit"
                class="btn btn-primary btn-lg"
                style="
                  background: linear-gradient(
                    135deg,
                    var(--completoplus-blue),
                    var(--completoplus-indigo)
                  );
                  border: none;
                "
              >
                <i class="fas fa-user-plus me-2"></i>Create Account
              </button>
            </div>
          </form>
        </div>

        <div class="card-footer bg-light text-center py-3">
          <p class="mb-0">
            Already have an account?
            <a
              href="{% url 'login' %}"
              class="fw-medium"
              style="color: var(--completoplus-blue)"
              >Sign in</a
            >
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Get the password field and requirements div
    const passwordField = document.getElementById(
      "{{ form.password1.id_for_label }}"
    );
    const passwordRequirements = document.getElementById(
      "password-requirements"
    );

    // Show requirements when password field is focused
    passwordField.addEventListener("focus", function () {
      passwordRequirements.style.display = "block";
    });

    // Hide requirements when password field loses focus (unless there are errors)
    passwordField.addEventListener("blur", function () {
      if (!passwordField.classList.contains("is-invalid")) {
        passwordRequirements.style.display = "none";
      }
    });

    // Show requirements if there are password errors or any form errors
    if (
      passwordField.classList.contains("is-invalid") ||
      document.querySelectorAll(".invalid-feedback").length > 0
    ) {
      passwordRequirements.style.display = "block";
    }
  });
</script>
{% endblock %}
