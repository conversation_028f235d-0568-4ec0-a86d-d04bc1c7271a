{% extends 'base/base.html' %}

{% block title %}{{ message.subject }} - CompletoPLUS{% endblock %}

{% block content %}
<div class="container py-4">
  <!-- Breadcrumb -->
  <nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">Dashboard</a></li>
      <li class="breadcrumb-item"><a href="{% url 'message_list' %}?tab={% if is_sender %}sent{% else %}inbox{% endif %}">Messages</a></li>
      <li class="breadcrumb-item active" aria-current="page">{{ message.subject|truncatechars:30 }}</li>
    </ol>
  </nav>

  <!-- Message Card -->
  <div class="card border-0 shadow-sm">
    <div class="card-header bg-light d-flex justify-content-between align-items-center py-3">
      <div>
        <h1 class="h4 mb-0">{{ message.subject }}</h1>
      </div>
      <div>
        <a href="{% url 'message_create' %}?recipient={{ message.sender.id }}" class="btn btn-outline-primary btn-sm">
          <i class="fas fa-reply me-1"></i> Reply
        </a>
      </div>
    </div>
    <div class="card-body">
      <!-- Message Meta -->
      <div class="message-meta mb-4 pb-3 border-bottom">
        <div class="d-flex align-items-center">
          <div class="avatar-circle bg-primary text-white me-3">
            {% if is_sender %}
              {{ message.recipient.get_full_name|default:message.recipient.username|slice:":1" }}
            {% else %}
              {{ message.sender.get_full_name|default:message.sender.username|slice:":1" }}
            {% endif %}
          </div>
          <div>
            <div class="d-flex align-items-center">
              <span class="fw-bold me-2">
                {% if is_sender %}
                  To: {{ message.recipient.get_full_name|default:message.recipient.username }}
                {% else %}
                  From: {{ message.sender.get_full_name|default:message.sender.username }}
                {% endif %}
              </span>
              <span class="badge {% if message.is_read %}bg-secondary{% else %}bg-primary{% endif %} me-2">
                {% if message.is_read %}Read{% else %}Unread{% endif %}
              </span>
            </div>
            <div class="text-muted small">
              {{ message.created_at|date:"F j, Y, g:i a" }}
            </div>
          </div>
        </div>
      </div>

      <!-- Message Content -->
      <div class="message-content">
        <div class="mb-4">
          {{ message.content|linebreaks }}
        </div>
      </div>
    </div>
    <div class="card-footer bg-light">
      <div class="d-flex justify-content-between">
        <a href="{% url 'message_list' %}?tab={% if is_sender %}sent{% else %}inbox{% endif %}" class="btn btn-outline-secondary">
          <i class="fas fa-arrow-left me-1"></i> Back to Messages
        </a>
        {% if not is_sender %}
        <a href="{% url 'message_create' %}?recipient={{ message.sender.id }}" class="btn btn-primary">
          <i class="fas fa-reply me-1"></i> Reply
        </a>
        {% endif %}
      </div>
    </div>
  </div>
</div>
{% endblock %}
