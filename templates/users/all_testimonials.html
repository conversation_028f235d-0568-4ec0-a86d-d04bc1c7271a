{% extends 'base/base.html' %}

{% block title %}Client Testimonials - CompletoPLUS{% endblock %}

{% block meta_description %}Read what our clients say about CompletoPLUS services. Real testimonials from students and professionals who have used our academic, data analysis, web development, and machine learning services.{% endblock %}

{% block content %}
<main class="py-5">
  <div class="container">
    <div class="row mb-4">
      <div class="col-12">
        <h1 class="h2 text-center mb-2">Client Testimonials</h1>
        <p class="text-center text-muted mb-4">What our clients say about our services</p>
      </div>
    </div>

    <!-- Filters and Sorting -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card border-0 shadow-sm">
          <div class="card-body p-3">
            <form method="get" id="filter-form" class="row g-3">
              <!-- Service Type Filter -->
              <div class="col-md-4">
                <label class="form-label"><i class="fas fa-tag me-1"></i> Service Type</label>
                <select name="service_type" class="form-select form-select-sm" onchange="this.form.submit()">
                  <option value="" {% if not current_service_type %}selected{% endif %}>All Services</option>
                  {% for service_type, service_name in service_type_filters %}
                    <option value="{{ service_type }}" {% if current_service_type == service_type %}selected{% endif %}>
                      {{ service_name }}
                    </option>
                  {% endfor %}
                </select>
              </div>

              <!-- Rating Filter -->
              <div class="col-md-4">
                <label class="form-label"><i class="fas fa-star me-1"></i> Rating</label>
                <select name="rating" class="form-select form-select-sm" onchange="this.form.submit()">
                  <option value="" {% if not current_rating %}selected{% endif %}>All Ratings</option>
                  <option value="5" {% if current_rating == '5' %}selected{% endif %}>5 Stars</option>
                  <option value="4" {% if current_rating == '4' %}selected{% endif %}>4 Stars</option>
                  <option value="3" {% if current_rating == '3' %}selected{% endif %}>3 Stars</option>
                  <option value="2" {% if current_rating == '2' %}selected{% endif %}>2 Stars</option>
                  <option value="1" {% if current_rating == '1' %}selected{% endif %}>1 Star</option>
                </select>
              </div>

              <!-- Sort Order -->
              <div class="col-md-4">
                <label class="form-label"><i class="fas fa-sort me-1"></i> Sort By</label>
                <select name="sort" class="form-select form-select-sm" onchange="this.form.submit()">
                  <option value="highest" {% if current_sort == 'highest' %}selected{% endif %}>Highest Rating</option>
                  <option value="lowest" {% if current_sort == 'lowest' %}selected{% endif %}>Lowest Rating</option>
                  <option value="newest" {% if current_sort == 'newest' %}selected{% endif %}>Newest First</option>
                  <option value="oldest" {% if current_sort == 'oldest' %}selected{% endif %}>Oldest First</option>
                  <option value="featured" {% if current_sort == 'featured' %}selected{% endif %}>Featured Only</option>
                </select>
              </div>

              <!-- Hidden page input to reset to page 1 when filters change -->
              <input type="hidden" name="page" value="1">

              <!-- Clear Filters and Add Testimonial -->
              <div class="col-12 d-flex justify-content-between align-items-center mt-3">
                <div>
                  {% if current_service_type or current_rating or current_sort != 'highest' %}
                    <a href="{% url 'all_testimonials' %}" class="btn btn-outline-secondary btn-sm">
                      <i class="fas fa-times me-1"></i> Clear Filters
                    </a>
                  {% endif %}
                </div>

                <div>
                  {% if user.is_authenticated and user.is_client_user %}
                    <a href="{% url 'add_testimonial' %}" class="btn btn-primary btn-sm">
                      <i class="fas fa-plus me-1"></i> Add Your Testimonial
                    </a>
                  {% else %}
                    <a href="{% url 'login' %}?next={% url 'add_testimonial' %}" class="btn btn-outline-primary btn-sm">
                      <i class="fas fa-sign-in-alt me-1"></i> Login to Add Testimonial
                    </a>
                  {% endif %}
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Testimonials Grid -->
    <div class="row">
      {% if testimonials %}
        {% for testimonial in testimonials %}
          <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100 border-0 shadow-sm testimonial-card">
              <div class="card-body p-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                  <div class="d-flex align-items-center">
                    <div class="text-warning me-2">
                      {% for i in "12345" %}
                        {% if forloop.counter <= testimonial.rating %}
                          <i class="fas fa-star"></i>
                        {% else %}
                          <i class="far fa-star"></i>
                        {% endif %}
                      {% endfor %}
                    </div>
                    <span class="small text-muted">{{ testimonial.rating }}.0</span>
                  </div>
                  {% if testimonial.is_featured %}
                    <span class="badge bg-warning text-dark"><i class="fas fa-award me-1"></i>Featured</span>
                  {% endif %}
                </div>
                <p class="card-text fst-italic mb-4">"{{ testimonial.content }}"</p>
                <div class="d-flex align-items-center mt-auto">
                  <div class="avatar-circle bg-primary text-white me-2">
                    {{ testimonial.client.get_initials }}
                  </div>
                  <div>
                    <p class="mb-0 fw-medium">{{ testimonial.client.get_full_name }}</p>
                    <p class="small text-muted mb-0">{{ testimonial.get_service_type_display }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        {% endfor %}
      {% else %}
        <div class="col-12 text-center py-5">
          <div class="mb-4">
            <i class="fas fa-comment-dots fa-4x text-muted"></i>
          </div>
          <h2 class="h4 mb-3">No Testimonials Found</h2>
          <p class="text-muted mb-4">
            {% if request.GET.service_type %}
              We don't have any testimonials for this service type yet.
              <a href="{% url 'all_testimonials' %}">View all testimonials</a> instead.
            {% else %}
              We don't have any testimonials yet. Be the first to share your experience!
            {% endif %}
          </p>
          {% if user.is_authenticated and user.is_client_user %}
            <a href="{% url 'add_testimonial' %}" class="btn btn-primary">
              <i class="fas fa-plus me-2"></i> Add Your Testimonial
            </a>
          {% else %}
            <a href="{% url 'login' %}?next={% url 'add_testimonial' %}" class="btn btn-primary">
              <i class="fas fa-sign-in-alt me-2"></i> Login to Add Testimonial
            </a>
          {% endif %}
        </div>
      {% endif %}
    </div>

    <!-- Pagination -->
    {% if testimonials.has_other_pages %}
      <div class="row mt-4">
        <div class="col-12">
          <nav aria-label="Testimonial pagination">
            <ul class="pagination justify-content-center">
              {% if testimonials.has_previous %}
                <li class="page-item">
                  <a class="page-link" href="?{% if current_service_type %}service_type={{ current_service_type }}&{% endif %}{% if current_rating %}rating={{ current_rating }}&{% endif %}{% if current_sort %}sort={{ current_sort }}&{% endif %}page=1" aria-label="First">
                    <span aria-hidden="true">&laquo;&laquo;</span>
                  </a>
                </li>
                <li class="page-item">
                  <a class="page-link" href="?{% if current_service_type %}service_type={{ current_service_type }}&{% endif %}{% if current_rating %}rating={{ current_rating }}&{% endif %}{% if current_sort %}sort={{ current_sort }}&{% endif %}page={{ testimonials.previous_page_number }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                  </a>
                </li>
              {% else %}
                <li class="page-item disabled">
                  <span class="page-link">&laquo;&laquo;</span>
                </li>
                <li class="page-item disabled">
                  <span class="page-link">&laquo;</span>
                </li>
              {% endif %}

              {% for i in testimonials.paginator.page_range %}
                {% if testimonials.number == i %}
                  <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                {% elif i > testimonials.number|add:'-3' and i < testimonials.number|add:'3' %}
                  <li class="page-item">
                    <a class="page-link" href="?{% if current_service_type %}service_type={{ current_service_type }}&{% endif %}{% if current_rating %}rating={{ current_rating }}&{% endif %}{% if current_sort %}sort={{ current_sort }}&{% endif %}page={{ i }}">{{ i }}</a>
                  </li>
                {% endif %}
              {% endfor %}

              {% if testimonials.has_next %}
                <li class="page-item">
                  <a class="page-link" href="?{% if current_service_type %}service_type={{ current_service_type }}&{% endif %}{% if current_rating %}rating={{ current_rating }}&{% endif %}{% if current_sort %}sort={{ current_sort }}&{% endif %}page={{ testimonials.next_page_number }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                  </a>
                </li>
                <li class="page-item">
                  <a class="page-link" href="?{% if current_service_type %}service_type={{ current_service_type }}&{% endif %}{% if current_rating %}rating={{ current_rating }}&{% endif %}{% if current_sort %}sort={{ current_sort }}&{% endif %}page={{ testimonials.paginator.num_pages }}" aria-label="Last">
                    <span aria-hidden="true">&raquo;&raquo;</span>
                  </a>
                </li>
              {% else %}
                <li class="page-item disabled">
                  <span class="page-link">&raquo;</span>
                </li>
                <li class="page-item disabled">
                  <span class="page-link">&raquo;&raquo;</span>
                </li>
              {% endif %}
            </ul>
          </nav>
        </div>
      </div>
    {% endif %}
  </div>
</main>
{% endblock %}

{% block extra_css %}
<style>
  .testimonial-card {
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
  }

  .testimonial-card .card-body {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .pagination .page-link {
    color: var(--bs-primary);
    border-color: var(--bs-gray-200);
  }

  .pagination .page-item.active .page-link {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
  }

  .pagination .page-item.disabled .page-link {
    color: var(--bs-gray-500);
  }
</style>
{% endblock %}
