{% extends 'base/base.html' %}

{% block title %}Emergency Registration - CompletoPLUS{% endblock %}

{% block content %}
<div class="container py-4">
  <div class="row justify-content-center">
    <div class="col-md-6">
      <div class="card shadow border-0 rounded-4 overflow-hidden">
        <div
          class="card-header text-white text-center py-4"
          style="
            background: linear-gradient(
              135deg,
              var(--completoplus-blue),
              var(--completoplus-indigo)
            );
          "
        >
          <h2 class="mb-1 fw-bold">Emergency Registration</h2>
          <p class="mb-0 opacity-75">
            This is a simplified registration form for emergency use
          </p>
        </div>

        <div class="card-body p-4">
          {% if messages %}
          <div class="messages">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
              {{ message }}
              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
          </div>
          {% endif %}

          <form method="post" novalidate>
            {% csrf_token %}

            <div class="mb-3">
              <label for="id_username" class="form-label">Username</label>
              <input
                type="text"
                name="username"
                id="id_username"
                class="form-control"
                required
              />
              {% if form.username.errors %}
              <div class="text-danger mt-1">
                {% for error in form.username.errors %} {{ error }} {% endfor %}
              </div>
              {% endif %}
            </div>

            <div class="mb-3">
              <label for="id_email" class="form-label">Email</label>
              <input
                type="email"
                name="email"
                id="id_email"
                class="form-control"
                required
              />
              {% if form.email.errors %}
              <div class="text-danger mt-1">
                {% for error in form.email.errors %} {{ error }} {% endfor %}
              </div>
              {% endif %}
            </div>

            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="id_first_name" class="form-label">First Name</label>
                <input
                  type="text"
                  name="first_name"
                  id="id_first_name"
                  class="form-control"
                  required
                />
                {% if form.first_name.errors %}
                <div class="text-danger mt-1">
                  {% for error in form.first_name.errors %} {{ error }} {% endfor %}
                </div>
                {% endif %}
              </div>

              <div class="col-md-6 mb-3">
                <label for="id_last_name" class="form-label">Last Name</label>
                <input
                  type="text"
                  name="last_name"
                  id="id_last_name"
                  class="form-control"
                  required
                />
                {% if form.last_name.errors %}
                <div class="text-danger mt-1">
                  {% for error in form.last_name.errors %} {{ error }} {% endfor %}
                </div>
                {% endif %}
              </div>
            </div>

            <div class="mb-3">
              <label for="id_password1" class="form-label">Password</label>
              <input
                type="password"
                name="password1"
                id="id_password1"
                class="form-control"
                required
              />
              {% if form.password1.errors %}
              <div class="text-danger mt-1">
                {% for error in form.password1.errors %} {{ error }} {% endfor %}
              </div>
              {% endif %}
            </div>

            <div class="mb-4">
              <label for="id_password2" class="form-label">Confirm Password</label>
              <input
                type="password"
                name="password2"
                id="id_password2"
                class="form-control"
                required
              />
              {% if form.password2.errors %}
              <div class="text-danger mt-1">
                {% for error in form.password2.errors %} {{ error }} {% endfor %}
              </div>
              {% endif %}
            </div>

            <div class="mb-3 form-check">
              <input
                type="checkbox"
                name="terms_agreement"
                id="id_terms_agreement"
                class="form-check-input"
                required
              />
              <label class="form-check-label" for="id_terms_agreement">
                I agree to the Terms of Service and Privacy Policy
              </label>
              {% if form.terms_agreement.errors %}
              <div class="text-danger mt-1">
                {% for error in form.terms_agreement.errors %} {{ error }} {% endfor %}
              </div>
              {% endif %}
            </div>

            <!-- Hidden field for user_type -->
            <input type="hidden" name="user_type" value="client">

            <div class="d-grid">
              <button
                type="submit"
                class="btn btn-primary btn-lg"
              >
                Create Account
              </button>
            </div>
          </form>
        </div>

        <div class="card-footer bg-light text-center py-3">
          <p class="mb-0">
            Already have an account?
            <a
              href="{% url 'login' %}"
              class="fw-medium"
            >Sign in</a
            >
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
