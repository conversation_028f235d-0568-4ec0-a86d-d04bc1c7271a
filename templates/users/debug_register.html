{% extends 'base/base.html' %}

{% block title %}Debug Registration - CompletoPLUS{% endblock %}

{% block content %}
<div class="container py-4">
  <div class="row justify-content-center">
    <div class="col-md-6">
      <div class="card shadow border-0 rounded-4 overflow-hidden">
        <div
          class="card-header text-white text-center py-4"
          style="
            background: linear-gradient(
              135deg,
              var(--completoplus-blue),
              var(--completoplus-indigo)
            );
          "
        >
          <h2 class="mb-1 fw-bold">Debug Registration</h2>
          <p class="mb-0 opacity-75">
            This is a simplified registration form for debugging
          </p>
        </div>

        <div class="card-body p-4">
          {% if form.non_field_errors %}
          <div
            class="alert alert-danger alert-dismissible fade show"
            role="alert"
          >
            <div class="d-flex align-items-center">
              <i class="fas fa-exclamation-circle me-2"></i>
              <div>
                {% for error in form.non_field_errors %} {{ error }} {% endfor %}
              </div>
            </div>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="alert"
              aria-label="Close"
            ></button>
          </div>
          {% endif %}

          <form method="post" novalidate>
            {% csrf_token %}

            <div class="mb-3">
              <label for="{{ form.username.id_for_label }}" class="form-label"
                >Username</label
              >
              <input
                type="text"
                name="{{ form.username.name }}"
                id="{{ form.username.id_for_label }}"
                class="form-control {% if form.username.errors %}is-invalid{% endif %}"
                required
              />
              {% if form.username.errors %}
              <div class="invalid-feedback d-block">
                {% for error in form.username.errors %} {{ error }} {% endfor %}
              </div>
              {% endif %}
            </div>

            <div class="mb-3">
              <label for="{{ form.email.id_for_label }}" class="form-label"
                >Email</label
              >
              <input
                type="email"
                name="{{ form.email.name }}"
                id="{{ form.email.id_for_label }}"
                class="form-control {% if form.email.errors %}is-invalid{% endif %}"
                required
              />
              {% if form.email.errors %}
              <div class="invalid-feedback d-block">
                {% for error in form.email.errors %} {{ error }} {% endfor %}
              </div>
              {% endif %}
            </div>

            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="{{ form.first_name.id_for_label }}" class="form-label"
                  >First Name</label
                >
                <input
                  type="text"
                  name="{{ form.first_name.name }}"
                  id="{{ form.first_name.id_for_label }}"
                  class="form-control {% if form.first_name.errors %}is-invalid{% endif %}"
                  required
                />
                {% if form.first_name.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.first_name.errors %} {{ error }} {% endfor %}
                </div>
                {% endif %}
              </div>

              <div class="col-md-6 mb-3">
                <label for="{{ form.last_name.id_for_label }}" class="form-label"
                  >Last Name</label
                >
                <input
                  type="text"
                  name="{{ form.last_name.name }}"
                  id="{{ form.last_name.id_for_label }}"
                  class="form-control {% if form.last_name.errors %}is-invalid{% endif %}"
                  required
                />
                {% if form.last_name.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.last_name.errors %} {{ error }} {% endfor %}
                </div>
                {% endif %}
              </div>
            </div>

            <div class="mb-3">
              <label for="{{ form.password1.id_for_label }}" class="form-label"
                >Password</label
              >
              <input
                type="password"
                name="{{ form.password1.name }}"
                id="{{ form.password1.id_for_label }}"
                class="form-control {% if form.password1.errors %}is-invalid{% endif %}"
                required
              />
              {% if form.password1.errors %}
              <div class="invalid-feedback d-block">
                {% for error in form.password1.errors %} {{ error }} {% endfor %}
              </div>
              {% endif %}
            </div>

            <div class="mb-4">
              <label for="{{ form.password2.id_for_label }}" class="form-label"
                >Confirm Password</label
              >
              <input
                type="password"
                name="{{ form.password2.name }}"
                id="{{ form.password2.id_for_label }}"
                class="form-control {% if form.password2.errors %}is-invalid{% endif %}"
                required
              />
              {% if form.password2.errors %}
              <div class="invalid-feedback d-block">
                {% for error in form.password2.errors %} {{ error }} {% endfor %}
              </div>
              {% endif %}
            </div>

            <div class="mb-3 form-check">
              <input
                type="checkbox"
                name="terms_agreement"
                id="id_terms_agreement"
                class="form-check-input"
                required
              />
              <label class="form-check-label" for="id_terms_agreement">
                I agree to the Terms of Service and Privacy Policy
              </label>
            </div>

            <!-- Hidden field for user_type -->
            <input type="hidden" name="user_type" value="client">

            <div class="d-grid">
              <button
                type="submit"
                class="btn btn-primary btn-lg"
              >
                Create Account
              </button>
            </div>
          </form>
        </div>

        <div class="card-footer bg-light text-center py-3">
          <p class="mb-0">
            Already have an account?
            <a
              href="{% url 'login' %}"
              class="fw-medium"
            >Sign in</a
            >
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
