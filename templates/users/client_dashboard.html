{% extends 'base/base.html' %}

{% block title %}Dashboard - CompletoPLUS{% endblock %}

{% block extra_css %}
<style>
  /* Timeline styling */
  .timeline {
    position: relative;
    padding-left: 10px;
  }

  .timeline-item {
    position: relative;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  .timeline-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
  }

  .timeline-icon {
    min-width: 40px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .timeline-content {
    flex: 1;
  }

  .timeline-content h6 {
    font-weight: 600;
    margin-bottom: 5px;
  }

  /* Card styling */
  .card {
    border-radius: 10px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    overflow: hidden;
  }

  .card.h-100 {
    height: 100%;
  }

  .card-header {
    border-bottom: none;
    padding: 15px 20px;
  }

  .card-body {
    padding: 20px;
  }

  /* Badge styling */
  .badge {
    font-weight: 500;
    padding: 0.4em 0.8em;
    border-radius: 30px;
  }

  /* Testimonial card styling */
  .card.border-0.shadow-sm {
    transition: transform 0.2s, box-shadow 0.2s;
    border-radius: 8px;
  }

  .card.border-0.shadow-sm:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
  }

  /* Table styling */
  .table {
    margin-bottom: 0;
  }

  .table th {
    border-top: none;
    font-weight: 600;
    color: #555;
  }

  /* Button styling */
  .btn {
    border-radius: 5px;
    padding: 8px 16px;
    font-weight: 500;
  }

  .btn-sm {
    padding: 5px 10px;
  }

  /* Welcome banner */
  .card.bg-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%) !important;
  }

  /* Stats cards */
  .border-left-primary {
    border-left: 4px solid #4e73df;
  }

  .border-left-success {
    border-left: 4px solid #1cc88a;
  }

  .border-left-info {
    border-left: 4px solid #36b9cc;
  }

  .text-primary {
    color: #4e73df !important;
  }

  .text-success {
    color: #1cc88a !important;
  }

  .text-info {
    color: #36b9cc !important;
  }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
  <div class="row">
    <div class="col-12">
      <!-- Welcome Banner -->
      <div class="card mb-4 bg-primary text-white">
        <div class="card-body py-4">
          <div class="d-flex align-items-center">
            <div>
              <h4 class="mb-1">Welcome back, {{ request.user.first_name }}!</h4>
              <p class="mb-0">Here's an overview of your projects and recent activities.</p>
            </div>
            <div class="ms-auto">
              <a href="{% url 'project_create' %}" class="btn btn-light">
                <i class="fas fa-plus me-2"></i>New Project
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="row mb-4">
        <div class="col-md-4 mb-3 mb-md-0">
          <div class="card border-left-primary h-100">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <div class="small text-muted mb-1">Total Projects</div>
                  <div class="h3 mb-0 font-weight-bold">{{ total_projects }}</div>
                </div>
                <div class="text-primary">
                  <i class="fas fa-folder fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-4 mb-3 mb-md-0">
          <div class="card border-left-success h-100">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <div class="small text-muted mb-1">Total Files</div>
                  <div class="h3 mb-0 font-weight-bold">{{ total_files }}</div>
                </div>
                <div class="text-success">
                  <i class="fas fa-file-alt fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card border-left-info h-100">
            <div class="card-body">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <div class="small text-muted mb-1">Completed Projects</div>
                  <div class="h3 mb-0 font-weight-bold">
                    {% if projects_by_status %}
                      {{ projects_by_status.completed }}
                    {% else %}
                      0
                    {% endif %}
                  </div>
                </div>
                <div class="text-info">
                  <i class="fas fa-check-circle fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Projects Overview -->
      <div class="row mb-4">
        <div class="col-md-12">
          <div class="card">
            <div class="card-header bg-primary text-white">
              <h5 class="mb-0"><i class="fas fa-folder me-2"></i>Recent Projects</h5>
            </div>
            <div class="card-body">
              {% if recent_projects %}
                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <th>Project Name</th>
                        <th>Status</th>
                        <th>Last Updated</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {% for project in recent_projects %}
                        <tr>
                          <td>
                            <a href="{% url 'project_detail' project.id %}" class="text-decoration-none">
                              {{ project.name }}
                            </a>
                          </td>
                          <td>
                            <span class="badge {% if project.status == 'pending' %}bg-warning{% elif project.status == 'in_progress' %}bg-info{% elif project.status == 'completed' %}bg-success{% elif project.status == 'on_hold' %}bg-secondary{% endif %}">
                              {{ project.get_status_display }}
                            </span>
                          </td>
                          <td>{{ project.updated_at|date:"M d, Y" }}</td>
                          <td>
                            <a href="{% url 'project_detail' project.id %}" class="btn btn-sm btn-outline-primary">
                              <i class="fas fa-eye"></i>
                            </a>
                          </td>
                        </tr>
                      {% endfor %}
                    </tbody>
                  </table>
                </div>
                <div class="text-end">
                  <a href="{% url 'project_list' %}" class="btn btn-outline-primary">View All Projects</a>
                </div>
              {% else %}
                <div class="text-center py-4">
                  <div class="mb-3">
                    <i class="fas fa-folder-open fa-3x text-muted"></i>
                  </div>
                  <h6 class="mb-3">You don't have any projects yet</h6>
                  <p class="text-muted mb-4">Create your first project to get started with CompletoPLUS.</p>
                  <a href="{% url 'project_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> Create New Project
                  </a>
                </div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
