{% extends 'base/base.html' %}

{% block title %}Password Reset Complete - CompletoPLUS{% endblock %}

{% block extra_css %}
<style>
  .password-reset-container {
    max-width: 450px;
    margin: 2rem auto;
  }

  .password-reset-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .password-reset-header {
    background: linear-gradient(
      135deg,
      var(--completoplus-blue),
      var(--completoplus-indigo)
    );
    padding: 2rem;
    text-align: center;
    color: white;
  }

  .password-reset-header h2 {
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .password-reset-header p {
    opacity: 0.9;
    margin-bottom: 0;
  }

  .password-reset-body {
    padding: 2rem;
    text-align: center;
  }

  .password-reset-icon {
    font-size: 3rem;
    color: #28a745;
    margin-bottom: 1.5rem;
  }

  .password-reset-message {
    margin-bottom: 1.5rem;
    color: var(--gray-700);
  }

  .btn-login {
    background: linear-gradient(
      135deg,
      var(--completoplus-blue),
      var(--completoplus-indigo)
    );
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    color: white;
    text-decoration: none;
    display: inline-block;
  }

  .btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
    color: white;
  }

  /* Animated background */
  .animated-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
  }

  .animated-bg .shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(
      135deg,
      rgba(67, 97, 238, 0.1),
      rgba(58, 12, 163, 0.1)
    );
    animation: float 15s infinite ease-in-out;
  }

  .animated-bg .shape:nth-child(1) {
    width: 300px;
    height: 300px;
    top: -150px;
    right: -50px;
    animation-delay: 0s;
  }

  .animated-bg .shape:nth-child(2) {
    width: 200px;
    height: 200px;
    bottom: -100px;
    left: -50px;
    animation-delay: 3s;
  }

  .animated-bg .shape:nth-child(3) {
    width: 150px;
    height: 150px;
    bottom: 50%;
    right: -75px;
    animation-delay: 7s;
  }

  @keyframes float {
    0% {
      transform: translateY(0) rotate(0deg);
    }
    50% {
      transform: translateY(-20px) rotate(5deg);
    }
    100% {
      transform: translateY(0) rotate(0deg);
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="animated-bg">
  <div class="shape"></div>
  <div class="shape"></div>
  <div class="shape"></div>
</div>

<div class="container password-reset-container">
  <div class="password-reset-card">
    <div class="password-reset-header">
      <h2>Password Reset Complete</h2>
      <p>Your password has been successfully reset</p>
    </div>

    <div class="password-reset-body">
      <div class="password-reset-icon">
        <i class="fas fa-check-circle"></i>
      </div>
      <div class="password-reset-message">
        <p>Your password has been set. You may now log in with your new password.</p>
      </div>
      <a href="{% url 'login' %}" class="btn-login">
        <i class="fas fa-sign-in-alt me-2"></i>Log In Now
      </a>
    </div>
  </div>
</div>
{% endblock %}
