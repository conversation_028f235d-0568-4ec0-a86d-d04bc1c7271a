{% extends 'base/base.html' %}
{% block title %}Login - CompletoPLUS{% endblock %}
{% block extra_css %}
<style>
  .login-container {
    max-width: 450px;
    margin: 2rem auto;
  }

  .login-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .login-header {
    background: linear-gradient(
      135deg,
      var(--completoplus-blue),
      var(--completoplus-indigo)
    );
    padding: 2rem;
    text-align: center;
    color: white;
  }

  .login-header h2 {
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .login-header p {
    opacity: 0.9;
    margin-bottom: 0;
  }

  .login-body {
    padding: 2rem;
  }

  .form-floating {
    margin-bottom: 1.5rem;
  }

  .form-floating > label {
    color: var(--gray-600);
  }

  .form-control {
    border-radius: 8px;
    padding: 0.75rem 1rem;
    height: calc(3.5rem + 2px);
    border: 1px solid var(--gray-300);
    transition: all 0.3s ease;
  }

  .form-control:focus {
    border-color: var(--completoplus-blue);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.15);
  }

  .login-btn {
    background: linear-gradient(
      135deg,
      var(--completoplus-blue),
      var(--completoplus-indigo)
    );
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
  }

  .login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
  }

  .login-footer {
    text-align: center;
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
    background-color: var(--gray-50);
  }

  .login-footer a {
    color: var(--completoplus-blue);
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
  }

  .login-footer a:hover {
    color: var(--completoplus-indigo);
    text-decoration: underline;
  }

  .remember-me {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .remember-me input {
    margin-right: 0.5rem;
  }

  .forgot-password {
    text-align: right;
    margin-bottom: 1.5rem;
  }

  .forgot-password a {
    color: var(--gray-600);
    font-size: 0.875rem;
    text-decoration: none;
    transition: all 0.3s ease;
  }

  .forgot-password a:hover {
    color: var(--completoplus-blue);
  }

  /* Animated background */
  .animated-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
  }

  .animated-bg .shape {
    position: absolute;
    background: linear-gradient(
      45deg,
      var(--completoplus-blue),
      var(--completoplus-purple)
    );
    border-radius: 50%;
    opacity: 0.1;
    animation: float 15s ease-in-out infinite;
  }

  .animated-bg .shape:nth-child(1) {
    width: 300px;
    height: 300px;
    top: -150px;
    right: -50px;
    animation-delay: 0s;
  }

  .animated-bg .shape:nth-child(2) {
    width: 200px;
    height: 200px;
    bottom: -100px;
    left: -50px;
    animation-delay: 3s;
  }

  .animated-bg .shape:nth-child(3) {
    width: 150px;
    height: 150px;
    bottom: 50%;
    right: -75px;
    animation-delay: 7s;
  }

  @keyframes float {
    0% {
      transform: translateY(0) rotate(0deg);
    }
    50% {
      transform: translateY(-20px) rotate(5deg);
    }
    100% {
      transform: translateY(0) rotate(0deg);
    }
  }
</style>
{% endblock %}
{% block content %}
<div class="animated-bg">
  <div class="shape"></div>
  <div class="shape"></div>
  <div class="shape"></div>
</div>

<div class="container login-container">
  <div class="login-card">
    <div class="login-header">
      <h2>Welcome Back</h2>
      <p>Sign in to continue to CompletoPLUS</p>
    </div>

    <div class="login-body">
      {% if form.errors %}
      <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <div class="d-flex align-items-center">
          <i class="fas fa-exclamation-circle me-2"></i>
          <div>Your username and password didn't match. Please try again.</div>
        </div>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="alert"
          aria-label="Close"
        ></button>
      </div>
      {% endif %}

      <form method="post" action="{% url 'login' %}">
        {% csrf_token %} {% if next %}
        <input type="hidden" name="next" value="{{ next }}" />
        {% endif %}

        <div class="form-floating mb-3">
          <input
            type="text"
            name="username"
            id="id_username"
            class="form-control"
            placeholder="Username"
            required
          />
          <label for="id_username">Username</label>
        </div>

        <div class="form-floating mb-3">
          <input
            type="password"
            name="password"
            id="id_password"
            class="form-control"
            placeholder="Password"
            required
          />
          <label for="id_password">Password</label>
        </div>

        <div class="d-flex justify-content-between align-items-center mb-4">
          <div class="form-check">
            <input
              class="form-check-input"
              type="checkbox"
              name="remember_me"
              id="id_remember_me"
            />
            <label class="form-check-label" for="id_remember_me">
              Remember me
            </label>
          </div>
          <div>
            <a href="{% url 'password_reset' %}" class="text-muted"
              >Forgot password?</a
            >
          </div>
        </div>

        <div class="d-grid">
          <button type="submit" class="btn btn-primary login-btn">
            <i class="fas fa-sign-in-alt me-2"></i>Sign In
          </button>
        </div>
      </form>
    </div>

    <div class="login-footer">
      <p class="mb-0">
        Don't have an account? <a href="{% url 'register' %}">Sign up</a>
      </p>
    </div>
  </div>
</div>
{% endblock %}
