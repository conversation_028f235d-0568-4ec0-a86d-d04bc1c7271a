{% extends 'base/base.html' %}

{% block title %}Reset Password - CompletoPLUS{% endblock %}

{% block meta_description %}Reset your CompletoPLUS password securely. Follow the instructions to create a new password and regain access to your account.{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-key me-2"></i>Reset Password</h4>
                </div>
                <div class="card-body p-4">
                    <p class="mb-4">Forgot your password? Enter your email address below, and we'll send you instructions to reset it.</p>
                    
                    {% if form.errors %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>Please correct the errors below.
                    </div>
                    {% endif %}
                    
                    <form method="post" class="needs-validation">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="id_email" class="form-label">Email Address</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input type="email" name="email" id="id_email" class="form-control {% if form.email.errors %}is-invalid{% endif %}" placeholder="Enter your email address" required>
                                {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {{ form.email.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                            <small class="form-text text-muted">Enter the email address you used to register.</small>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Send Reset Link
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer bg-light">
                    <div class="text-center">
                        <a href="{% url 'login' %}" class="text-decoration-none">
                            <i class="fas fa-arrow-left me-1"></i> Back to Login
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
