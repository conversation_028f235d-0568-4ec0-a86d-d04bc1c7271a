{% extends 'base/base.html' %}

{% block title %}Messages - CompletoPLUS{% endblock %}

{% block content %}
<div class="container py-4">
  <!-- Modern Header Section -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h1 class="h3 mb-1"><i class="fas fa-envelope me-2"></i>Messages</h1>
      <p class="text-muted mb-0">Communicate with administrators and team members</p>
    </div>
    <a href="{% url 'message_create' %}" class="btn btn-primary">
      <i class="fas fa-plus me-2"></i>New Message
    </a>
  </div>

  <!-- Messages Container -->
  <div class="card border-0 shadow-sm">
    <div class="card-body p-0">
      <!-- Tabs -->
      <ul class="nav nav-tabs" id="messagesTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link {% if active_tab == 'inbox' %}active{% endif %}" 
                  id="inbox-tab" 
                  data-bs-toggle="tab" 
                  data-bs-target="#inbox" 
                  type="button" 
                  role="tab" 
                  aria-controls="inbox" 
                  aria-selected="{% if active_tab == 'inbox' %}true{% else %}false{% endif %}">
            Inbox
            {% if unread_count > 0 %}
            <span class="badge bg-danger ms-2">{{ unread_count }}</span>
            {% endif %}
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link {% if active_tab == 'sent' %}active{% endif %}" 
                  id="sent-tab" 
                  data-bs-toggle="tab" 
                  data-bs-target="#sent" 
                  type="button" 
                  role="tab" 
                  aria-controls="sent" 
                  aria-selected="{% if active_tab == 'sent' %}true{% else %}false{% endif %}">
            Sent
          </button>
        </li>
      </ul>

      <!-- Tab Content -->
      <div class="tab-content p-4" id="messagesTabsContent">
        <!-- Inbox Tab -->
        <div class="tab-pane fade {% if active_tab == 'inbox' %}show active{% endif %}" id="inbox" role="tabpanel" aria-labelledby="inbox-tab">
          {% if received_messages %}
            <div class="table-responsive">
              <table class="table table-hover align-middle">
                <thead class="table-light">
                  <tr>
                    <th scope="col" style="width: 30%">From</th>
                    <th scope="col" style="width: 40%">Subject</th>
                    <th scope="col" style="width: 20%">Date</th>
                    <th scope="col" style="width: 10%">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {% for message in received_messages %}
                    <tr class="{% if not message.is_read %}fw-bold table-hover{% endif %}">
                      <td>
                        <div class="d-flex align-items-center">
                          <div class="avatar-circle bg-primary text-white me-2">
                            {{ message.sender.get_full_name|default:message.sender.username|slice:":1" }}
                          </div>
                          <span>{{ message.sender.get_full_name|default:message.sender.username }}</span>
                        </div>
                      </td>
                      <td>
                        <a href="{% url 'message_detail' message.id %}" class="text-decoration-none text-dark">
                          {{ message.subject|truncatechars:50 }}
                        </a>
                      </td>
                      <td>{{ message.created_at|date:"M d, Y" }}</td>
                      <td>
                        {% if message.is_read %}
                          <span class="badge bg-secondary">Read</span>
                        {% else %}
                          <span class="badge bg-primary">New</span>
                        {% endif %}
                      </td>
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="text-center py-5">
              <div class="mb-3">
                <i class="fas fa-inbox fa-4x text-muted"></i>
              </div>
              <h3 class="h5 mb-3">Your inbox is empty</h3>
              <p class="text-muted">You don't have any messages yet.</p>
            </div>
          {% endif %}
        </div>

        <!-- Sent Tab -->
        <div class="tab-pane fade {% if active_tab == 'sent' %}show active{% endif %}" id="sent" role="tabpanel" aria-labelledby="sent-tab">
          {% if sent_messages %}
            <div class="table-responsive">
              <table class="table table-hover align-middle">
                <thead class="table-light">
                  <tr>
                    <th scope="col" style="width: 30%">To</th>
                    <th scope="col" style="width: 40%">Subject</th>
                    <th scope="col" style="width: 20%">Date</th>
                    <th scope="col" style="width: 10%">Status</th>
                  </tr>
                </thead>
                <tbody>
                  {% for message in sent_messages %}
                    <tr>
                      <td>
                        <div class="d-flex align-items-center">
                          <div class="avatar-circle bg-secondary text-white me-2">
                            {{ message.recipient.get_full_name|default:message.recipient.username|slice:":1" }}
                          </div>
                          <span>{{ message.recipient.get_full_name|default:message.recipient.username }}</span>
                        </div>
                      </td>
                      <td>
                        <a href="{% url 'message_detail' message.id %}" class="text-decoration-none text-dark">
                          {{ message.subject|truncatechars:50 }}
                        </a>
                      </td>
                      <td>{{ message.created_at|date:"M d, Y" }}</td>
                      <td>
                        {% if message.is_read %}
                          <span class="badge bg-success">Read</span>
                        {% else %}
                          <span class="badge bg-warning text-dark">Unread</span>
                        {% endif %}
                      </td>
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          {% else %}
            <div class="text-center py-5">
              <div class="mb-3">
                <i class="fas fa-paper-plane fa-4x text-muted"></i>
              </div>
              <h3 class="h5 mb-3">No sent messages</h3>
              <p class="text-muted">You haven't sent any messages yet.</p>
              <a href="{% url 'message_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Compose Message
              </a>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Get the active tab from URL parameter
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');
    
    if (tab) {
      // Activate the tab
      const tabEl = document.querySelector(`#${tab}-tab`);
      if (tabEl) {
        const tabTrigger = new bootstrap.Tab(tabEl);
        tabTrigger.show();
      }
    }
    
    // Update URL when tab changes
    const tabs = document.querySelectorAll('button[data-bs-toggle="tab"]');
    tabs.forEach(tab => {
      tab.addEventListener('shown.bs.tab', function(event) {
        const id = event.target.id.replace('-tab', '');
        const url = new URL(window.location);
        url.searchParams.set('tab', id);
        window.history.replaceState({}, '', url);
      });
    });
  });
</script>
{% endblock %}
