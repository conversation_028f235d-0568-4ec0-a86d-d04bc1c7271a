{% extends 'base/base.html' %}

{% block title %}Add Testimonial - CompletoPLUS{% endblock %}

{% block content %}
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-primary text-white">
          <h1 class="h4 mb-0">Share Your Experience</h1>
        </div>
        <div class="card-body p-4">
          <p class="text-muted mb-4">
            Your feedback helps us improve our services and helps other clients make informed decisions.
            Please share your honest experience with CompletoPLUS.
          </p>

          <form method="post" class="testimonial-form">
            {% csrf_token %}

            <div class="mb-4">
              <label for="{{ form.service_type.id_for_label }}" class="form-label fw-medium">
                Which service did you use?
              </label>
              {{ form.service_type }}
              {% if form.service_type.help_text %}
                <div class="form-text">{{ form.service_type.help_text }}</div>
              {% endif %}
              {% if form.service_type.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.service_type.errors %}{{ error }}{% endfor %}
                </div>
              {% endif %}
            </div>

            <div class="mb-4">
              <label class="form-label fw-medium">
                How would you rate your experience?
              </label>
              <div class="rating-container">
                <div class="star-rating">
                  {% for choice in form.rating.field.choices %}
                  <div class="form-check form-check-inline">
                    <input class="form-check-input visually-hidden" type="radio" name="{{ form.rating.name }}"
                           id="rating_{{ choice.0 }}" value="{{ choice.0 }}"
                           {% if form.rating.value == choice.0 %}checked{% endif %}>
                    <label class="form-check-label rating-star" for="rating_{{ choice.0 }}">
                      <i class="fas fa-star"></i>
                      <span class="rating-text">{{ choice.1 }}</span>
                    </label>
                  </div>
                  {% endfor %}
                </div>
              </div>
              {% if form.rating.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.rating.errors %}{{ error }}{% endfor %}
                </div>
              {% endif %}
            </div>

            <div class="mb-4">
              <label for="{{ form.content.id_for_label }}" class="form-label fw-medium">
                Your Testimonial
              </label>
              {{ form.content }}
              {% if form.content.help_text %}
                <div class="form-text">{{ form.content.help_text }}</div>
              {% endif %}
              {% if form.content.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.content.errors %}{{ error }}{% endfor %}
                </div>
              {% endif %}
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
              <a href="{% url 'dashboard' %}" class="btn btn-outline-secondary">Cancel</a>
              <button type="submit" class="btn btn-primary">Submit Testimonial</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
  .rating-container {
    margin-bottom: 1rem;
  }

  .star-rating {
    display: flex;
    flex-direction: row;
    justify-content: center;
    max-width: 100%;
    gap: 0.5rem;
  }

  .rating-star {
    font-size: 2rem;
    color: #e0e0e0;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0.5rem;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .rating-star:hover {
    transform: scale(1.2);
  }

  .form-check-input:checked + .rating-star,
  .form-check-input:checked ~ .form-check-input + .rating-star {
    color: #ffc107;
  }

  .form-check-input:not(:checked) + .rating-star:hover,
  .form-check-input:not(:checked) + .rating-star:hover ~ .form-check-input + .rating-star {
    color: #ffdb70;
  }

  .rating-text {
    display: block;
    font-size: 0.75rem;
    text-align: center;
    margin-top: 0.5rem;
    font-weight: 500;
    color: #666;
    transition: all 0.3s ease;
  }

  .form-check-input:checked + .rating-star .rating-text {
    color: #333;
  }

  .form-check-inline {
    margin-right: 0;
    position: relative;
  }

  /* Testimonial card styling */
  .testimonial-card {
    transition: all 0.3s ease;
    border-radius: 12px;
    overflow: hidden;
  }

  .testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
  }

  .avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }
</style>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize star rating
    const stars = document.querySelectorAll('.rating-star');
    const ratingInputs = document.querySelectorAll('input[name="rating"]');

    // Add hover effect
    stars.forEach(star => {
      // Handle click
      star.addEventListener('click', function() {
        const input = document.getElementById(this.getAttribute('for'));
        input.checked = true;

        // Update all stars
        updateStars();
      });

      // Handle hover
      star.addEventListener('mouseenter', function() {
        const starValue = parseInt(this.getAttribute('for').split('_')[1]);

        stars.forEach((s, index) => {
          const sValue = parseInt(s.getAttribute('for').split('_')[1]);
          if (sValue <= starValue) {
            s.style.color = '#ffdb70'; // Lighter yellow on hover
          } else {
            s.style.color = '#e0e0e0'; // Default color
          }
        });
      });
    });

    // Handle mouse leave from rating container
    document.querySelector('.rating-container').addEventListener('mouseleave', function() {
      updateStars();
    });

    // Function to update stars based on selected rating
    function updateStars() {
      let selectedValue = 0;

      // Find selected rating
      ratingInputs.forEach(input => {
        if (input.checked) {
          selectedValue = parseInt(input.value);
        }
      });

      // Update star colors
      stars.forEach(star => {
        const starValue = parseInt(star.getAttribute('for').split('_')[1]);
        if (starValue <= selectedValue) {
          star.style.color = '#ffc107'; // Gold for selected
        } else {
          star.style.color = '#e0e0e0'; // Default color
        }
      });
    }

    // Initialize stars on page load
    updateStars();
  });
</script>
{% endblock %}
