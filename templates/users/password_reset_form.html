{% extends 'base/base.html' %}

{% block title %}Reset Password - CompletoPLUS{% endblock %}

{% block extra_css %}
<style>
  .password-reset-container {
    max-width: 450px;
    margin: 2rem auto;
  }

  .password-reset-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .password-reset-header {
    background: linear-gradient(
      135deg,
      var(--completoplus-blue),
      var(--completoplus-indigo)
    );
    padding: 2rem;
    text-align: center;
    color: white;
  }

  .password-reset-header h2 {
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .password-reset-header p {
    opacity: 0.9;
    margin-bottom: 0;
  }

  .password-reset-body {
    padding: 2rem;
  }

  .form-floating {
    margin-bottom: 1.5rem;
  }

  .form-floating > label {
    color: var(--gray-600);
  }

  .form-control {
    border-radius: 8px;
    padding: 0.75rem 1rem;
    height: calc(3.5rem + 2px);
    border: 1px solid var(--gray-300);
    transition: all 0.3s ease;
  }

  .form-control:focus {
    border-color: var(--completoplus-blue);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.15);
  }

  .reset-btn {
    background: linear-gradient(
      135deg,
      var(--completoplus-blue),
      var(--completoplus-indigo)
    );
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
  }

  .reset-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
  }

  .password-reset-footer {
    text-align: center;
    padding: 1.5rem;
    border-top: 1px solid var(--gray-200);
    background-color: var(--gray-50);
  }

  .password-reset-footer a {
    color: var(--completoplus-blue);
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
  }

  .password-reset-footer a:hover {
    color: var(--completoplus-indigo);
    text-decoration: underline;
  }

  /* Animated background */
  .animated-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
  }

  .animated-bg .shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(
      135deg,
      rgba(67, 97, 238, 0.1),
      rgba(58, 12, 163, 0.1)
    );
    animation: float 15s infinite ease-in-out;
  }

  .animated-bg .shape:nth-child(1) {
    width: 300px;
    height: 300px;
    top: -150px;
    right: -50px;
    animation-delay: 0s;
  }

  .animated-bg .shape:nth-child(2) {
    width: 200px;
    height: 200px;
    bottom: -100px;
    left: -50px;
    animation-delay: 3s;
  }

  .animated-bg .shape:nth-child(3) {
    width: 150px;
    height: 150px;
    bottom: 50%;
    right: -75px;
    animation-delay: 7s;
  }

  @keyframes float {
    0% {
      transform: translateY(0) rotate(0deg);
    }
    50% {
      transform: translateY(-20px) rotate(5deg);
    }
    100% {
      transform: translateY(0) rotate(0deg);
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="animated-bg">
  <div class="shape"></div>
  <div class="shape"></div>
  <div class="shape"></div>
</div>

<div class="container password-reset-container">
  <div class="password-reset-card">
    <div class="password-reset-header">
      <h2>Reset Password</h2>
      <p>Enter your email to receive a password reset link</p>
    </div>

    <div class="password-reset-body">
      <form method="post">
        {% csrf_token %}

        {% if form.email.errors %}
        <div class="alert alert-danger">
          {{ form.email.errors }}
        </div>
        {% endif %}

        <div class="form-floating mb-3">
          <input
            type="email"
            name="email"
            id="id_email"
            class="form-control"
            placeholder="Email"
            required
          />
          <label for="id_email">Email</label>
        </div>

        <div class="d-grid">
          <button type="submit" class="btn btn-primary reset-btn">
            <i class="fas fa-key me-2"></i>Reset Password
          </button>
        </div>
      </form>
    </div>

    <div class="password-reset-footer">
      <p class="mb-0">
        Remember your password? <a href="{% url 'login' %}">Sign in</a>
      </p>
    </div>
  </div>
</div>
{% endblock %}
