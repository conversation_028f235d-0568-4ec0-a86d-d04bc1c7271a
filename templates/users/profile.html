{% extends 'base/base.html' %}

{% block title %}Profile - CompletoPLUS{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <ul class="nav nav-tabs" id="profileTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="profile-tab" data-bs-toggle="tab" data-bs-target="#profile-pane" type="button" role="tab" aria-controls="profile-pane" aria-selected="true">
                        <i class="fas fa-user me-2"></i>Profile
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="dashboard-tab" data-bs-toggle="tab" data-bs-target="#dashboard-pane" type="button" role="tab" aria-controls="dashboard-pane" aria-selected="false">
                        <i class="fas fa-cog me-2"></i>Dashboard Settings
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="profileTabsContent">
                <div class="tab-pane fade show active" id="profile-pane" role="tabpanel" aria-labelledby="profile-tab">
                    <div class="card border-top-0 rounded-top-0">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0">User Profile</h4>
                        </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}

                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
                            <input type="text" name="{{ form.username.name }}" id="{{ form.username.id_for_label }}" value="{{ form.username.value }}" class="form-control {% if form.username.errors %}is-invalid{% endif %}" required>
                            {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">Email</label>
                            <input type="email" name="{{ form.email.name }}" id="{{ form.email.id_for_label }}" value="{{ form.email.value }}" class="form-control {% if form.email.errors %}is-invalid{% endif %}" required>
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.email.help_text %}
                                <small class="form-text text-muted">{{ form.email.help_text }}</small>
                            {% endif %}
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name</label>
                                <input type="text" name="{{ form.first_name.name }}" id="{{ form.first_name.id_for_label }}" value="{{ form.first_name.value }}" class="form-control {% if form.first_name.errors %}is-invalid{% endif %}">
                                {% if form.first_name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.first_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.first_name.help_text %}
                                    <small class="form-text text-muted">{{ form.first_name.help_text }}</small>
                                {% endif %}
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name</label>
                                <input type="text" name="{{ form.last_name.name }}" id="{{ form.last_name.id_for_label }}" value="{{ form.last_name.value }}" class="form-control {% if form.last_name.errors %}is-invalid{% endif %}">
                                {% if form.last_name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.last_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.last_name.help_text %}
                                    <small class="form-text text-muted">{{ form.last_name.help_text }}</small>
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Update Profile</button>
                        </div>
                    </form>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>Account Type:</strong>
                            {% if user.is_admin_user %}
                                <span class="badge bg-danger">Administrator</span>
                            {% else %}
                                <span class="badge bg-primary">Client</span>
                            {% endif %}
                        </div>
                        <div>
                            <strong>Member Since:</strong> {{ user.date_joined|date:"M d, Y" }}
                        </div>
                    </div>
                </div>
                </div>

                <!-- Dashboard Settings Tab -->
                <div class="tab-pane fade" id="dashboard-pane" role="tabpanel" aria-labelledby="dashboard-tab">
                    <div class="card border-top-0 rounded-top-0">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0">Dashboard Settings</h4>
                        </div>
                        <div class="card-body">
                            <form method="post" id="dashboard-settings-form">
                                {% csrf_token %}
                                <input type="hidden" name="dashboard_settings" value="true">

                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <h5><i class="fas fa-palette me-2"></i>Appearance</h5>
                                        <hr>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="{{ dashboard_form.layout.id_for_label }}" class="form-label">Layout</label>
                                        {{ dashboard_form.layout }}
                                        {% if dashboard_form.layout.help_text %}
                                            <small class="form-text text-muted">{{ dashboard_form.layout.help_text }}</small>
                                        {% endif %}
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="{{ dashboard_form.theme.id_for_label }}" class="form-label">Theme</label>
                                        {{ dashboard_form.theme }}
                                        {% if dashboard_form.theme.help_text %}
                                            <small class="form-text text-muted">{{ dashboard_form.theme.help_text }}</small>
                                        {% endif %}
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="{{ dashboard_form.color_scheme.id_for_label }}" class="form-label">Color Scheme</label>
                                        {{ dashboard_form.color_scheme }}
                                        {% if dashboard_form.color_scheme.help_text %}
                                            <small class="form-text text-muted">{{ dashboard_form.color_scheme.help_text }}</small>
                                        {% endif %}
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="{{ dashboard_form.items_per_page.id_for_label }}" class="form-label">Items Per Page</label>
                                        {{ dashboard_form.items_per_page }}
                                        {% if dashboard_form.items_per_page.help_text %}
                                            <small class="form-text text-muted">{{ dashboard_form.items_per_page.help_text }}</small>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <h5><i class="fas fa-th-large me-2"></i>Dashboard Widgets</h5>
                                        <hr>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            {{ dashboard_form.show_recent_files }}
                                            <label class="form-check-label" for="{{ dashboard_form.show_recent_files.id_for_label }}">
                                                Show Recent Files
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            {{ dashboard_form.show_notifications }}
                                            <label class="form-check-label" for="{{ dashboard_form.show_notifications.id_for_label }}">
                                                Show Notifications
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            {{ dashboard_form.show_projects }}
                                            <label class="form-check-label" for="{{ dashboard_form.show_projects.id_for_label }}">
                                                Show Projects
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            {{ dashboard_form.show_statistics }}
                                            <label class="form-check-label" for="{{ dashboard_form.show_statistics.id_for_label }}">
                                                Show Statistics
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            {{ dashboard_form.show_activity }}
                                            <label class="form-check-label" for="{{ dashboard_form.show_activity.id_for_label }}">
                                                Show Activity
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <h5><i class="fas fa-bell me-2"></i>Notification Settings</h5>
                                        <hr>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <div class="form-check form-switch">
                                            {{ dashboard_form.email_notifications }}
                                            <label class="form-check-label" for="{{ dashboard_form.email_notifications.id_for_label }}">
                                                Email Notifications
                                            </label>
                                        </div>
                                    </div>


                                </div>

                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save Dashboard Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle dashboard settings form submission via AJAX
        const dashboardForm = document.getElementById('dashboard-settings-form');

        if (dashboardForm) {
            // Handle form switches (checkboxes) via AJAX
            const switchInputs = dashboardForm.querySelectorAll('.form-check-input');
            switchInputs.forEach(input => {
                input.addEventListener('change', function() {
                    updateSetting(this.name, this.checked);
                });
            });

            // Handle select inputs via AJAX
            const selectInputs = dashboardForm.querySelectorAll('select');
            selectInputs.forEach(select => {
                select.addEventListener('change', function() {
                    updateSetting(this.name, this.value);
                });
            });

            // Function to update a single setting via AJAX
            function updateSetting(settingName, settingValue) {
                // Get CSRF token
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

                // Create form data
                const formData = new FormData();
                formData.append('setting_name', settingName);
                formData.append('setting_value', settingValue);

                // Send AJAX request
                fetch('{% url "save_dashboard_settings" %}', {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Show success message
                        const toast = document.createElement('div');
                        toast.className = 'toast align-items-center text-white bg-success border-0';
                        toast.setAttribute('role', 'alert');
                        toast.setAttribute('aria-live', 'assertive');
                        toast.setAttribute('aria-atomic', 'true');
                        toast.innerHTML = `
                            <div class="d-flex">
                                <div class="toast-body">
                                    <i class="fas fa-check-circle me-2"></i> Setting updated successfully
                                </div>
                                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                            </div>
                        `;

                        // Add toast to container or create one if it doesn't exist
                        let toastContainer = document.querySelector('.toast-container');
                        if (!toastContainer) {
                            toastContainer = document.createElement('div');
                            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
                            document.body.appendChild(toastContainer);
                        }

                        toastContainer.appendChild(toast);

                        // Initialize and show toast
                        const bsToast = new bootstrap.Toast(toast, {
                            autohide: true,
                            delay: 3000
                        });
                        bsToast.show();

                        // Apply theme changes immediately if applicable
                        if (settingName === 'theme') {
                            applyTheme(settingValue);
                        } else if (settingName === 'color_scheme') {
                            applyColorScheme(settingValue);
                        }
                    } else {
                        console.error('Error updating setting:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            }

            // Function to apply theme changes
            function applyTheme(theme) {
                const body = document.body;

                // Remove existing theme classes
                body.classList.remove('theme-light', 'theme-dark');

                // Add new theme class
                if (theme === 'light') {
                    body.classList.add('theme-light');
                    localStorage.setItem('theme', 'light');
                } else if (theme === 'dark') {
                    body.classList.add('theme-dark');
                    localStorage.setItem('theme', 'dark');
                } else if (theme === 'auto') {
                    // Use system preference
                    localStorage.removeItem('theme');
                    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                        body.classList.add('theme-dark');
                    } else {
                        body.classList.add('theme-light');
                    }
                }
            }

            // Function to apply color scheme changes
            function applyColorScheme(colorScheme) {
                const body = document.body;

                // Remove existing color scheme classes
                body.classList.remove('color-blue', 'color-green', 'color-purple', 'color-orange', 'color-red');

                // Add new color scheme class
                body.classList.add(`color-${colorScheme}`);
                localStorage.setItem('colorScheme', colorScheme);
            }
        }
    });
</script>
{% endblock %}