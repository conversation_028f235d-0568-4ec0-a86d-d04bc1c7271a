<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
  <!-- Main pages -->
  <url>
    <loc>{{ site_url }}/</loc>
    <lastmod>{% now "Y-m-d" %}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>{{ site_url }}/about/</loc>
    <lastmod>{% now "Y-m-d" %}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>{{ site_url }}/contact/</loc>
    <lastmod>{% now "Y-m-d" %}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>{{ site_url }}/services/</loc>
    <lastmod>{% now "Y-m-d" %}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>{{ site_url }}/data-analysis/</loc>
    <lastmod>{% now "Y-m-d" %}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>{{ site_url }}/pricing/</loc>
    <lastmod>{% now "Y-m-d" %}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>
  <url>
    <loc>{{ site_url }}/faq/</loc>
    <lastmod>{% now "Y-m-d" %}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>
  <url>
    <loc>{{ site_url }}/terms/</loc>
    <lastmod>{% now "Y-m-d" %}</lastmod>
    <changefreq>yearly</changefreq>
    <priority>0.5</priority>
  </url>
  <url>
    <loc>{{ site_url }}/privacy/</loc>
    <lastmod>{% now "Y-m-d" %}</lastmod>
    <changefreq>yearly</changefreq>
    <priority>0.5</priority>
  </url>
  <url>
    <loc>{{ site_url }}/password_reset/</loc>
    <lastmod>{% now "Y-m-d" %}</lastmod>
    <changefreq>yearly</changefreq>
    <priority>0.3</priority>
  </url>

  <!-- Authentication pages (lower priority) -->
  <url>
    <loc>{{ site_url }}/login/</loc>
    <lastmod>{% now "Y-m-d" %}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.4</priority>
  </url>
  <url>
    <loc>{{ site_url }}/register/</loc>
    <lastmod>{% now "Y-m-d" %}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.4</priority>
  </url>

  <!-- Dynamic pages (if available) -->
  {% if blog_posts %}
  {% for post in blog_posts %}
  <url>
    <loc>{{ site_url }}{{ post.get_absolute_url }}</loc>
    <lastmod>{{ post.updated_at|date:"Y-m-d" }}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>
  {% endfor %}
  {% endif %}
</urlset>