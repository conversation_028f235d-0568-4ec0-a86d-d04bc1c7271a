{% for file in files %}
<tr class="file-row" data-file-type="{{ file.file_type }}" data-upload-date="{{ file.uploaded_at|date:'Y-m-d' }}">
  <td>
    <div class="form-check">
      <input class="form-check-input file-checkbox" type="checkbox" name="file_ids" value="{{ file.id }}" form="batch-action-form" />
    </div>
  </td>
  <td>
    <div class="d-flex align-items-center">
      <div class="file-icon me-2">
        {% if file.is_zipped %}
        <i class="fas fa-file-archive text-warning"></i>
        {% elif file.is_pdf %}
        <i class="fas fa-file-pdf text-danger"></i>
        {% elif file.is_document %}
        <i class="fas fa-file-word text-primary"></i>
        {% elif file.is_image %}
        <i class="fas fa-file-image text-info"></i>
        {% else %}
        <i class="fas fa-file text-secondary"></i>
        {% endif %}
      </div>
      <div>
        <div class="fw-bold">{{ file.file_name }}</div>
        <div class="small text-muted">
          {{ file.original_file_name|default:file.file.name|truncatechars:40 }}
        </div>
      </div>
    </div>
  </td>
  <td>
    {% if file.notes %}
      {{ file.notes|truncatechars:50 }}
    {% else %}
      <span class="text-muted">No notes</span>
    {% endif %}
  </td>
  <td>
    <div>
      {{ file.owner.get_full_name|default:file.owner.username }}
      <div>
        {% if file.file_type == 'admin_upload' %}
        <span class="badge bg-primary">Admin</span>
        {% else %}
        <span class="badge bg-secondary">Client</span>
        {% endif %}
      </div>
    </div>
  </td>
  <td>
    <div>
      {{ file.uploaded_at|date:"M d, Y" }}
      <div class="small text-muted">
        {{ file.uploaded_at|time:"g:i A" }}
      </div>
    </div>
  </td>
  <td>{{ file.get_size_display }}</td>
  <td>
    <div class="btn-group file-actions">
      <a href="{% url 'file_download' file.id %}" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" data-bs-placement="top" title="Download">
        <i class="fas fa-download"></i>
      </a>
      {% if file.can_preview %}
      <a href="{% url 'file_preview' file.id %}" class="btn btn-sm btn-outline-primary" data-bs-toggle="tooltip" data-bs-placement="top" title="Preview" target="_blank">
        <i class="fas fa-eye"></i>
      </a>
      {% endif %}
      <a href="{% url 'file_delete' file.id %}" class="btn btn-sm btn-outline-danger" data-bs-toggle="tooltip" data-bs-placement="top" title="Delete" onclick="return confirm('Are you sure you want to delete this file?');">
        <i class="fas fa-trash-alt"></i>
      </a>
    </div>
  </td>
</tr>
{% endfor %}
