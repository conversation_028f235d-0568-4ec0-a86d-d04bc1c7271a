{% for notification in notifications %}
{% with notification_type=notification.notification_type %}
<div class="notification-item d-flex {% if not notification.is_read %}unread{% endif %} {{ notification_type }}">
  <!-- Notification Icon -->
  <div class="notification-icon {% if notification_type == 'file_upload' %}file-upload{% elif notification_type == 'status_change' %}status-change{% elif notification_type == 'progress_update' %}progress-update{% elif notification_type == 'comment' %}comment{% else %}default{% endif %}">
    {% if notification_type == 'file_upload' %}
    <i class="fas fa-file-upload"></i>
    {% elif notification_type == 'status_change' %}
    <i class="fas fa-sync-alt"></i>
    {% elif notification_type == 'progress_update' %}
    <i class="fas fa-tasks"></i>
    {% elif notification_type == 'comment' %}
    <i class="fas fa-comment"></i>
    {% else %}
    <i class="fas fa-bell"></i>
    {% endif %}
  </div>

  <!-- Notification Content -->
  <div class="notification-content">
    <div class="d-flex justify-content-between align-items-start mb-2">
      <div class="notification-title">
        {% if not notification.is_read %}
        <span class="notification-badge new">New</span>
        {% endif %}
        {{ notification.message }}
      </div>
      <div class="notification-time">
        <i class="far fa-clock"></i>
        {{ notification.created_at|date:"M d, Y H:i" }}
      </div>
    </div>

    <!-- Project Link (if project exists) -->
    {% if notification.project %}
    <a href="{% url 'project_detail' notification.project.id %}" class="notification-project">
      <i class="fas fa-project-diagram me-2"></i>
      {{ notification.project.name }}
    </a>
    {% elif notification_type == 'welcome' %}
    <span class="notification-project">
      <i class="fas fa-user-check me-2"></i>
      Welcome Notification
    </span>
    {% endif %}

    <!-- Notification Details -->
    {% if notification.data %}
    <div class="notification-details">
      {% if notification_type == 'file_upload' and notification.data.file_url %}
      <p class="mb-2">File: <strong>{{ notification.data.file_name }}</strong></p>
      {% elif notification_type == 'progress_update' and notification.data.note_text %}
      <p class="mb-2">{{ notification.data.note_text }}</p>
      {% elif notification_type == 'status_change' %}
      <p class="mb-2">Status changed from <span class="badge bg-secondary">{{ notification.data.old_status }}</span> to <span class="badge bg-info">{{ notification.data.new_status }}</span></p>
      {% elif notification_type == 'welcome' %}
      <p class="mb-2">Welcome to CompletoPLUS! We're excited to have you join us.</p>
      {% endif %}
    </div>
    {% endif %}

    <!-- Action Buttons -->
    <div class="notification-actions">
      {% if notification_type == 'file_upload' and notification.data.file_id %}
      <a href="{% url 'file_download' notification.data.file_id %}" class="btn btn-sm btn-outline-success">
        <i class="fas fa-download me-1"></i> Download File
      </a>
      {% endif %}
      
      {% if notification.project %}
      <a href="{% url 'project_detail' notification.project.id %}" class="btn btn-sm btn-primary">
        <i class="fas fa-eye me-1"></i> View Project
      </a>
      {% elif notification_type == 'welcome' %}
      <a href="{% url 'dashboard' %}" class="btn btn-sm btn-primary">
        <i class="fas fa-tachometer-alt me-1"></i> Go to Dashboard
      </a>
      {% endif %}
      
      {% if not notification.is_read %}
      <a href="{% url 'mark_notification_read' notification.id %}" class="btn btn-sm btn-outline-secondary mark-read-btn">
        <i class="fas fa-check me-1"></i> Mark as Read
      </a>
      {% endif %}
    </div>
  </div>
</div>
{% endwith %}
{% endfor %}
