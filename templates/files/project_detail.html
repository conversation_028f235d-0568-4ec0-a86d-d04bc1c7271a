{% extends 'base/base.html' %}
{% load static %}
{% block title %}{{ project.name }} - CompletoPLUS{% endblock %}

{% block meta_description %}View details and files for project {{ project.name }}. Track progress, communicate with experts, and manage your academic project efficiently with CompletoPLUS.{% endblock %}

{% block breadcrumb_schema %}
<script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://completoplus.com/"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Projects",
        "item": "https://completoplus.com/files/projects/"
      },
      {
        "@type": "ListItem",
        "position": 3,
        "name": "{{ project.name }}",
        "item": "{{ request.build_absolute_uri }}"
      }
    ]
  }
</script>
{% endblock %}
{% block extra_css %}
<style>
  /* Modern styling for the project detail page */
  .file-upload-area {
    border: 2px dashed #ddd;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    margin-bottom: 20px;
    transition: all 0.3s;
    cursor: pointer;
    background-color: rgba(114, 9, 183, 0.03);
  }

  .file-upload-area:hover {
    border-color: #7209b7;
    background-color: rgba(114, 9, 183, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  }

  /* Progress notes styling */
  .progress-notes-list {
    position: relative;
  }

  .progress-notes-list::before {
    content: "";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 20px;
    width: 2px;
    background-color: rgba(114, 9, 183, 0.2);
    z-index: 0;
  }

  .progress-note-item {
    position: relative;
    padding-left: 40px;
    margin-bottom: 25px;
  }

  .progress-note-item::before {
    content: "";
    position: absolute;
    left: 14px;
    top: 5px;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: #7209b7;
    z-index: 1;
  }

  .progress-note-header {
    margin-bottom: 10px;
  }

  .progress-note-date {
    font-size: 0.85rem;
    color: #6c757d;
  }

  .progress-note-author {
    font-size: 0.85rem;
    background-color: rgba(114, 9, 183, 0.1);
    color: #7209b7;
    padding: 3px 8px;
    border-radius: 20px;
  }

  .progress-note-content {
    background-color: #fff;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  /* Styling for note content sections */
  .note-text-content {
    margin-bottom: 15px;
  }

  .progress-update-section, .status-update-section {
    border-top: 1px dashed rgba(114, 9, 183, 0.2);
    padding-top: 12px;
  }

  .progress-update-badge, .status-update-badge {
    display: inline-block;
    font-size: 0.8rem;
    font-weight: 500;
    padding: 3px 8px;
    border-radius: 20px;
    margin-bottom: 8px;
  }

  .progress-update-badge {
    background-color: rgba(76, 201, 240, 0.15);
    color: #0e5e73;
  }

  .status-update-badge {
    background-color: rgba(6, 214, 160, 0.15);
    color: #00573f;
  }

  .progress-update-text, .status-update-text {
    padding-left: 10px;
    border-left: 3px solid rgba(76, 201, 240, 0.3);
    font-size: 0.95rem;
  }

  .status-update-text {
    border-left-color: rgba(6, 214, 160, 0.3);
  }

  /* Card styling */
  .card {
    border-radius: 10px;
    overflow: hidden;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
  }

  .card:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
  }

  /* Button styling */
  .btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
  }

  .btn-primary,
  .btn-outline-primary {
    border-color: #7209b7;
  }

  .btn-primary {
    background-color: #7209b7;
  }

  .btn-primary:hover {
    background-color: #6008a0;
    border-color: #6008a0;
  }

  .btn-outline-primary {
    color: #7209b7;
  }

  .btn-outline-primary:hover {
    background-color: #7209b7;
    color: white;
  }

  /* File list styling */
  .file-item {
    border-radius: 8px;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
  }

  .file-item:hover {
    background-color: rgba(114, 9, 183, 0.03);
    border-left-color: #7209b7;
  }

  .file-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background-color: rgba(114, 9, 183, 0.1);
    color: #7209b7;
  }

  .file-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }

  /* Status badge styling */
  .badge-status {
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 500;
    border-radius: 30px;
  }

  .badge-pending {
    background-color: #ffd166;
    color: #806000;
  }

  .badge-in-progress {
    background-color: #4cc9f0;
    color: #0e5e73;
  }

  .badge-completed {
    background-color: #06d6a0;
    color: #00573f;
  }

  .badge-cancelled {
    background-color: #ef476f;
    color: #7a1930;
  }

  /* Upload progress styling */
  .progress {
    height: 8px;
    border-radius: 4px;
    background-color: rgba(114, 9, 183, 0.1);
  }

  .progress-bar {
    background-color: #7209b7;
  }

  /* Checkbox styling */
  .form-check-input:checked {
    background-color: #7209b7;
    border-color: #7209b7;
  }

  /* File type icons */
  .file-type-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
  }

  .file-type-doc {
    background-color: rgba(77, 93, 255, 0.1);
    color: #4d5dff;
  }

  .file-type-image {
    background-color: rgba(239, 71, 111, 0.1);
    color: #ef476f;
  }

  .file-type-pdf {
    background-color: rgba(255, 99, 71, 0.1);
    color: #ff6347;
  }

  .file-type-zip {
    background-color: rgba(255, 209, 102, 0.1);
    color: #ffd166;
  }

  .file-type-other {
    background-color: rgba(114, 9, 183, 0.1);
    color: #7209b7;
  }

  /* Progress circle styling */
  .progress-circle {
    position: relative;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .progress-text {
    position: absolute;
    font-size: 1.5rem;
    font-weight: bold;
    color: #7209b7;
  }

  /* Status icons */
  .status-icon i {
    font-size: 2.5rem;
  }

  .files-icon {
    position: relative;
  }

  .files-icon i {
    font-size: 2.5rem;
  }

  .files-count {
    position: absolute;
    top: -10px;
    right: -10px;
    background-color: #7209b7;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: bold;
  }

  .timeline-icon i {
    font-size: 2.5rem;
  }

  /* Status badge colors */
  .bg-pending-subtle {
    background-color: rgba(255, 209, 102, 0.2);
  }

  .bg-in_progress-subtle {
    background-color: rgba(76, 201, 240, 0.2);
  }

  .bg-completed-subtle {
    background-color: rgba(6, 214, 160, 0.2);
  }

  .bg-cancelled-subtle {
    background-color: rgba(239, 71, 111, 0.2);
  }

  .text-pending {
    color: #ffd166;
  }

  .text-in_progress {
    color: #4cc9f0;
  }

  .text-completed {
    color: #06d6a0;
  }

  .text-cancelled {
    color: #ef476f;
  }

  /* Progress notes connector */
  .progress-note-connector {
    position: absolute;
    left: 20px;
    top: 100%;
    bottom: -25px;
    width: 2px;
    background-color: rgba(114, 9, 183, 0.2);
  }

  /* File upload styles */
  .file-upload-area {
    border: 2px dashed #7209b7;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    margin-bottom: 20px;
    transition: all 0.3s;
    cursor: pointer;
    background-color: rgba(114, 9, 183, 0.03);
  }

  .file-upload-area:hover {
    border-color: #7209b7;
    background-color: rgba(114, 9, 183, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(114, 9, 183, 0.1);
  }

  .file-upload-area.drag-highlight {
    border-color: #7209b7;
    background-color: rgba(114, 9, 183, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(114, 9, 183, 0.2);
  }

  .file-upload-area input[type="file"] {
    margin-bottom: 10px;
    opacity: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    cursor: pointer;
  }

  .file-upload-icon {
    font-size: 3rem;
    color: #7209b7;
    margin-bottom: 1rem;
  }

  .drag-text {
    font-size: 1.2rem;
    font-weight: 500;
    color: #333;
    margin-bottom: 0.5rem;
  }

  #file-list-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    margin-top: 1rem;
  }

  .file-item {
    transition: all 0.2s;
  }

  .file-item:hover {
    background-color: rgba(114, 9, 183, 0.05);
  }
</style>
{% endblock %}
{% block content %}
<div class="container py-5">
  <div class="row mb-4 align-items-center">
    <div class="col-md-8">
      <h1>{{ project.name }}</h1>
      <p class="text-muted">Project ID: {{ project.id }}</p>
    </div>
    <div class="col-md-4 text-end">
      <div class="btn-group">
        <a
          href="{% url 'project_update' project.pk %}"
          class="btn btn-outline-primary"
        >
          <i class="fas fa-edit me-2"></i>Edit Project
        </a>
        <a
          href="{% url 'project_delete' project.pk %}"
          class="btn btn-outline-danger"
        >
          <i class="fas fa-trash-alt me-2"></i>Delete Project
        </a>
        <button
          type="button"
          class="btn btn-outline-primary"
          id="upload-toggle-button"
        >
          <i class="fas fa-upload me-2"></i>Upload Files
        </button>
        {% if user.is_admin_user %}
        <button
          type="button"
          class="btn btn-outline-primary"
          data-bs-toggle="modal"
          data-bs-target="#addProgressNoteModal"
        >
          <i class="fas fa-clipboard-list me-2"></i>Add Update
        </button>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- File Upload Form -->
  <div class="row mb-4">
    <div class="col-12">
      <form
        method="post"
        enctype="multipart/form-data"
        id="upload-form"
        class="d-none"
        action="{% url 'file_upload' project.pk %}"
        data-project-id="{{ project.pk }}"
        onsubmit="return preventDuplicateSubmission(this);"
      >
        {% csrf_token %}
        <div class="card">
          <div class="card-header bg-white py-3">
            <h5 class="mb-0">Upload Files</h5>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-8">
                <div class="mb-3">
                  <label for="id_file_name" class="form-label"
                    >File Name (optional)</label
                  >
                  <input
                    type="text"
                    name="file_name"
                    id="id_file_name"
                    class="form-control"
                    placeholder="Leave blank to use original file name"
                  />
                  <div class="form-text">
                    This will be the name displayed in the system
                  </div>
                </div>
                <div class="mb-3">
                  <label for="id_notes" class="form-label"
                    >Notes (optional)</label
                  >
                  <textarea
                    name="notes"
                    id="id_notes"
                    class="form-control"
                    rows="3"
                    placeholder="Add any notes about this file"
                  ></textarea>
                </div>
              </div>
              <div class="col-md-4">
                <div
                  class="file-upload-area position-relative"
                  id="dropzone"
                  data-project-id="{{ project.pk }}"
                >
                  <input type="file" name="file" id="id_file" multiple />
                  <div class="text-center">
                    <i class="fas fa-cloud-upload-alt file-upload-icon"></i>
                    <h5 class="drag-text">Drag & Drop Files Here</h5>
                    <p class="text-muted">or</p>
                    <button
                      type="button"
                      class="btn btn-primary"
                      onclick="document.getElementById('id_file').click()"
                    >
                      <i class="fas fa-folder-open me-2"></i>Browse Files
                    </button>
                    <p class="text-muted mt-3 small">
                      All file types are supported. Multiple files will be
                      automatically zipped.
                      <br />Maximum size: 50MB per file, 100MB total.
                    </p>
                    <p class="text-muted mt-2 small">
                      <i class="fas fa-info-circle me-1"></i>
                      <strong>Tip:</strong> Hold Ctrl (or Cmd on Mac) while
                      clicking to select multiple files.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Upload progress -->
            <div id="upload-progress" class="mt-3 d-none">
              <div class="d-flex align-items-center mb-2">
                <div class="me-auto">Uploading...</div>
                <div id="progress-percentage">0%</div>
              </div>
              <div class="progress">
                <div
                  class="progress-bar progress-bar-striped progress-bar-animated"
                  role="progressbar"
                  style="width: 0%"
                  aria-valuenow="0"
                  aria-valuemin="0"
                  aria-valuemax="100"
                ></div>
              </div>
            </div>
          </div>
          <div class="card-footer bg-white py-3">
            <div class="d-flex justify-content-end">
              <button
                type="button"
                class="btn btn-outline-secondary me-2"
                id="cancel-upload"
              >
                Cancel
              </button>
              <button type="submit" class="btn btn-primary" id="upload-button">
                <i class="fas fa-upload me-2"></i>Upload File
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Project Dashboard -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-body p-0">
          <div class="row g-0">
            <!-- Project Progress -->
            <div class="col-md-3 border-end">
              <div class="p-4 text-center">
                <div
                  class="progress-circle mx-auto"
                  data-value="{{ project.progress }}"
                >
                  <span class="progress-text">{{ project.progress }}%</span>
                </div>
                <h5 class="mt-3 mb-0">Project Progress</h5>
                <p class="text-muted small mb-0">
                  Last updated: {{ project.updated_at|date:"M d, Y" }}
                </p>
              </div>
            </div>

            <!-- Project Status -->
            <div class="col-md-3 border-end">
              <div class="p-4 text-center">
                <div class="status-icon mx-auto mb-3">
                  {% if project.status == 'completed' %}
                  <i class="fas fa-check-circle text-success"></i>
                  {% elif project.status == 'in_progress' %}
                  <i class="fas fa-sync-alt text-primary"></i>
                  {% elif project.status == 'pending' %}
                  <i class="fas fa-clock text-warning"></i>
                  {% elif project.status == 'cancelled' %}
                  <i class="fas fa-times-circle text-danger"></i>
                  {% endif %}
                </div>
                <h5 class="mb-1">{{ project.get_status_display }}</h5>
                <span
                  class="badge bg-{{ project.status|lower }}-subtle text-{{ project.status|lower }}"
                >
                  {{ project.get_status_display }}
                </span>
              </div>
            </div>

            <!-- Files Count -->
            <div class="col-md-3 border-end">
              <div class="p-4 text-center">
                <div class="files-icon mx-auto mb-3">
                  <i class="fas fa-file-alt text-info"></i>
                  <span class="files-count">{{ files|length }}</span>
                </div>
                <h5 class="mb-1">Files</h5>
                <p class="text-muted small mb-0">Total files in this project</p>
              </div>
            </div>

            <!-- Project Timeline -->
            <div class="col-md-3">
              <div class="p-4 text-center">
                <div class="timeline-icon mx-auto mb-3">
                  <i class="fas fa-calendar-alt text-primary"></i>
                </div>
                <h5 class="mb-1">Timeline</h5>
                <p class="text-muted small mb-0">
                  Created: {{ project.created_at|date:"M d, Y" }}
                  {% if project.deadline %}
                  <br />Deadline: {{ project.deadline|date:"M d, Y" }}
                  {% endif %}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Project Details -->
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="card shadow-sm h-100">
        <div class="card-header bg-light py-3">
          <h5 class="mb-0">
            <i class="fas fa-info-circle me-2"></i>Project Information
          </h5>
        </div>
        <div class="card-body">
          <div class="row mb-3">
            <div class="col-md-4 text-muted">Project ID:</div>
            <div class="col-md-8">{{ project.id }}</div>
          </div>
          <div class="row mb-3">
            <div class="col-md-4 text-muted">Client:</div>
            <div class="col-md-8">
              <i class="fas fa-user me-1 text-primary"></i>
              {{ project.client.get_full_name }}
              <div class="small text-muted">{{ project.client.email }}</div>
            </div>
          </div>
          <div class="row mb-3">
            <div class="col-md-4 text-muted">Created:</div>
            <div class="col-md-8">
              <i class="fas fa-calendar me-1 text-primary"></i>
              {{ project.created_at|date:"F j, Y" }}
            </div>
          </div>
          <div class="row mb-3">
            <div class="col-md-4 text-muted">Last Update:</div>
            <div class="col-md-8">
              <i class="fas fa-clock me-1 text-primary"></i>
              {% if project.updated_at %} {{ project.updated_at|date:"F j, Y" }}
              {% else %} No updates yet {% endif %}
            </div>
          </div>
          {% if user.is_admin_user %}
          <div class="row mb-3">
            <div class="col-md-4 text-muted">Priority:</div>
            <div class="col-md-8">
              {% if project.priority == 'high' %}
              <span class="badge bg-danger">High Priority</span>
              {% elif project.priority == 'medium' %}
              <span class="badge bg-warning text-dark">Medium Priority</span>
              {% else %}
              <span class="badge bg-info">Normal Priority</span>
              {% endif %}
            </div>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card shadow-sm h-100">
        <div class="card-header bg-light py-3">
          <h5 class="mb-0">
            <i class="fas fa-align-left me-2"></i>Project Description
          </h5>
        </div>
        <div class="card-body">
          {% if project.description %}
          <p>{{ project.description|linebreaks }}</p>
          {% else %}
          <p class="text-muted">No description provided.</p>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Files Section -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-sm">
        <div
          class="card-header bg-light py-3 d-flex justify-content-between align-items-center"
        >
          <h5 class="mb-0">
            <i class="fas fa-file-alt me-2"></i>Project Files
          </h5>
          <div>
            <button
              class="btn btn-sm btn-outline-secondary me-2"
              id="file-view-toggle"
            >
              <i class="fas fa-th-large me-1"></i><span>Grid View</span>
            </button>

            <!-- Search and Filter Button -->
            <button
              class="btn btn-sm btn-outline-primary me-2"
              type="button"
              data-bs-toggle="collapse"
              data-bs-target="#fileSearchFilterCollapse"
              aria-expanded="false"
              aria-controls="fileSearchFilterCollapse"
            >
              <i class="fas fa-search me-1"></i>Search & Filter
              {% if search_query or current_file_type != 'all' or current_date != 'all' %}
              <span class="badge bg-primary ms-1">Active</span>
              {% endif %}
            </button>

            {% if files %}
            <div class="btn-group me-2">
              <button
                type="button"
                class="btn btn-sm btn-outline-primary"
                id="select-all-files"
              >
                <i class="fas fa-check-square me-1"></i>Select All
              </button>
              <button
                type="button"
                class="btn btn-sm btn-outline-primary"
                id="deselect-all-files"
              >
                <i class="fas fa-square me-1"></i>Deselect All
              </button>
            </div>
            <form id="batch-action-form" method="post" class="d-inline">
              {% csrf_token %}
              <input type="hidden" name="project_id" value="{{ project.pk }}" />
              <button
                type="button"
                class="btn btn-sm btn-outline-primary me-2"
                id="batch-download-btn"
                disabled
              >
                <i class="fas fa-download me-1"></i>Download Selected
              </button>
              <button
                type="button"
                class="btn btn-sm btn-outline-danger me-2"
                id="batch-delete-btn"
                disabled
              >
                <i class="fas fa-trash-alt me-1"></i>Delete Selected
              </button>
            </form>
            {% endif %}
            <button
              type="button"
              class="btn btn-sm btn-primary"
              id="upload-toggle-button-header"
            >
              <i class="fas fa-upload me-1"></i>Upload Files
            </button>
          </div>
        </div>
        <div class="card-body">
          <!-- Search and Filter Collapse -->
          <div class="collapse mb-4" id="fileSearchFilterCollapse">
            <div class="card card-body bg-light">
              <form id="file-filter-form" method="get" class="row g-3 align-items-end">
                <!-- Search Box -->
                <div class="col-md-4">
                  <label for="search" class="form-label">Search Files</label>
                  <div class="input-group">
                    <input type="text" class="form-control" id="search" name="search" placeholder="Search by name or notes..." value="{{ search_query }}">
                    <button class="btn btn-outline-primary" type="submit">
                      <i class="fas fa-search"></i>
                    </button>
                  </div>
                </div>

                <!-- File Type Filter -->
                <div class="col-md-3">
                  <label for="file_type" class="form-label">File Type</label>
                  <select class="form-select" id="file_type" name="file_type">
                    <option value="all" {% if current_file_type == 'all' %}selected{% endif %}>All Types</option>
                    <option value="admin_upload" {% if current_file_type == 'admin_upload' %}selected{% endif %}>Admin Uploads</option>
                    <option value="client_upload" {% if current_file_type == 'client_upload' %}selected{% endif %}>Client Uploads</option>
                  </select>
                </div>

                <!-- Date Filter -->
                <div class="col-md-3">
                  <label for="date" class="form-label">Date</label>
                  <select class="form-select" id="date" name="date">
                    <option value="all" {% if current_date == 'all' %}selected{% endif %}>All Time</option>
                    <option value="recent" {% if current_date == 'recent' %}selected{% endif %}>Recent (Last 7 Days)</option>
                  </select>
                </div>

                <!-- Items Per Page -->
                <div class="col-md-2">
                  <label for="page_size" class="form-label">Show</label>
                  <select class="form-select" id="page_size" name="page_size">
                    <option value="10" {% if page_size == 10 %}selected{% endif %}>10</option>
                    <option value="25" {% if page_size == 25 %}selected{% endif %}>25</option>
                    <option value="50" {% if page_size == 50 %}selected{% endif %}>50</option>
                    <option value="100" {% if page_size == 100 %}selected{% endif %}>100</option>
                  </select>
                </div>

                <!-- Action Buttons -->
                <div class="col-12 d-flex justify-content-between align-items-center mt-3">
                  <div>
                    <span class="badge bg-primary me-2">{{ total_files_count }} Total Files</span>
                  </div>
                  <div>
                    <button type="button" class="btn btn-sm btn-outline-secondary me-2" id="reset-file-filters">
                      <i class="fas fa-undo me-1"></i> Reset Filters
                    </button>
                    <button type="submit" class="btn btn-sm btn-primary">
                      <i class="fas fa-filter me-1"></i> Apply Filters
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>

          {% if files %}
          <!-- List View -->
          <div class="table-responsive" id="files-list-view">
            <table class="table table-hover align-middle">
              <thead class="table-light">
                <tr>
                  <th width="30">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        id="select-all-checkbox"
                      />
                    </div>
                  </th>
                  <th>File Name</th>
                  <th>Notes</th>
                  <th>Uploaded By</th>
                  <th>Date</th>
                  <th>File Size</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="file-items-container">
                {% include 'files/partials/file_items.html' %}
              </tbody>
            </table>

            <!-- Pagination -->
            {% if files.has_other_pages %}
            <div class="d-flex justify-content-between align-items-center flex-wrap mt-4">
              <!-- Pagination Info -->
              <div class="pagination-info text-muted small mb-3 mb-md-0">
                Showing {{ files.start_index }} to {{ files.end_index }} of {{ paginator.count }} files
              </div>

              <!-- Pagination Controls -->
              <nav aria-label="File pagination">
                <ul class="pagination pagination-sm mb-0">
                  {% if files.has_previous %}
                  <li class="page-item">
                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if current_file_type != 'all' %}&file_type={{ current_file_type }}{% endif %}{% if current_date != 'all' %}&date={{ current_date }}{% endif %}&page_size={{ page_size }}" aria-label="First">
                      <span aria-hidden="true">&laquo;&laquo;</span>
                    </a>
                  </li>
                  <li class="page-item">
                    <a class="page-link" href="?page={{ files.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_file_type != 'all' %}&file_type={{ current_file_type }}{% endif %}{% if current_date != 'all' %}&date={{ current_date }}{% endif %}&page_size={{ page_size }}" aria-label="Previous">
                      <span aria-hidden="true">&laquo;</span>
                    </a>
                  </li>
                  {% endif %}

                  {% for i in files.paginator.page_range %}
                    {% if files.number == i %}
                      <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                    {% elif i > files.number|add:'-3' and i < files.number|add:'3' %}
                      <li class="page-item">
                        <a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_file_type != 'all' %}&file_type={{ current_file_type }}{% endif %}{% if current_date != 'all' %}&date={{ current_date }}{% endif %}&page_size={{ page_size }}">{{ i }}</a>
                      </li>
                    {% endif %}
                  {% endfor %}

                  {% if files.has_next %}
                  <li class="page-item">
                    <a class="page-link" href="?page={{ files.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_file_type != 'all' %}&file_type={{ current_file_type }}{% endif %}{% if current_date != 'all' %}&date={{ current_date }}{% endif %}&page_size={{ page_size }}" aria-label="Next">
                      <span aria-hidden="true">&raquo;</span>
                    </a>
                  </li>
                  <li class="page-item">
                    <a class="page-link" href="?page={{ files.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_file_type != 'all' %}&file_type={{ current_file_type }}{% endif %}{% if current_date != 'all' %}&date={{ current_date }}{% endif %}&page_size={{ page_size }}" aria-label="Last">
                      <span aria-hidden="true">&raquo;&raquo;</span>
                    </a>
                  </li>
                  {% endif %}
                </ul>
              </nav>
            </div>

            <!-- Load More Button (Alternative to Pagination) -->
            <div class="text-center mt-4">
              <button type="button" class="btn btn-outline-primary d-none" id="load-more-files-btn" data-next-page="{{ files.next_page_number }}" data-has-next="{{ files.has_next|lower }}">
                <i class="fas fa-spinner me-2"></i> Load More Files
              </button>
            </div>
            {% endif %}
          <!-- Grid View (hidden by default) -->
          <div class="row g-3 d-none" id="files-grid-view">
            {% for file in files %}
            <div
              class="col-md-3 file-card"
              data-file-type="{{ file.file_type }}"
              data-upload-date="{{ file.uploaded_at|date:'Y-m-d' }}"
            >
              <div class="card h-100">
                <div class="card-body p-3">
                  <div class="form-check position-absolute top-0 end-0 m-2">
                    <input
                      class="form-check-input file-checkbox-grid"
                      type="checkbox"
                      value="{{ file.id }}"
                      form="batch-action-form"
                      name="file_ids"
                    />
                  </div>
                  <div class="text-center mb-3 pt-3">
                    {% if file.is_zipped %}
                    <i class="fas fa-file-archive fa-3x text-warning"></i>
                    {% elif file.is_pdf %}
                    <i class="fas fa-file-pdf fa-3x text-danger"></i>
                    {% elif file.is_document %}
                    <i class="fas fa-file-word fa-3x text-primary"></i>
                    {% elif file.is_image %}
                    <i class="fas fa-file-image fa-3x text-info"></i>
                    {% else %}
                    <i class="fas fa-file fa-3x text-secondary"></i>
                    {% endif %}
                  </div>
                  <h6
                    class="card-title text-center mb-1 text-truncate"
                    title="{{ file.file_name }}"
                  >
                    {{ file.file_name }}
                  </h6>
                  <p class="card-text small text-muted text-center mb-3">
                    {{ file.get_size_display }}
                  </p>
                  <div class="d-flex justify-content-center">
                    <a
                      href="{% url 'file_download' file.id %}"
                      class="btn btn-sm btn-outline-primary me-1"
                      data-bs-toggle="tooltip"
                      title="Download"
                    >
                      <i class="fas fa-download"></i>
                    </a>
                    {% if file.can_preview %}
                    <a
                      href="{% url 'file_preview' file.id %}"
                      class="btn btn-sm btn-outline-primary me-1"
                      data-bs-toggle="tooltip"
                      title="Preview"
                      target="_blank"
                    >
                      <i class="fas fa-eye"></i>
                    </a>
                    {% endif %}
                    <a
                      href="{% url 'file_delete' file.id %}"
                      class="btn btn-sm btn-outline-danger"
                      data-bs-toggle="tooltip"
                      title="Delete"
                    >
                      <i class="fas fa-trash-alt"></i>
                    </a>
                  </div>
                </div>
                <div class="card-footer bg-light p-2">
                  <div
                    class="d-flex justify-content-between align-items-center small"
                  >
                    <span>
                      {% if file.file_type == 'admin_upload' %}
                      <span class="badge bg-primary">Admin</span>
                      {% else %}
                      <span class="badge bg-secondary">Client</span>
                      {% endif %}
                    </span>
                    <span class="text-muted"
                      >{{ file.uploaded_at|date:"M d, Y" }}</span
                    >
                  </div>
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
          {% else %}
          <div class="text-center py-5">
            <div class="mb-3">
              <i class="fas fa-file-upload fa-4x text-muted"></i>
            </div>
            <h5>No files yet</h5>
            <p class="text-muted">
              Click the "Upload Files" button to add files to this project
            </p>
            <button
              type="button"
              class="btn btn-primary mt-2"
              id="empty-upload-button"
            >
              <i class="fas fa-upload me-2"></i>Upload Files
            </button>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Progress Notes Section -->
  <div class="row">
    <div class="col-12">
      <div class="card shadow-sm">
        <div
          class="card-header bg-light py-3 d-flex justify-content-between align-items-center"
        >
          <h5 class="mb-0">
            <i class="fas fa-clipboard-list me-2"></i>Project Updates
          </h5>
          {% if user.is_admin_user %}
          <button
            type="button"
            class="btn btn-sm btn-primary"
            data-bs-toggle="modal"
            data-bs-target="#addProgressNoteModal"
          >
            <i class="fas fa-plus me-1"></i>Add Update
          </button>
          {% endif %}
        </div>
        <div class="card-body">
          {% if progress_notes %}
          <div class="progress-notes-list">
            {% for note in progress_notes %}
            <div class="progress-note-item animate__animated animate__fadeIn mb-4 pb-4 border-bottom">
              <div class="progress-note-header d-flex justify-content-between mb-2">
                <span class="progress-note-date text-muted">
                  <i class="far fa-calendar-alt me-1"></i> {{ note.created_at|date:"M d, Y" }}
                  <i class="far fa-clock ms-2 me-1"></i> {{ note.created_at|time:"H:i" }}
                </span>
                <span class="progress-note-author badge bg-light text-dark">
                  <i class="far fa-user me-1"></i> {{ note.created_by.username }}
                </span>
              </div>
              <div class="progress-note-content">
                {{ note.note|linebreaks }}
              </div>
            </div>
            {% endfor %}
          </div>
          {% else %}
          <div class="text-center py-4 no-notes-message">
            <div class="mb-3">
              <i class="fas fa-clipboard-check text-muted" style="font-size: 3rem"></i>
            </div>
            <p class="text-muted">No progress updates yet.</p>
            {% if user.is_admin_user %}
            <button type="button" class="btn btn-sm btn-outline-primary mt-2" data-bs-toggle="modal" data-bs-target="#addProgressNoteModal">
              <i class="fas fa-plus me-1"></i> Add First Update
            </button>
            {% endif %}
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Add Progress Note Modal -->
<div
  class="modal fade"
  id="addProgressNoteModal"
  tabindex="-1"
  aria-labelledby="addProgressNoteModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <form
        method="post"
        action="{% url 'add_progress_note' project.pk %}"
        id="progress-note-form"
      >
        {% csrf_token %}
        <div class="modal-header">
          <h5 class="modal-title" id="addProgressNoteModalLabel">
            Add Project Update
          </h5>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
          ></button>
        </div>
        <div class="modal-body">
          <div class="mb-3">
            <label for="id_note" class="form-label">Update Content</label>
            <textarea
              name="note"
              id="id_note"
              class="form-control"
              rows="5"
              required
              placeholder="Enter project update details..."
            ></textarea>
          </div>
          <div class="mb-3">
            <label for="id_progress" class="form-label"
              >Project Progress ({{ project.progress }}%)</label
            >
            <input
              type="range"
              class="form-range"
              min="0"
              max="100"
              step="5"
              id="id_progress"
              name="progress"
              value="{{ project.progress }}"
            />
            <div class="d-flex justify-content-between">
              <span class="small text-muted">0%</span>
              <span class="small text-muted" id="progress-value"
                >{{ project.progress }}%</span
              >
              <span class="small text-muted">100%</span>
            </div>
          </div>
          <div class="mb-3">
            <label for="id_status" class="form-label">Project Status</label>
            <select name="status" id="id_status" class="form-select">
              <option value="pending" {% if project.status == 'pending' %}selected{% endif %}>
                Pending
              </option>
              <option value="in_progress" {% if project.status == 'in_progress' %}selected{% endif %}>
                In Progress
              </option>
              <option value="completed" {% if project.status == 'completed' %}selected{% endif %}>
                Completed
              </option>
              <option value="cancelled" {% if project.status == 'cancelled' %}selected{% endif %}>
                Cancelled
              </option>
            </select>
          </div>
        </div>
        <div class="modal-footer">
          <button
            type="button"
            class="btn btn-outline-secondary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>Add Update
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div
  class="modal fade"
  id="deleteConfirmationModal"
  tabindex="-1"
  aria-labelledby="deleteConfirmationModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteConfirmationModalLabel">
          Confirm Delete
        </h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
          aria-label="Close"
        ></button>
      </div>
      <div class="modal-body">
        <p>Are you sure you want to delete the selected files?</p>
        <p class="text-danger">
          <i class="fas fa-exclamation-triangle me-1"></i>This action cannot be
          undone.
        </p>
      </div>
      <div class="modal-footer">
        <button
          type="button"
          class="btn btn-outline-secondary"
          data-bs-dismiss="modal"
        >
          Cancel
        </button>
        <button
          type="button"
          class="btn btn-danger"
          id="confirm-batch-delete"
        >
          <i class="fas fa-trash-alt me-1"></i>Delete Files
        </button>
      </div>
    </div>
  </div>
</div>

{% endblock %}
{% block extra_js %}
<script>
  // Function to prevent duplicate form submissions
  let formSubmitted = false;
  function preventDuplicateSubmission(form) {
    if (formSubmitted) {
      console.log('Form already submitted, preventing duplicate submission');
      return false;
    }
    formSubmitted = true;

    // Reset the flag after 5 seconds to allow future submissions
    setTimeout(() => {
      formSubmitted = false;
    }, 5000);

    return true;
  }
  document.addEventListener("DOMContentLoaded", function () {
    // Progress circle animation
    const progressCircle = document.querySelector(".progress-circle");
    if (progressCircle) {
      const progressValue = progressCircle.getAttribute("data-value");
      const radius = 54;
      const circumference = 2 * Math.PI * radius;

      // Create SVG
      const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
      svg.setAttribute("width", "120");
      svg.setAttribute("height", "120");
      svg.setAttribute("viewBox", "0 0 120 120");
      svg.classList.add("position-absolute", "top-0", "left-0");

      // Background circle
      const bgCircle = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "circle"
      );
      bgCircle.setAttribute("cx", "60");
      bgCircle.setAttribute("cy", "60");
      bgCircle.setAttribute("r", radius);
      bgCircle.setAttribute("fill", "none");
      bgCircle.setAttribute("stroke", "rgba(114, 9, 183, 0.1)");
      bgCircle.setAttribute("stroke-width", "8");
      svg.appendChild(bgCircle);

      // Progress circle
      const circle = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "circle"
      );
      circle.setAttribute("cx", "60");
      circle.setAttribute("cy", "60");
      circle.setAttribute("r", radius);
      circle.setAttribute("fill", "none");
      circle.setAttribute("stroke", "#7209b7");
      circle.setAttribute("stroke-width", "8");
      circle.setAttribute("stroke-linecap", "round");
      circle.setAttribute(
        "stroke-dasharray",
        `${circumference} ${circumference}`
      );
      circle.setAttribute(
        "stroke-dashoffset",
        circumference - (progressValue / 100) * circumference
      );
      circle.setAttribute("transform", "rotate(-90 60 60)");
      circle.style.transition = "stroke-dashoffset 1s ease-in-out";
      svg.appendChild(circle);

      progressCircle.appendChild(svg);
    }

    // File upload form toggle
    const uploadToggleButton = document.getElementById("upload-toggle-button");
    const uploadToggleButtonHeader = document.getElementById(
      "upload-toggle-button-header"
    );
    const emptyUploadButton = document.getElementById("empty-upload-button");
    const uploadForm = document.getElementById("upload-form");
    const cancelUpload = document.getElementById("cancel-upload");

    function toggleUploadForm() {
      uploadForm.classList.toggle("d-none");
      window.scrollTo({
        top: uploadForm.offsetTop - 100,
        behavior: "smooth",
      });
    }

    if (uploadToggleButton) {
      uploadToggleButton.addEventListener("click", toggleUploadForm);
    }

    if (uploadToggleButtonHeader) {
      uploadToggleButtonHeader.addEventListener("click", toggleUploadForm);
    }

    if (emptyUploadButton) {
      emptyUploadButton.addEventListener("click", toggleUploadForm);
    }

    if (cancelUpload) {
      cancelUpload.addEventListener("click", function () {
        uploadForm.classList.add("d-none");
      });
    }

    // File view toggle (list/grid)
    const fileViewToggle = document.getElementById("file-view-toggle");
    const filesListView = document.getElementById("files-list-view");
    const filesGridView = document.getElementById("files-grid-view");

    if (fileViewToggle && filesListView && filesGridView) {
      fileViewToggle.addEventListener("click", function () {
        filesListView.classList.toggle("d-none");
        filesGridView.classList.toggle("d-none");

        const toggleText = fileViewToggle.querySelector("span");
        const toggleIcon = fileViewToggle.querySelector("i");

        if (filesListView.classList.contains("d-none")) {
          toggleText.textContent = "List View";
          toggleIcon.classList.remove("fa-th-large");
          toggleIcon.classList.add("fa-list");
        } else {
          toggleText.textContent = "Grid View";
          toggleIcon.classList.remove("fa-list");
          toggleIcon.classList.add("fa-th-large");
        }
      });
    }

    // File filtering
    const filterButtons = document.querySelectorAll(".filter-file");
    if (filterButtons.length > 0) {
      filterButtons.forEach((button) => {
        button.addEventListener("click", function (e) {
          e.preventDefault();
          const filter = this.getAttribute("data-filter");
          const fileRows = document.querySelectorAll(".file-row");
          const fileCards = document.querySelectorAll(".file-card");

          // Update active state
          filterButtons.forEach((btn) => {
            btn.classList.remove("active");
          });
          this.classList.add("active");

          // Filter list view
          fileRows.forEach((row) => {
            if (filter === "all") {
              row.style.display = "";
            } else if (filter === "recent") {
              const uploadDate = new Date(
                row.getAttribute("data-upload-date")
              );
              const today = new Date();
              const diffTime = Math.abs(today - uploadDate);
              const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
              row.style.display = diffDays <= 7 ? "" : "none";
            } else {
              row.style.display =
                row.getAttribute("data-file-type") === filter ? "" : "none";
            }
          });

          // Filter grid view
          fileCards.forEach((card) => {
            if (filter === "all") {
              card.style.display = "";
            } else if (filter === "recent") {
              const uploadDate = new Date(
                card.getAttribute("data-upload-date")
              );
              const today = new Date();
              const diffTime = Math.abs(today - uploadDate);
              const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
              card.style.display = diffDays <= 7 ? "" : "none";
            } else {
              card.style.display =
                card.getAttribute("data-file-type") === filter ? "" : "none";
            }
          });
        });
      });
    }

    // Select all files
    const selectAllCheckbox = document.getElementById("select-all-checkbox");
    const fileCheckboxes = document.querySelectorAll(".file-checkbox");
    const fileCheckboxesGrid = document.querySelectorAll(".file-checkbox-grid");
    const selectAllBtn = document.getElementById("select-all-files");
    const deselectAllBtn = document.getElementById("deselect-all-files");
    const batchDownloadBtn = document.getElementById("batch-download-btn");
    const batchDeleteBtn = document.getElementById("batch-delete-btn");
    const batchActionForm = document.getElementById("batch-action-form");
    const confirmBatchDelete = document.getElementById("confirm-batch-delete");

    function updateBatchButtons() {
      const checkedCount = document.querySelectorAll(
        ".file-checkbox:checked, .file-checkbox-grid:checked"
      ).length;
      batchDownloadBtn.disabled = checkedCount === 0;
      batchDeleteBtn.disabled = checkedCount === 0;
    }

    if (selectAllCheckbox) {
      selectAllCheckbox.addEventListener("change", function () {
        fileCheckboxes.forEach((checkbox) => {
          checkbox.checked = selectAllCheckbox.checked;
        });
        fileCheckboxesGrid.forEach((checkbox) => {
          checkbox.checked = selectAllCheckbox.checked;
        });
        updateBatchButtons();
      });
    }

    if (fileCheckboxes.length > 0) {
      fileCheckboxes.forEach((checkbox) => {
        checkbox.addEventListener("change", function () {
          updateBatchButtons();
          // Update select all checkbox state
          selectAllCheckbox.checked =
            fileCheckboxes.length ===
            document.querySelectorAll(".file-checkbox:checked").length;
        });
      });
    }

    if (fileCheckboxesGrid.length > 0) {
      fileCheckboxesGrid.forEach((checkbox) => {
        checkbox.addEventListener("change", function () {
          updateBatchButtons();
          // Sync with list view checkboxes
          const fileId = checkbox.value;
          const listCheckbox = document.querySelector(
            `.file-checkbox[value="${fileId}"]`
          );
          if (listCheckbox) {
            listCheckbox.checked = checkbox.checked;
          }
        });
      });
    }

    if (selectAllBtn) {
      selectAllBtn.addEventListener("click", function () {
        fileCheckboxes.forEach((checkbox) => {
          checkbox.checked = true;
        });
        fileCheckboxesGrid.forEach((checkbox) => {
          checkbox.checked = true;
        });
        selectAllCheckbox.checked = true;
        updateBatchButtons();
      });
    }

    if (deselectAllBtn) {
      deselectAllBtn.addEventListener("click", function () {
        fileCheckboxes.forEach((checkbox) => {
          checkbox.checked = false;
        });
        fileCheckboxesGrid.forEach((checkbox) => {
          checkbox.checked = false;
        });
        selectAllCheckbox.checked = false;
        updateBatchButtons();
      });
    }

    // Batch download
    if (batchDownloadBtn) {
      batchDownloadBtn.addEventListener("click", function () {
        batchActionForm.action = "{% url 'batch_download' %}";
        batchActionForm.submit();
      });
    }

    // Batch delete
    if (batchDeleteBtn) {
      batchDeleteBtn.addEventListener("click", function () {
        const modal = new bootstrap.Modal(
          document.getElementById("deleteConfirmationModal")
        );
        modal.show();
      });
    }

    if (confirmBatchDelete) {
      confirmBatchDelete.addEventListener("click", function () {
        batchActionForm.action = "{% url 'batch_delete' %}";
        batchActionForm.submit();
      });
    }

    // Progress slider
    const progressSlider = document.getElementById("id_progress");
    const progressValueDisplay = document.getElementById("progress-value");

    if (progressSlider && progressValueDisplay) {
      progressSlider.addEventListener("input", function () {
        progressValueDisplay.textContent = `${progressSlider.value}%`;
      });
    }

    // File upload drag and drop
    const dropzone = document.getElementById("dropzone");
    const fileInput = document.getElementById("id_file");
    const uploadButton = document.getElementById("upload-button");
    const uploadProgress = document.getElementById("upload-progress");
    const progressBar = document.querySelector(".progress-bar");
    const progressPercentage = document.getElementById("progress-percentage");

    if (dropzone && fileInput) {
      // Prevent default drag behaviors
      ["dragenter", "dragover", "dragleave", "drop"].forEach((eventName) => {
        dropzone.addEventListener(
          eventName,
          (e) => {
            e.preventDefault();
            e.stopPropagation();
          },
          false
        );
      });

      // Highlight drop area when item is dragged over it
      ["dragenter", "dragover"].forEach((eventName) => {
        dropzone.addEventListener(
          eventName,
          () => {
            dropzone.classList.add("drag-highlight");
          },
          false
        );
      });

      ["dragleave", "drop"].forEach((eventName) => {
        dropzone.addEventListener(
          eventName,
          () => {
            dropzone.classList.remove("drag-highlight");
          },
          false
        );
      });

      // Handle dropped files
      dropzone.addEventListener(
        "drop",
        (e) => {
          fileInput.files = e.dataTransfer.files;
          // Update file name display
          updateFileList(fileInput.files);
        },
        false
      );

      // Handle selected files
      fileInput.addEventListener("change", () => {
        updateFileList(fileInput.files);
      });

      function updateFileList(files) {
        if (files.length > 0) {
          // Update upload button text
          uploadButton.innerHTML = `<i class="fas fa-upload me-2"></i>Upload ${
            files.length > 1 ? files.length + " Files" : "File"
          }`;
        }
      }

      // Handle file upload with progress
      if (uploadButton && uploadProgress) {
        const uploadForm = document.getElementById("upload-form");
        uploadForm.addEventListener("submit", function (e) {
          e.preventDefault();

          const formData = new FormData(this);
          const xhr = new XMLHttpRequest();

          // Show progress bar
          uploadProgress.classList.remove("d-none");
          uploadButton.disabled = true;

          xhr.upload.addEventListener("progress", function (event) {
            if (event.lengthComputable) {
              const percentComplete = Math.round(
                (event.loaded / event.total) * 100
              );
              progressBar.style.width = percentComplete + "%";
              progressBar.setAttribute("aria-valuenow", percentComplete);
              progressPercentage.textContent = percentComplete + "%";
            }
          });

          xhr.addEventListener("load", function () {
            if (xhr.status === 200) {
              // Redirect to the same page to show the uploaded file
              window.location.reload();
            } else {
              alert("Upload failed. Please try again.");
              uploadButton.disabled = false;
              uploadProgress.classList.add("d-none");
            }
          });

          xhr.addEventListener("error", function () {
            alert("Upload failed. Please try again.");
            uploadButton.disabled = false;
            uploadProgress.classList.add("d-none");
          });

          xhr.open("POST", uploadForm.action, true);
          xhr.send(formData);
        });
      }
    }

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(
      document.querySelectorAll('[data-bs-toggle="tooltip"]')
    );
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Handle file filtering and pagination
    const fileFilterForm = document.getElementById('file-filter-form');
    const resetFileFiltersBtn = document.getElementById('reset-file-filters');
    const loadMoreFilesBtn = document.getElementById('load-more-files-btn');

    // Auto-submit form when select fields change
    if (fileFilterForm) {
      const selectFields = fileFilterForm.querySelectorAll('select');
      selectFields.forEach(select => {
        select.addEventListener('change', function() {
          fileFilterForm.submit();
        });
      });

      // Reset filters
      if (resetFileFiltersBtn) {
        resetFileFiltersBtn.addEventListener('click', function() {
          window.location.href = window.location.pathname;
        });
      }
    }

    // Load More Files functionality
    if (loadMoreFilesBtn) {
      loadMoreFilesBtn.addEventListener('click', function() {
        const nextPage = this.getAttribute('data-next-page');
        const hasNext = this.getAttribute('data-has-next') === 'true';

        if (!hasNext) return;

        // Show loading state
        this.classList.add('loading');
        this.innerHTML = '<i class="fas fa-spinner me-2"></i> Loading...';

        // Build URL with current filters
        let url = `?page=${nextPage}`;
        const searchParam = new URLSearchParams(window.location.search).get('search');
        const fileTypeParam = new URLSearchParams(window.location.search).get('file_type');
        const dateParam = new URLSearchParams(window.location.search).get('date');
        const pageSizeParam = new URLSearchParams(window.location.search).get('page_size');

        if (searchParam) url += `&search=${searchParam}`;
        if (fileTypeParam && fileTypeParam !== 'all') url += `&file_type=${fileTypeParam}`;
        if (dateParam && dateParam !== 'all') url += `&date=${dateParam}`;
        if (pageSizeParam) url += `&page_size=${pageSizeParam}`;

        // Add AJAX header
        url += '&format=json';

        // Fetch next page
        fetch(url, {
          headers: {
            'X-Requested-With': 'XMLHttpRequest'
          }
        })
        .then(response => response.json())
        .then(data => {
          // Append new items
          const container = document.getElementById('file-items-container');
          container.insertAdjacentHTML('beforeend', data.html);

          // Update button state
          if (data.has_next) {
            this.setAttribute('data-next-page', data.next_page);
          } else {
            this.setAttribute('data-has-next', 'false');
            this.classList.add('disabled');
            this.innerHTML = '<i class="fas fa-check-circle me-2"></i> All Files Loaded';
          }

          // Remove loading state if there are more pages
          if (data.has_next) {
            this.classList.remove('loading');
            this.innerHTML = '<i class="fas fa-spinner me-2"></i> Load More Files';
          }

          // Reinitialize tooltips for new items
          const newTooltips = [].slice.call(
            document.querySelectorAll('[data-bs-toggle="tooltip"]:not(.tooltip-initialized)')
          );
          newTooltips.forEach(el => {
            el.classList.add('tooltip-initialized');
            new bootstrap.Tooltip(el);
          });

          // Update select all checkbox functionality for new items
          updateSelectAllCheckboxes();
        })
        .catch(error => {
          console.error('Error:', error);
          this.classList.remove('loading');
          this.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i> Error Loading';
        });
      });
    }

    // Toggle between pagination and load more view
    const togglePaginationBtn = document.getElementById('toggle-pagination-btn');
    if (togglePaginationBtn) {
      togglePaginationBtn.addEventListener('click', function() {
        const paginationContainer = document.querySelector('.pagination');
        if (paginationContainer) {
          paginationContainer.parentElement.classList.toggle('d-none');
        }

        if (loadMoreFilesBtn) {
          loadMoreFilesBtn.classList.toggle('d-none');
        }

        // Update button text
        if (this.textContent.includes('Load More')) {
          this.innerHTML = '<i class="fas fa-th-list me-1"></i> Pagination View';
        } else {
          this.innerHTML = '<i class="fas fa-spinner me-1"></i> Load More View';
        }
      });
    }
  });
</script>
{% endblock %}
