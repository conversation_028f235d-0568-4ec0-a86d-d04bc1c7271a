{% extends 'base/base.html' %}

{% block title %}{{ action }} Project - File Sharing Platform{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">{{ action }} Project</h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">Project Name</label>
                            <input type="text" name="{{ form.name.name }}" id="{{ form.name.id_for_label }}" value="{{ form.name.value|default:'' }}" class="form-control {% if form.name.errors %}is-invalid{% endif %}" required>
                            {% if form.name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.name.help_text %}
                                <small class="form-text text-muted">{{ form.name.help_text }}</small>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                            <textarea name="{{ form.description.name }}" id="{{ form.description.id_for_label }}" class="form-control {% if form.description.errors %}is-invalid{% endif %}" rows="4">{{ form.description.value|default:'' }}</textarea>
                            {% if form.description.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.description.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.description.help_text %}
                                <small class="form-text text-muted">{{ form.description.help_text }}</small>
                            {% endif %}
                        </div>

                        {% if 'client' in form.fields %}
                            <div class="mb-3">
                                <label for="{{ form.client.id_for_label }}" class="form-label">Client</label>
                                {{ form.client }}
                                {% if form.client.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.client.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.client.help_text %}
                                    <small class="form-text text-muted">{{ form.client.help_text }}</small>
                                {% endif %}
                            </div>
                        {% endif %}

                        {% if 'status' in form.fields %}
                            <div class="mb-3">
                                <label for="{{ form.status.id_for_label }}" class="form-label">Status</label>
                                {{ form.status }}
                                {% if form.status.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.status.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.status.help_text %}
                                    <small class="form-text text-muted">{{ form.status.help_text }}</small>
                                {% endif %}
                            </div>
                        {% endif %}

                        <!-- Progress is now automatically calculated based on status -->

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">{{ action }} Project</button>
                            <a href="{% if project %}{% url 'project_detail' project.id %}{% else %}{% url 'project_list' %}{% endif %}" class="btn btn-outline-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
