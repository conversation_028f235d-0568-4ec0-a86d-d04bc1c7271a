{% extends 'base/base.html' %}

{% block title %}Notifications - CompletoPLUS{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
  /* Modern Notification Styles */
  .notification-page-header {
    background: var(--completoplus-gradient);
    border-radius: var(--border-radius-md);
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    box-shadow: var(--box-shadow-md);
  }

  .notification-card {
    border-radius: var(--border-radius-md);
    border: none;
    overflow: hidden;
    box-shadow: var(--box-shadow-md);
    transition: var(--transition-base);
  }

  .notification-item {
    border-left: 4px solid transparent;
    border-radius: var(--border-radius-sm);
    margin-bottom: 1rem;
    padding: 1.25rem;
    transition: all 0.3s ease;
    background-color: var(--gray-100);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  }

  .notification-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  }

  .notification-item.unread {
    border-left-color: var(--completoplus-blue);
    background-color: rgba(67, 97, 238, 0.05);
  }

  .notification-item.file-upload {
    border-left-color: var(--success-color);
  }

  .notification-item.status-change {
    border-left-color: var(--completoplus-cyan);
  }

  .notification-item.progress-update {
    border-left-color: var(--warning-color);
  }

  .notification-item.comment {
    border-left-color: var(--completoplus-purple);
  }

  .notification-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    margin-right: 1rem;
    flex-shrink: 0;
  }

  .notification-icon.file-upload {
    background-color: rgba(6, 214, 160, 0.1);
    color: var(--success-color);
  }

  .notification-icon.status-change {
    background-color: rgba(76, 201, 240, 0.1);
    color: var(--completoplus-cyan);
  }

  .notification-icon.progress-update {
    background-color: rgba(255, 209, 102, 0.1);
    color: #e6b800;
  }

  .notification-icon.comment {
    background-color: rgba(114, 9, 183, 0.1);
    color: var(--completoplus-purple);
  }

  .notification-icon.default {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--completoplus-blue);
  }

  .notification-content {
    flex: 1;
  }

  .notification-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1rem;
    display: flex;
    align-items: center;
  }

  .notification-project {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.8rem;
    background-color: rgba(0,0,0,0.05);
    color: var(--gray-700);
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
  }

  .notification-project:hover {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--completoplus-blue);
    text-decoration: none;
  }

  .notification-time {
    font-size: 0.8rem;
    color: var(--gray-600);
    display: flex;
    align-items: center;
  }

  .notification-time i {
    margin-right: 0.25rem;
  }

  .notification-details {
    background-color: rgba(0,0,0,0.02);
    border-radius: var(--border-radius-sm);
    padding: 0.75rem;
    margin-top: 0.75rem;
    margin-bottom: 0.75rem;
    border-left: 3px solid rgba(0,0,0,0.05);
  }

  .notification-actions {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
  }

  .notification-actions .btn {
    border-radius: 50px;
    padding: 0.4rem 1rem;
    font-size: 0.85rem;
  }

  .notification-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    font-size: 0.7rem;
    font-weight: 600;
    margin-right: 0.5rem;
  }

  .notification-badge.new {
    background-color: rgba(67, 97, 238, 0.1);
    color: var(--completoplus-blue);
  }

  .notification-empty {
    text-align: center;
    padding: 3rem 1rem;
  }

  .notification-empty-icon {
    font-size: 4rem;
    color: var(--gray-300);
    margin-bottom: 1rem;
  }

  .notification-empty-text {
    color: var(--gray-600);
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
  }

  .mark-all-btn {
    border-radius: 50px;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background-color: white;
    color: var(--completoplus-blue);
    border: none;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
  }

  .mark-all-btn:hover {
    background-color: white;
    color: var(--completoplus-indigo);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transform: translateY(-2px);
  }

  /* Additional styles for pagination and filtering */
  .filter-badge {
    display: inline-flex;
    align-items: center;
    background-color: var(--gray-200);
    color: var(--gray-700);
    border-radius: 50px;
    padding: 0.35rem 0.75rem;
    font-size: 0.8rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
  }

  .filter-badge:hover {
    background-color: var(--gray-300);
  }

  .filter-badge .close {
    margin-left: 0.5rem;
    font-size: 0.9rem;
    cursor: pointer;
  }

  .pagination-info {
    color: var(--gray-600);
  }

  .pagination .page-link {
    border-radius: 0.25rem;
    margin: 0 0.15rem;
    color: var(--completoplus-blue);
  }

  .pagination .page-item.active .page-link {
    background-color: var(--completoplus-blue);
    border-color: var(--completoplus-blue);
  }

  .notification-stats .badge {
    padding: 0.5rem 0.75rem;
    font-weight: 500;
    border-radius: 50px;
  }

  #load-more-btn {
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
  }

  #load-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-sm);
  }

  #load-more-btn.loading {
    pointer-events: none;
    opacity: 0.7;
  }

  #load-more-btn.loading i {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .notification-item {
      flex-direction: column;
    }

    .notification-icon {
      margin-right: 0;
      margin-bottom: 1rem;
    }

    .notification-header {
      flex-direction: column;
      align-items: flex-start;
    }

    .notification-time {
      margin-top: 0.5rem;
    }

    .notification-actions {
      flex-direction: column;
      width: 100%;
    }

    .notification-actions .btn {
      width: 100%;
      margin-bottom: 0.5rem;
    }

    .pagination-info {
      width: 100%;
      text-align: center;
      margin-bottom: 1rem;
    }

    .pagination {
      justify-content: center;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
  <!-- Modern Header Section -->
  <div class="notification-page-header d-flex justify-content-between align-items-center">
    <div>
      <h1 class="mb-2"><i class="fas fa-bell me-3"></i>Notifications</h1>
      <p class="mb-0 text-white-50">Stay updated with your project activities</p>
    </div>
    {% if notifications %}
    <form id="mark-all-read-form" action="{% url 'mark_all_notifications_read' %}" method="post">
      {% csrf_token %}
      <button type="submit" class="mark-all-btn">
        <i class="fas fa-check-double"></i>
        <span>Mark All as Read</span>
      </button>
    </form>
    {% endif %}
  </div>

  <!-- Filters and Search -->
  <div class="notification-card card mb-4">
    <div class="card-body p-4">
      <form id="notification-filter-form" method="get" class="row g-3 align-items-end">
        <!-- Search Box -->
        <div class="col-md-4">
          <label for="search" class="form-label">Search</label>
          <div class="input-group">
            <input type="text" class="form-control" id="search" name="search" placeholder="Search notifications..." value="{{ search_query }}">
            <button class="btn btn-outline-primary" type="submit">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>

        <!-- Type Filter -->
        <div class="col-md-3">
          <label for="type" class="form-label">Type</label>
          <select class="form-select" id="type" name="type">
            <option value="all" {% if current_type == 'all' %}selected{% endif %}>All Types</option>
            {% for type_code, type_name in notification_types %}
            <option value="{{ type_code }}" {% if current_type == type_code %}selected{% endif %}>{{ type_name }}</option>
            {% endfor %}
          </select>
        </div>

        <!-- Read Status Filter -->
        <div class="col-md-3">
          <label for="read" class="form-label">Status</label>
          <select class="form-select" id="read" name="read">
            <option value="all" {% if current_read == 'all' %}selected{% endif %}>All</option>
            <option value="unread" {% if current_read == 'unread' %}selected{% endif %}>Unread</option>
            <option value="read" {% if current_read == 'read' %}selected{% endif %}>Read</option>
          </select>
        </div>

        <!-- Items Per Page -->
        <div class="col-md-2">
          <label for="page_size" class="form-label">Show</label>
          <select class="form-select" id="page_size" name="page_size">
            <option value="10" {% if page_size == 10 %}selected{% endif %}>10</option>
            <option value="25" {% if page_size == 25 %}selected{% endif %}>25</option>
            <option value="50" {% if page_size == 50 %}selected{% endif %}>50</option>
            <option value="100" {% if page_size == 100 %}selected{% endif %}>100</option>
          </select>
        </div>
      </form>

      <!-- Stats Bar -->
      <div class="d-flex justify-content-between align-items-center mt-4">
        <div class="notification-stats">
          <span class="badge bg-primary me-2">{{ total_count }} Total</span>
          {% if unread_count > 0 %}
          <span class="badge bg-danger">{{ unread_count }} Unread</span>
          {% endif %}
        </div>

        <div>
          <button type="button" class="btn btn-sm btn-outline-secondary" id="reset-filters">
            <i class="fas fa-undo me-1"></i> Reset Filters
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Notifications List -->
  <div class="notification-card card">
    <div class="card-body p-4">
      {% if notifications %}
      <div class="notification-list" id="notification-items-container">
        {% include 'files/partials/notification_items.html' %}
      </div>

      <!-- Pagination or Load More -->
      <div class="text-center mt-4" id="pagination-container">
        {% if notifications.has_other_pages %}
        <div class="d-flex justify-content-between align-items-center flex-wrap">
          <!-- Pagination Info -->
          <div class="pagination-info text-muted small mb-3 mb-md-0">
            Showing {{ notifications.start_index }} to {{ notifications.end_index }} of {{ paginator.count }} notifications
          </div>

          <!-- Pagination Controls -->
          <nav aria-label="Notification pagination">
            <ul class="pagination pagination-sm mb-0">
              {% if notifications.has_previous %}
              <li class="page-item">
                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if current_type != 'all' %}&type={{ current_type }}{% endif %}{% if current_read != 'all' %}&read={{ current_read }}{% endif %}&page_size={{ page_size }}" aria-label="First">
                  <span aria-hidden="true">&laquo;&laquo;</span>
                </a>
              </li>
              <li class="page-item">
                <a class="page-link" href="?page={{ notifications.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_type != 'all' %}&type={{ current_type }}{% endif %}{% if current_read != 'all' %}&read={{ current_read }}{% endif %}&page_size={{ page_size }}" aria-label="Previous">
                  <span aria-hidden="true">&laquo;</span>
                </a>
              </li>
              {% endif %}

              {% for i in notifications.paginator.page_range %}
                {% if notifications.number == i %}
                  <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                {% elif i > notifications.number|add:'-3' and i < notifications.number|add:'3' %}
                  <li class="page-item">
                    <a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_type != 'all' %}&type={{ current_type }}{% endif %}{% if current_read != 'all' %}&read={{ current_read }}{% endif %}&page_size={{ page_size }}">{{ i }}</a>
                  </li>
                {% endif %}
              {% endfor %}

              {% if notifications.has_next %}
              <li class="page-item">
                <a class="page-link" href="?page={% if notifications.has_next %}{{ notifications.next_page_number }}{% else %}{{ notifications.number }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_type != 'all' %}&type={{ current_type }}{% endif %}{% if current_read != 'all' %}&read={{ current_read }}{% endif %}&page_size={{ page_size }}" aria-label="Next">
                  <span aria-hidden="true">&raquo;</span>
                </a>
              </li>
              <li class="page-item">
                <a class="page-link" href="?page={{ notifications.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if current_type != 'all' %}&type={{ current_type }}{% endif %}{% if current_read != 'all' %}&read={{ current_read }}{% endif %}&page_size={{ page_size }}" aria-label="Last">
                  <span aria-hidden="true">&raquo;&raquo;</span>
                </a>
              </li>
              {% endif %}
            </ul>
          </nav>
        </div>

        <!-- Load More Button (Alternative to Pagination) -->
        <button type="button" class="btn btn-outline-primary mt-4 d-none" id="load-more-btn"
          data-next-page="{% if notifications.has_next %}{{ notifications.next_page_number }}{% else %}0{% endif %}"
          data-has-next="{{ notifications.has_next|lower }}">
          <i class="fas fa-spinner me-2"></i> Load More
        </button>
        {% endif %}
      </div>
      {% else %}
      <!-- Empty State or No Results -->
      <div class="notification-empty">
        <div class="notification-empty-icon">
          {% if search_query or current_type != 'all' or current_read != 'all' %}
          <i class="fas fa-search"></i>
          {% else %}
          <i class="far fa-bell-slash"></i>
          {% endif %}
        </div>
        {% if search_query or current_type != 'all' or current_read != 'all' %}
        <h4 class="notification-empty-text">No notifications match your filters</h4>
        <p class="text-muted">Try adjusting your search criteria or filters</p>
        <button type="button" class="btn btn-primary" id="reset-filters-empty">
          <i class="fas fa-undo me-2"></i> Reset Filters
        </button>
        {% else %}
        <h4 class="notification-empty-text">You don't have any notifications</h4>
        <p class="text-muted">When you receive updates on your projects, they will appear here</p>
        <a href="{% url 'project_list' %}" class="btn btn-primary">
          <i class="fas fa-project-diagram me-2"></i> Go to Projects
        </a>
        {% endif %}
      </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}



{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle Mark All as Read form submission via AJAX
        const markAllReadForm = document.getElementById('mark-all-read-form');
        if (markAllReadForm) {
            markAllReadForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Get CSRF token
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

                // Send AJAX request
                fetch(this.action, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Update UI to show all notifications as read
                        document.querySelectorAll('.notification-item.unread').forEach(item => {
                            item.classList.remove('unread');
                        });

                        document.querySelectorAll('.notification-badge.new').forEach(badge => {
                            badge.remove();
                        });

                        document.querySelectorAll('.mark-read-btn').forEach(btn => {
                            btn.remove();
                        });

                        // Show success message
                        const alertDiv = document.createElement('div');
                        alertDiv.className = 'alert alert-success alert-dismissible fade show animate__animated animate__fadeIn';
                        alertDiv.innerHTML = `
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle me-2 fs-4"></i>
                                <div>${data.message}</div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        `;

                        const container = document.querySelector('.container');
                        container.insertBefore(alertDiv, container.firstChild);

                        // Hide the Mark All as Read button
                        markAllReadForm.style.display = 'none';

                        // Update notification badge in navbar
                        const navBadge = document.querySelector('.notification-badge');
                        if (navBadge) {
                            navBadge.style.display = 'none';
                        }

                        // Update unread count in the stats
                        const unreadBadge = document.querySelector('.notification-stats .badge.bg-danger');
                        if (unreadBadge) {
                            unreadBadge.remove();
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        }

        // Handle individual Mark as Read buttons via AJAX
        function setupMarkAsReadButtons() {
            document.querySelectorAll('a[href^="/files/notifications/mark-read/"]').forEach(link => {
                if (link.hasAttribute('data-event-attached')) return;

                link.setAttribute('data-event-attached', 'true');
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    const notificationItem = this.closest('.notification-item');
                    const url = this.getAttribute('href');

                    // Send AJAX request
                    fetch(url, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            // Update UI to show notification as read
                            notificationItem.classList.remove('unread');

                            // Remove the New badge
                            const badge = notificationItem.querySelector('.notification-badge.new');
                            if (badge) badge.remove();

                            // Remove the Mark as Read button
                            this.remove();

                            // Decrement notification count in navbar
                            const navBadge = document.querySelector('.notification-badge');
                            if (navBadge) {
                                const count = parseInt(navBadge.textContent || '0');
                                if (count > 1) {
                                    navBadge.textContent = count - 1;
                                } else {
                                    navBadge.style.display = 'none';
                                }
                            }

                            // Update unread count in the stats
                            const unreadBadge = document.querySelector('.notification-stats .badge.bg-danger');
                            if (unreadBadge) {
                                const unreadCount = parseInt(unreadBadge.textContent.split(' ')[0]) - 1;
                                if (unreadCount > 0) {
                                    unreadBadge.textContent = `${unreadCount} Unread`;
                                } else {
                                    unreadBadge.remove();
                                }
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
                });
            });
        }

        // Initial setup of mark as read buttons
        setupMarkAsReadButtons();

        // Add animation effects to notification items
        function animateNotificationItems() {
            const notificationItems = document.querySelectorAll('.notification-item:not(.animated)');
            notificationItems.forEach((item, index) => {
                // Add staggered animation delay
                item.style.animationDelay = `${index * 0.1}s`;
                item.classList.add('animate__animated', 'animate__fadeInUp', 'animated');
            });
        }

        // Initial animation
        animateNotificationItems();

        // Handle filter form changes
        const filterForm = document.getElementById('notification-filter-form');
        if (filterForm) {
            // Auto-submit form when select fields change
            const selectFields = filterForm.querySelectorAll('select');
            selectFields.forEach(select => {
                select.addEventListener('change', function() {
                    filterForm.submit();
                });
            });

            // Reset filters button
            const resetFiltersBtn = document.getElementById('reset-filters');
            const resetFiltersEmptyBtn = document.getElementById('reset-filters-empty');

            function resetFilters() {
                window.location.href = '{% url "notification_list" %}';
            }

            if (resetFiltersBtn) {
                resetFiltersBtn.addEventListener('click', resetFilters);
            }

            if (resetFiltersEmptyBtn) {
                resetFiltersEmptyBtn.addEventListener('click', resetFilters);
            }
        }

        // Load More functionality
        const loadMoreBtn = document.getElementById('load-more-btn');
        const paginationLinks = document.querySelectorAll('.pagination .page-link');
        const toggleViewBtn = document.getElementById('toggle-view-btn');

        // Toggle between pagination and load more
        if (toggleViewBtn) {
            toggleViewBtn.addEventListener('click', function() {
                const paginationContainer = document.querySelector('.pagination');
                if (paginationContainer) {
                    paginationContainer.parentElement.classList.toggle('d-none');
                }

                if (loadMoreBtn) {
                    loadMoreBtn.classList.toggle('d-none');
                }

                // Update button text
                if (this.textContent.includes('Load More')) {
                    this.innerHTML = '<i class="fas fa-th-list me-1"></i> Pagination View';
                } else {
                    this.innerHTML = '<i class="fas fa-spinner me-1"></i> Load More View';
                }
            });
        }

        // Handle Load More button
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', function() {
                const nextPage = this.getAttribute('data-next-page');
                const hasNext = this.getAttribute('data-has-next') === 'true';

                if (!hasNext) return;

                // Show loading state
                this.classList.add('loading');
                this.innerHTML = '<i class="fas fa-spinner me-2"></i> Loading...';

                // Build URL with current filters
                let url = `?page=${nextPage}`;
                const searchParam = new URLSearchParams(window.location.search).get('search');
                const typeParam = new URLSearchParams(window.location.search).get('type');
                const readParam = new URLSearchParams(window.location.search).get('read');
                const pageSizeParam = new URLSearchParams(window.location.search).get('page_size');

                if (searchParam) url += `&search=${searchParam}`;
                if (typeParam && typeParam !== 'all') url += `&type=${typeParam}`;
                if (readParam && readParam !== 'all') url += `&read=${readParam}`;
                if (pageSizeParam) url += `&page_size=${pageSizeParam}`;

                // Add AJAX header
                url += '&format=json';

                // Fetch next page
                fetch(url, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    // Append new items
                    const container = document.getElementById('notification-items-container');
                    container.insertAdjacentHTML('beforeend', data.html);

                    // Update button state
                    if (data.has_next && data.next_page) {
                        this.setAttribute('data-next-page', data.next_page);
                        this.classList.remove('loading');
                        this.innerHTML = '<i class="fas fa-spinner me-2"></i> Load More';
                    } else {
                        this.setAttribute('data-has-next', 'false');
                        this.classList.add('disabled');
                        this.innerHTML = '<i class="fas fa-check-circle me-2"></i> All Loaded';
                    }

                    // Setup mark as read buttons for new items
                    setupMarkAsReadButtons();

                    // Animate new items
                    animateNotificationItems();
                })
                .catch(error => {
                    console.error('Error:', error);
                    this.classList.remove('loading');
                    this.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i> Error Loading';
                });
            });
        }
    });
</script>
{% endblock %}