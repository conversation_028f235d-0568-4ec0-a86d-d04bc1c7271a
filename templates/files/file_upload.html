{% extends 'base/base.html' %} {% block title %}Upload File - {{ project.name }}
- CompletoPLUS{% endblock %} {% block extra_css %}
<style>
  .file-upload-area {
    border: 2px dashed #7209b7;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    margin-bottom: 20px;
    transition: all 0.3s;
    cursor: pointer;
    background-color: rgba(114, 9, 183, 0.03);
  }

  .file-upload-area:hover {
    border-color: #7209b7;
    background-color: rgba(114, 9, 183, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(114, 9, 183, 0.1);
  }

  .file-upload-area.drag-highlight {
    border-color: #7209b7;
    background-color: rgba(114, 9, 183, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(114, 9, 183, 0.2);
  }

  .file-upload-area input[type="file"] {
    margin-bottom: 10px;
    opacity: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    cursor: pointer;
  }

  .file-upload-icon {
    font-size: 3rem;
    color: #7209b7;
    margin-bottom: 1rem;
  }

  .drag-text {
    font-size: 1.2rem;
    font-weight: 500;
    color: #333;
    margin-bottom: 0.5rem;
  }

  .file-info {
    background-color: rgba(114, 9, 183, 0.05);
    border-left: 4px solid #7209b7;
  }

  .upload-progress {
    margin-top: 1rem;
  }

  .progress-bar {
    background-color: #7209b7;
  }

  .accordion-button:not(.collapsed) {
    background-color: rgba(114, 9, 183, 0.1);
    color: #7209b7;
  }

  .accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(114, 9, 183, 0.25);
  }

  .badge {
    background-color: rgba(114, 9, 183, 0.1) !important;
    color: #7209b7 !important;
    font-weight: 600;
  }

  .file-item {
    transition: all 0.2s;
  }

  .file-item:hover {
    background-color: rgba(114, 9, 183, 0.05);
  }

  #file-list-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    margin-top: 1rem;
  }
</style>
{% endblock %} {% block content %}
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h4 class="mb-0">Upload File to "{{ project.name }}"</h4>
        </div>
        <div class="card-body">
          <form
            method="post"
            enctype="multipart/form-data"
            class="file-upload-form"
            onsubmit="return false;"
            id="file-upload-form"
          >
            {% csrf_token %}

            <!-- Alert containers -->
            <div id="file-upload-error" class="alert alert-danger d-none"></div>
            <div
              id="file-upload-success"
              class="alert alert-success d-none"
            ></div>

            {% if form.non_field_errors %}
            <div class="alert alert-danger">
              {% for error in form.non_field_errors %} {{ error }} {% endfor %}
            </div>
            {% endif %}

            <div class="mb-3">
              <label for="{{ form.file.id_for_label }}" class="form-label"
                >Select File</label
              >
              <div
                class="file-upload-area position-relative p-4 mb-2"
                data-project-id="{{ project.id }}"
              >
                <input
                  type="file"
                  name="{{ form.file.name }}"
                  id="{{ form.file.id_for_label }}"
                  class="form-control {% if form.file.errors %}is-invalid{% endif %}"
                  multiple
                  required
                  accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.rtf,.odt,.ods,.odp,.jpg,.jpeg,.png,.gif,.svg,.webp,.tiff,.bmp,.zip,.rar,.7z,.tar,.gz,.mp3,.mp4,.wav,.avi,.mov,.wmv,.py,.js,.html,.css,.json,.xml,.csv,.md,.ipynb,.r,.rmd,.sav,.dta"
                />
                <div class="text-center">
                  <i class="fas fa-cloud-upload-alt file-upload-icon"></i>
                  <p class="drag-text">
                    Drag and drop files here or click to browse
                  </p>
                  <div class="row justify-content-center">
                    <div class="col-md-10">
                      <p class="text-muted small">
                        You can select multiple files at once. Files will be
                        automatically compressed into a single zip file. Maximum
                        size: 50MB per file, 100MB total.
                      </p>
                      <div
                        class="d-flex justify-content-center align-items-center gap-2 mb-2"
                      >
                        <span class="badge rounded-pill"
                          ><i class="fas fa-file-word me-1"></i> Documents</span
                        >
                        <span class="badge rounded-pill"
                          ><i class="fas fa-file-image me-1"></i> Images</span
                        >
                        <span class="badge rounded-pill"
                          ><i class="fas fa-file-code me-1"></i> Code</span
                        >
                        <span class="badge rounded-pill"
                          ><i class="fas fa-file-audio me-1"></i> Media</span
                        >
                      </div>
                      <p class="text-muted small">
                        <i class="fas fa-info-circle me-1"></i>
                        <strong>Tip:</strong> Hold Ctrl (or Cmd on Mac) while
                        clicking to select multiple files.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- File list container -->
              <div id="file-list-container" class="d-none">
                <div class="p-2 bg-light border-bottom">
                  <div
                    class="d-flex justify-content-between align-items-center"
                  >
                    <h6 class="mb-0">Selected Files</h6>
                    <button
                      type="button"
                      class="btn btn-sm btn-outline-danger"
                      id="clear-files"
                    >
                      <i class="fas fa-trash-alt me-1"></i>Clear All
                    </button>
                  </div>
                </div>
                <div id="file-list"></div>
              </div>

              <!-- Upload progress -->
              <div id="upload-progress" class="upload-progress d-none">
                <div class="progress">
                  <div
                    class="progress-bar progress-bar-striped progress-bar-animated"
                    role="progressbar"
                    aria-valuenow="0"
                    aria-valuemin="0"
                    aria-valuemax="100"
                    style="width: 0%"
                  ></div>
                </div>
              </div>
              {% if form.file.errors %}
              <div class="invalid-feedback d-block">
                {% for error in form.file.errors %} {{ error }} {% endfor %}
              </div>
              {% endif %} {% if form.file.help_text %}
              <small class="form-text text-muted"
                >{{ form.file.help_text }}</small
              >
              {% endif %}
              <div class="mt-2">
                <h6 class="text-muted">Supported File Types:</h6>
                <div class="accordion" id="fileTypesAccordion">
                  {% for category, file_types in accepted_file_types.items %}
                  <div class="accordion-item">
                    <h2
                      class="accordion-header"
                      id="heading{{ category|slugify }}"
                    >
                      <button
                        class="accordion-button collapsed"
                        type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapse{{ category|slugify }}"
                        aria-expanded="false"
                        aria-controls="collapse{{ category|slugify }}"
                      >
                        {{ category }}
                      </button>
                    </h2>
                    <div
                      id="collapse{{ category|slugify }}"
                      class="accordion-collapse collapse"
                      aria-labelledby="heading{{ category|slugify }}"
                      data-bs-parent="#fileTypesAccordion"
                    >
                      <div class="accordion-body">
                        <div class="row">
                          {% for file_type in file_types %}
                          <div class="col-md-4 mb-1">
                            <span class="badge bg-light text-dark"
                              >.{{ file_type.extension }}</span
                            >
                            <small>{{ file_type.description }}</small>
                          </div>
                          {% endfor %}
                        </div>
                      </div>
                    </div>
                  </div>
                  {% endfor %}
                </div>
              </div>
            </div>

            <div class="mb-3">
              <label for="{{ form.file_name.id_for_label }}" class="form-label"
                >File Name</label
              >
              <input
                type="text"
                name="{{ form.file_name.name }}"
                id="{{ form.file_name.id_for_label }}"
                class="form-control {% if form.file_name.errors %}is-invalid{% endif %}"
                required
              />
              {% if form.file_name.errors %}
              <div class="invalid-feedback">
                {% for error in form.file_name.errors %} {{ error }} {% endfor
                %}
              </div>
              {% endif %} {% if form.file_name.help_text %}
              <small class="form-text text-muted"
                >{{ form.file_name.help_text }}</small
              >
              {% endif %}
            </div>

            <div class="mb-3">
              <label for="{{ form.notes.id_for_label }}" class="form-label"
                >Notes</label
              >
              <textarea
                name="{{ form.notes.name }}"
                id="{{ form.notes.id_for_label }}"
                class="form-control {% if form.notes.errors %}is-invalid{% endif %}"
                rows="3"
              ></textarea>
              {% if form.notes.errors %}
              <div class="invalid-feedback">
                {% for error in form.notes.errors %} {{ error }} {% endfor %}
              </div>
              {% endif %} {% if form.notes.help_text %}
              <small class="form-text text-muted"
                >{{ form.notes.help_text }}</small
              >
              {% endif %}
            </div>

            <div class="d-grid gap-2">
              <button
                type="submit"
                id="upload-button"
                class="btn btn-primary btn-lg"
              >
                <i class="fas fa-upload me-2"></i>Upload File
              </button>
              <a
                href="{% url 'project_detail' project.id %}"
                class="btn btn-outline-secondary"
                ><i class="fas fa-times me-2"></i>Cancel</a
              >
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<!-- JSZip library for client-side zipping -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<!-- Enhanced file upload script -->
<script src="/static/js/enhanced-file-upload.js"></script>
{% endblock %}
