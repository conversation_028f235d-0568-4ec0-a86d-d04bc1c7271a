{% extends 'base/base.html' %}

{% block title %}Projects - CompletoPLUS{% endblock %}

{% block meta_description %}View and manage all your projects in one place. Track progress, filter by status, and access your academic assignments, data analysis, web development, and machine learning projects.{% endblock %}

{% block breadcrumb_schema %}
<script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://completoplus.com/"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Projects",
        "item": "{{ request.build_absolute_uri }}"
      }
    ]
  }
</script>
{% endblock %}

{% block content %}
<main class="container py-5">
    <header class="row mb-4">
        <div class="col-md-8">
            <h1 class="fw-bold mb-2">Your Projects</h1>
            <p class="text-muted">Manage and track all your projects in one place</p>
        </div>
        <div class="col-md-4 text-md-end d-flex align-items-center justify-content-md-end">
            <a href="{% url 'project_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i> New Project
            </a>
        </div>
    </header>

    <!-- Filter Controls -->
    <section class="card mb-4 border-0 shadow-sm" aria-labelledby="filter-heading">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2 id="filter-heading" class="h5 mb-0"><i class="fas fa-filter me-2 text-primary"></i>Filter Projects</h2>
            <button class="btn btn-sm btn-link text-muted" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="true" aria-controls="filterCollapse">
                <i class="fas fa-chevron-down"></i>
            </button>
        </div>
        <div class="collapse show" id="filterCollapse">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="status" class="form-label fw-medium">Status</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light"><i class="fas fa-tasks text-muted"></i></span>
                            <select name="status" id="status" class="form-select">
                                <option value="">All Statuses</option>
                                <option value="pending" {% if request.GET.status == 'pending' %}selected{% endif %}>Pending</option>
                                <option value="in_progress" {% if request.GET.status == 'in_progress' %}selected{% endif %}>In Progress</option>
                                <option value="completed" {% if request.GET.status == 'completed' %}selected{% endif %}>Completed</option>
                            </select>
                        </div>
                    </div>

                    {% if user.is_admin_user %}
                    <div class="col-md-4">
                        <label for="client" class="form-label fw-medium">Client</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light"><i class="fas fa-user text-muted"></i></span>
                            <input type="text" name="client" id="client" class="form-control" value="{{ request.GET.client }}">
                        </div>
                    </div>
                    {% endif %}

                    <div class="col-md-4">
                        <label for="search" class="form-label fw-medium">Search</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light"><i class="fas fa-search text-muted"></i></span>
                            <input type="text" name="search" id="search" class="form-control" placeholder="Project name..." value="{{ request.GET.search }}">
                        </div>
                    </div>

                    <div class="col-12 mt-4">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i>Apply Filters
                            </button>
                            <a href="{% url 'project_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Clear Filters
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Projects Table -->
    <section class="card border-0 shadow-sm" aria-labelledby="project-list-heading">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h2 id="project-list-heading" class="h5 mb-0"><i class="fas fa-folder me-2 text-primary"></i>Project List</h2>
            <div class="btn-group btn-group-sm" role="group" aria-label="Toggle view mode">
                <button type="button" class="btn btn-outline-primary active" id="table-view-btn" aria-pressed="true">
                    <i class="fas fa-list" aria-hidden="true"></i>
                    <span class="visually-hidden">Table View</span>
                </button>
                <button type="button" class="btn btn-outline-primary" id="card-view-btn" aria-pressed="false">
                    <i class="fas fa-th-large" aria-hidden="true"></i>
                    <span class="visually-hidden">Card View</span>
                </button>
            </div>
        </div>
        <div class="card-body">
            {% if projects %}
                <!-- Table View -->
                <div class="table-responsive" id="table-view">
                    <table class="table table-hover align-middle">
                        <thead class="table-light">
                            <tr>
                                <th>Project Name</th>
                                {% if user.is_admin_user %}
                                    <th>Client</th>
                                {% endif %}
                                <th>Status</th>
                                <th>Progress</th>
                                <th>Created</th>
                                <th>Last Updated</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for project in projects %}
                                <tr>
                                    <td class="fw-medium">{{ project.name }}</td>
                                    {% if user.is_admin_user %}
                                        <td>{{ project.client.username }}</td>
                                    {% endif %}
                                    <td>
                                        {% if project.status == 'pending' %}
                                            <span class="badge bg-warning text-dark rounded-pill px-3 py-2">Pending</span>
                                        {% elif project.status == 'in_progress' %}
                                            <span class="badge bg-info text-dark rounded-pill px-3 py-2">In Progress</span>
                                        {% elif project.status == 'completed' %}
                                            <span class="badge bg-success text-white rounded-pill px-3 py-2">Completed</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 8px; width: 100px;">
                                            <div class="progress-bar" role="progressbar" style="width: {{ project.progress }}%" aria-valuenow="{{ project.progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                        <span class="small text-muted">{{ project.progress }}%</span>
                                    </td>
                                    <td><span class="text-muted">{{ project.created_at|date:"M d, Y" }}</span></td>
                                    <td><span class="text-muted">{{ project.updated_at|date:"M d, Y" }}</span></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'project_detail' project.pk %}" class="btn btn-primary" data-bs-toggle="tooltip" title="View Project">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if user.is_admin_user %}
                                                <a href="{% url 'project_update' project.pk %}" class="btn btn-warning" data-bs-toggle="tooltip" title="Edit Project">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Card View -->
                <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4" id="card-view" style="display: none;">
                    {% for project in projects %}
                    <div class="col">
                        <div class="card h-100 project-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0 text-truncate" title="{{ project.name }}">{{ project.name }}</h5>
                                {% if project.status == 'pending' %}
                                    <span class="badge bg-warning text-dark rounded-pill px-3 py-2">Pending</span>
                                {% elif project.status == 'in_progress' %}
                                    <span class="badge bg-info text-dark rounded-pill px-3 py-2">In Progress</span>
                                {% elif project.status == 'completed' %}
                                    <span class="badge bg-success text-white rounded-pill px-3 py-2">Completed</span>
                                {% endif %}
                            </div>
                            <div class="card-body">
                                {% if project.description %}
                                <p class="card-text">{{ project.description|truncatechars:100 }}</p>
                                {% endif %}
                                <div class="progress-section p-3 bg-light rounded mb-3">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="small fw-medium">Progress</span>
                                        <span class="small text-muted">{{ project.progress }}%</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar" style="width: {{ project.progress }}%" aria-valuenow="{{ project.progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center text-muted small">
                                    <span><i class="fas fa-calendar-alt me-1"></i> {{ project.created_at|date:"M d, Y" }}</span>
                                    <span><i class="fas fa-clock me-1"></i> {{ project.updated_at|date:"M d, Y" }}</span>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="d-flex gap-2">
                                    <a href="{% url 'project_detail' project.pk %}" class="btn btn-primary flex-grow-1">
                                        <i class="fas fa-eye me-2"></i>View
                                    </a>
                                    {% if user.is_admin_user %}
                                    <a href="{% url 'project_update' project.pk %}" class="btn btn-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state text-center py-5">
                    <div class="empty-state-icon mb-4">
                        <i class="fas fa-folder-open fa-4x text-muted opacity-50"></i>
                    </div>
                    <h3 class="fw-bold">No projects found</h3>
                    <p class="text-muted mb-4">{% if not user.is_admin_user %}Click the "New Project" button to create one.{% else %}No projects match your filter criteria.{% endif %}</p>
                    <a href="{% url 'project_create' %}" class="btn btn-primary btn-lg">
                        <i class="fas fa-plus me-2"></i>Create Project
                    </a>
                </div>
            {% endif %}
        </div>
    </section>
</main>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // View toggle functionality
    const tableViewBtn = document.getElementById('table-view-btn');
    const cardViewBtn = document.getElementById('card-view-btn');
    const tableView = document.getElementById('table-view');
    const cardView = document.getElementById('card-view');

    // Check if there's a saved preference in localStorage
    const savedView = localStorage.getItem('projectViewPreference');
    if (savedView === 'card') {
      showCardView();
    }

    tableViewBtn.addEventListener('click', function() {
      showTableView();
      localStorage.setItem('projectViewPreference', 'table');
    });

    cardViewBtn.addEventListener('click', function() {
      showCardView();
      localStorage.setItem('projectViewPreference', 'card');
    });

    function showTableView() {
      tableView.style.display = 'block';
      cardView.style.display = 'none';
      tableViewBtn.classList.add('active');
      cardViewBtn.classList.remove('active');
    }

    function showCardView() {
      tableView.style.display = 'none';
      cardView.style.display = 'flex';
      tableViewBtn.classList.remove('active');
      cardViewBtn.classList.add('active');
    }

    // Add project-card hover effect
    const projectCards = document.querySelectorAll('.project-card');
    projectCards.forEach(card => {
      card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-5px)';
        this.style.boxShadow = 'var(--box-shadow-lg)';
      });

      card.addEventListener('mouseleave', function() {
        this.style.transform = '';
        this.style.boxShadow = '';
      });
    });
  });
</script>
{% endblock %}

{% block extra_css %}
<style>
  .project-card {
    transition: var(--transition-base);
  }

  .progress-section {
    border-radius: var(--border-radius-sm);
  }

  .empty-state {
    max-width: 500px;
    margin: 0 auto;
  }

  .empty-state-icon {
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
  }


</style>
{% endblock %}
