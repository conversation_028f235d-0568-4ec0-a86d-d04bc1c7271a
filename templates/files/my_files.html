{% extends 'base/base.html' %}

{% block title %}My Files - CompletoPLUS{% endblock %}

{% block content %}
<div class="container py-4">
  <!-- Header Section -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <div>
      <h1 class="h3 mb-1"><i class="fas fa-file-alt me-2"></i>My Files</h1>
      <p class="text-muted mb-0">View and manage all your files across projects</p>
    </div>
  </div>

  <!-- Files Container -->
  <div class="card border-0 shadow-sm">
    <!-- Filters and Search -->
    <div class="card-header bg-light p-3">
      <form id="file-filter-form" method="get" class="row g-3 align-items-end">
        <!-- File Type Filter -->
        <div class="col-md-3">
          <label for="file-type-filter" class="form-label">File Type</label>
          <select id="file-type-filter" name="file_type" class="form-select">
            <option value="all" {% if current_file_type == 'all' %}selected{% endif %}>All Types</option>
            <option value="client_upload" {% if current_file_type == 'client_upload' %}selected{% endif %}>Client Uploads</option>
            <option value="admin_upload" {% if current_file_type == 'admin_upload' %}selected{% endif %}>Admin Uploads</option>
          </select>
        </div>

        <!-- Date Filter -->
        <div class="col-md-3">
          <label for="date-filter" class="form-label">Upload Date</label>
          <input type="date" id="date-filter" name="date" class="form-control" value="{{ current_date|default:'' }}">
        </div>

        <!-- Search -->
        <div class="col-md-4">
          <label for="search-input" class="form-label">Search Files</label>
          <div class="input-group">
            <input type="text" id="search-input" name="search" class="form-control" placeholder="Search by name or notes..." value="{{ search_query }}">
            <button type="submit" class="btn btn-primary">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>

        <!-- Page Size -->
        <div class="col-md-2">
          <label for="page-size" class="form-label">Items Per Page</label>
          <select id="page-size" name="page_size" class="form-select">
            <option value="10" {% if page_size == 10 %}selected{% endif %}>10</option>
            <option value="25" {% if page_size == 25 %}selected{% endif %}>25</option>
            <option value="50" {% if page_size == 50 %}selected{% endif %}>50</option>
            <option value="100" {% if page_size == 100 %}selected{% endif %}>100</option>
          </select>
        </div>
      </form>
    </div>

    <!-- Files Table -->
    <div class="card-body p-0">
      {% if files %}
        <div class="table-responsive">
          <table class="table table-hover align-middle mb-0">
            <thead class="table-light">
              <tr>
                <th scope="col" style="width: 5%">
                  <div class="form-check">
                    <input class="form-check-input select-all-files" type="checkbox" id="select-all-files">
                  </div>
                </th>
                <th scope="col" style="width: 35%">File</th>
                <th scope="col" style="width: 15%">Project</th>
                <th scope="col" style="width: 15%">Uploaded By</th>
                <th scope="col" style="width: 15%">Date</th>
                <th scope="col" style="width: 10%">Size</th>
                <th scope="col" style="width: 5%">Actions</th>
              </tr>
            </thead>
            <tbody id="file-items-container">
              {% for file in files %}
                <tr class="file-row" data-file-type="{{ file.file_type }}" data-upload-date="{{ file.uploaded_at|date:'Y-m-d' }}">
                  <td>
                    <div class="form-check">
                      <input class="form-check-input file-checkbox" type="checkbox" name="file_ids" value="{{ file.id }}" form="batch-action-form">
                    </div>
                  </td>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="file-icon me-2">
                        <i class="{{ file.icon_class }} fa-lg"></i>
                      </div>
                      <div>
                        <div class="fw-bold">{{ file.file_name }}</div>
                        <div class="small text-muted">
                          {{ file.original_file_name|default:file.file.name|truncatechars:40 }}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <a href="{% url 'project_detail' file.project.id %}" class="text-decoration-none">
                      {{ file.project.name|truncatechars:25 }}
                    </a>
                  </td>
                  <td>
                    <div>
                      {{ file.owner.get_full_name|default:file.owner.username }}
                      <div>
                        {% if file.file_type == 'admin_upload' %}
                        <span class="badge bg-primary">Admin</span>
                        {% else %}
                        <span class="badge bg-secondary">Client</span>
                        {% endif %}
                      </div>
                    </div>
                  </td>
                  <td>
                    <div>
                      {{ file.uploaded_at|date:"M d, Y" }}
                      <div class="small text-muted">
                        {{ file.uploaded_at|time:"g:i A" }}
                      </div>
                    </div>
                  </td>
                  <td>{{ file.get_size_display }}</td>
                  <td>
                    <div class="dropdown">
                      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="dropdownMenuButton{{ file.id }}" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-ellipsis-v"></i>
                      </button>
                      <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton{{ file.id }}">
                        <li>
                          <a class="dropdown-item" href="{% url 'file_download' file.id %}">
                            <i class="fas fa-download me-2"></i> Download
                          </a>
                        </li>
                        <li>
                          <a class="dropdown-item" href="{% url 'file_preview' file.id %}">
                            <i class="fas fa-eye me-2"></i> Preview
                          </a>
                        </li>
                        {% if request.user == file.owner or request.user == file.project.client or request.user.is_admin_user %}
                        <li>
                          <a class="dropdown-item text-danger" href="{% url 'file_delete' file.id %}">
                            <i class="fas fa-trash-alt me-2"></i> Delete
                          </a>
                        </li>
                        {% endif %}
                      </ul>
                    </div>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <!-- Batch Actions Form -->
        <form id="batch-action-form" method="post" class="d-none">
          {% csrf_token %}
          <input type="hidden" name="action" id="batch-action-input" value="">
        </form>

        <!-- Pagination -->
        <div class="d-flex justify-content-between align-items-center p-3 border-top">
          <div class="small text-muted">
            Showing {{ files.start_index }} to {{ files.end_index }} of {{ total_files_count }} files
          </div>

          <nav aria-label="Files pagination">
            <ul class="pagination mb-0">
              {% if files.has_previous %}
                <li class="page-item">
                  <a class="page-link" href="?page=1{% if current_file_type != 'all' %}&file_type={{ current_file_type }}{% endif %}{% if current_date != 'all' %}&date={{ current_date }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}&page_size={{ page_size }}" aria-label="First">
                    <span aria-hidden="true">&laquo;&laquo;</span>
                  </a>
                </li>
                <li class="page-item">
                  <a class="page-link" href="?page={{ files.previous_page_number }}{% if current_file_type != 'all' %}&file_type={{ current_file_type }}{% endif %}{% if current_date != 'all' %}&date={{ current_date }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}&page_size={{ page_size }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                  </a>
                </li>
              {% else %}
                <li class="page-item disabled">
                  <span class="page-link">&laquo;&laquo;</span>
                </li>
                <li class="page-item disabled">
                  <span class="page-link">&laquo;</span>
                </li>
              {% endif %}

              {% for i in files.paginator.page_range %}
                {% if files.number == i %}
                  <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                {% elif i > files.number|add:"-3" and i < files.number|add:"3" %}
                  <li class="page-item">
                    <a class="page-link" href="?page={{ i }}{% if current_file_type != 'all' %}&file_type={{ current_file_type }}{% endif %}{% if current_date != 'all' %}&date={{ current_date }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}&page_size={{ page_size }}">{{ i }}</a>
                  </li>
                {% endif %}
              {% endfor %}

              {% if files.has_next %}
                <li class="page-item">
                  <a class="page-link" href="?page={{ files.next_page_number }}{% if current_file_type != 'all' %}&file_type={{ current_file_type }}{% endif %}{% if current_date != 'all' %}&date={{ current_date }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}&page_size={{ page_size }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                  </a>
                </li>
                <li class="page-item">
                  <a class="page-link" href="?page={{ files.paginator.num_pages }}{% if current_file_type != 'all' %}&file_type={{ current_file_type }}{% endif %}{% if current_date != 'all' %}&date={{ current_date }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}&page_size={{ page_size }}" aria-label="Last">
                    <span aria-hidden="true">&raquo;&raquo;</span>
                  </a>
                </li>
              {% else %}
                <li class="page-item disabled">
                  <span class="page-link">&raquo;</span>
                </li>
                <li class="page-item disabled">
                  <span class="page-link">&raquo;&raquo;</span>
                </li>
              {% endif %}
            </ul>
          </nav>
        </div>
      {% else %}
        <div class="text-center py-5">
          <div class="mb-4">
            <i class="fas fa-file-alt fa-4x text-muted"></i>
          </div>
          <h2 class="h4 mb-3">No Files Found</h2>
          <p class="text-muted mb-4">
            {% if search_query or current_file_type != 'all' or current_date != 'all' %}
              No files match your current filters. Try adjusting your search criteria.
              <br>
              <button id="reset-filters" class="btn btn-outline-primary mt-3">
                <i class="fas fa-undo me-2"></i>Reset Filters
              </button>
            {% else %}
              You don't have any files yet. Upload files to your projects to see them here.
            {% endif %}
          </p>
        </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form when filters change
    const filterForm = document.getElementById('file-filter-form');
    const filterInputs = filterForm.querySelectorAll('select, input[type="date"]');

    filterInputs.forEach(input => {
      input.addEventListener('change', function() {
        filterForm.submit();
      });
    });

    // Reset filters button
    const resetButton = document.getElementById('reset-filters');
    if (resetButton) {
      resetButton.addEventListener('click', function() {
        window.location.href = '{% url "my_files" %}';
      });
    }

    // Select all files checkbox
    const selectAllCheckbox = document.querySelector('.select-all-files');
    const fileCheckboxes = document.querySelectorAll('.file-checkbox');

    if (selectAllCheckbox) {
      selectAllCheckbox.addEventListener('change', function() {
        fileCheckboxes.forEach(checkbox => {
          checkbox.checked = selectAllCheckbox.checked;
        });
      });
    }
  });
</script>
{% endblock %}
