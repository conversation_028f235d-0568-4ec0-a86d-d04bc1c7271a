{% load note_filters %} {% if progress_notes %}
<div class="progress-notes-list">
  {% for note in progress_notes %}
  <div
    class="progress-note-item animate__animated animate__fadeIn mb-4 pb-4 border-bottom"
  >
    <div class="progress-note-header d-flex justify-content-between mb-2">
      <span class="progress-note-date text-muted">
        <i class="far fa-calendar-alt me-1"></i> {{ note.created_at|date:"M d,
        Y" }} <i class="far fa-clock ms-2 me-1"></i> {{
        note.created_at|time:"H:i" }}
      </span>
      <span class="progress-note-author badge bg-light text-dark">
        <i class="far fa-user me-1"></i> {{ note.created_by.username }}
      </span>
    </div>
    <div class="progress-note-content">
      {% if "\n\nProject progress updated from" in note.note or "\n\nProject
      status updated from" in note.note %} {% with
      note_parts=note.note|split_progress_note %}
      <div class="note-text-content">{{ note_parts.note_text|linebreaks }}</div>
      {% if note_parts.progress_text %}
      <div class="progress-update-section mt-3">
        <div class="progress-update-badge">
          <i class="fas fa-chart-line me-1"></i> Progress Update
        </div>
        <div class="progress-update-text">
          {{ note_parts.progress_text|linebreaks }}
        </div>
      </div>
      {% endif %} {% if note_parts.status_text %}
      <div class="status-update-section mt-2">
        <div class="status-update-badge">
          <i class="fas fa-tasks me-1"></i> Status Update
        </div>
        <div class="status-update-text">
          {{ note_parts.status_text|linebreaks }}
        </div>
      </div>
      {% endif %} {% endwith %} {% else %} {{ note.note|linebreaks }} {% endif
      %}
    </div>
  </div>
  {% endfor %}
</div>
{% else %}
<div class="text-center py-4 no-notes-message">
  <div class="mb-3">
    <i class="fas fa-clipboard-check text-muted" style="font-size: 3rem"></i>
  </div>
  <p class="text-muted">No progress updates yet.</p>
  {% if user.is_admin_user %}
  <button
    type="button"
    class="btn btn-sm btn-outline-primary mt-2"
    data-bs-toggle="modal"
    data-bs-target="#addProgressNoteModal"
  >
    <i class="fas fa-plus me-1"></i> Add First Update
  </button>
  {% endif %}
</div>
{% endif %}
