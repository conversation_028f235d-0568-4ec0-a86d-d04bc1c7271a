{% extends "emails/email_base.html" %}

{% block title %}Project Status Update - CompletoPLUS{% endblock %}

{% block content %}
<h1>Project Status Update</h1>

<p>Hello {{ user.get_full_name|default:user.username }},</p>

<p>The status of your project <strong>{{ project.name }}</strong> has been updated.</p>

<div class="info-box">
    <p><strong>Status Change Details:</strong></p>
    <p>Previous Status: <span style="display: inline-block; padding: 4px 8px; background-color: #f0f0f0; border-radius: 4px;">{{ data.old_status }}</span></p>
    <p>New Status: <span style="display: inline-block; padding: 4px 8px; background-color: {% if data.new_status == 'Pending' %}#ffd166{% elif data.new_status == 'In Progress' %}#4cc9f0{% elif data.new_status == 'Completed' %}#06d6a0{% else %}#ef476f{% endif %}; color: {% if data.new_status == 'Pending' %}#000{% else %}#fff{% endif %}; border-radius: 4px;">{{ data.new_status }}</span></p>
    <p>Updated By: {{ data.updated_by }}</p>
    <p>Update Date: {{ notification.created_at|date:"F j, Y, g:i a" }}</p>
</div>

<p>You can view the project details by clicking the button below:</p>

<div style="text-align: center;">
    <a href="{{ site_url }}{% url 'project_detail' project.id %}" class="button">View Project</a>
</div>

<div class="divider"></div>

<p>If you have any questions about this status change, please contact us at <a href="mailto:<EMAIL>" style="color: #7209b7;"><EMAIL></a>.</p>

<p>Best regards,<br>
The CompletoPLUS Team</p>
{% endblock %}
