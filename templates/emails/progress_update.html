<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Project Update</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #7209b7;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            padding: 20px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            color: #777;
        }
        .button {
            display: inline-block;
            background-color: #7209b7;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .note-content {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #7209b7;
            margin: 15px 0;
        }
        .meta {
            font-size: 14px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>New Project Update</h1>
    </div>
    <div class="content">
        <p>Hello {{ project.client.username }},</p>

        <p>There's a new update for your project <strong>{{ project.name }}</strong>.</p>

        <div class="note-content">
            {{ note.note|linebreaks }}
        </div>

        <p class="meta">
            Update added by {{ user.username }} on {{ note.created_at|date:"F j, Y" }} at {{ note.created_at|time:"g:i A" }}
        </p>

        <p>
            <a href="{{ site_url }}{% url 'project_detail' project.pk %}" class="button">View Project</a>
        </p>

        <p>Thank you for using CompletoPLUS!</p>
    </div>
    <div class="footer">
        <p>© {% now "Y" %} CompletoPLUS. All rights reserved.</p>
        <p>This email was sent to {{ project.client.email }}</p>
    </div>
</body>
</html>
