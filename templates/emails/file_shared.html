<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Shared With You</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #7209b7;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            padding: 20px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            color: #777;
        }
        .button {
            display: inline-block;
            background-color: #7209b7;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .file-details {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #7209b7;
            margin: 15px 0;
        }
        .file-icon {
            font-size: 24px;
            margin-right: 10px;
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>File Shared With You</h1>
    </div>
    <div class="content">
        <p>Hello {{ recipient.username }},</p>
        
        <p>{{ sender.username }} has shared a file with you in CompletoPLUS.</p>
        
        <div class="file-details">
            <h3>File Details:</h3>
            <p><strong>File Name:</strong> <span class="file-icon">📄</span> {{ file.file_name }}</p>
            <p><strong>File Size:</strong> {{ file.get_size_display }}</p>
            <p><strong>Uploaded On:</strong> {{ file.uploaded_at|date:"F j, Y" }}</p>
            <p><strong>Project:</strong> {{ file.project.name }}</p>
            {% if message %}
            <p><strong>Message from {{ sender.username }}:</strong> {{ message }}</p>
            {% endif %}
        </div>
        
        <p>To view or download this file, click the button below:</p>
        
        <p style="text-align: center;">
            <a href="{{ site_url }}{% url 'file_download' file.pk %}" class="button">Download File</a>
        </p>
        
        <p>You can also view the file in the project by clicking here:</p>
        
        <p style="text-align: center;">
            <a href="{{ site_url }}{% url 'project_detail' file.project.pk %}" class="button" style="background-color: #6c757d;">View Project</a>
        </p>
        
        <p>Best regards,<br>The CompletoPLUS Team</p>
    </div>
    <div class="footer">
        <p>© {% now "Y" %} CompletoPLUS. All rights reserved.</p>
        <p>This email was sent to {{ recipient.email }}</p>
    </div>
</body>
</html>
