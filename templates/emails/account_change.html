<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Changes Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #7209b7;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 5px 5px 0 0;
        }
        .content {
            padding: 20px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 5px 5px;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 12px;
            color: #777;
        }
        .button {
            display: inline-block;
            background-color: #7209b7;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .changes {
            background-color: white;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #7209b7;
            margin: 15px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Account Changes Notification</h1>
    </div>
    <div class="content">
        <p>Hello {{ user.username }},</p>
        
        <p>We're writing to inform you that changes have been made to your CompletoPLUS account.</p>
        
        <div class="changes">
            <h3>Changes Made:</h3>
            <table>
                <tr>
                    <th>Field</th>
                    <th>Previous Value</th>
                    <th>New Value</th>
                </tr>
                {% for field, values in changes.items %}
                <tr>
                    <td>{{ field }}</td>
                    <td>{{ values.old }}</td>
                    <td>{{ values.new }}</td>
                </tr>
                {% endfor %}
            </table>
            <p><strong>Changed at:</strong> {{ timestamp|date:"F j, Y" }} at {{ timestamp|time:"g:i A" }}</p>
        </div>
        
        <p>If you made these changes, no further action is required.</p>
        
        <p>If you did not make these changes, please secure your account immediately:</p>
        
        <p style="text-align: center;">
            <a href="{{ site_url }}{% url 'password_reset' %}" class="button">Reset Password</a>
        </p>
        
        <p>Best regards,<br>The CompletoPLUS Team</p>
    </div>
    <div class="footer">
        <p>© {% now "Y" %} CompletoPLUS. All rights reserved.</p>
        <p>This email was sent to {{ user.email }}</p>
    </div>
</body>
</html>
