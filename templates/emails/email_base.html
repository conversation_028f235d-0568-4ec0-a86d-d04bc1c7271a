<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{% block title %}CompletoPLUS{% endblock %}</title>
    <style type="text/css">
      .button {
        display: inline-block;
        background-color: #7209b7;
        color: #ffffff !important;
        font-weight: bold;
        padding: 12px 24px;
        border-radius: 4px;
        text-decoration: none;
        margin: 20px 0;
        text-align: center;
      }
      .button:hover {
        background-color: #6008a0;
      }
      .divider {
        margin: 30px 0;
        border-top: 1px solid #e0e0e0;
      }
      .info-box {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
      }
    </style>
  </head>
  <body
    style="
      margin: 0;
      padding: 0;
      font-family: Arial, Helvetica, sans-serif;
      font-size: 16px;
      line-height: 1.6;
      color: #333333;
      background-color: #f8f9fa;
    "
  >
    <table
      border="0"
      cellpadding="0"
      cellspacing="0"
      width="100%"
      style="border-collapse: collapse"
    >
      <tr>
        <td>
          <table
            align="center"
            border="0"
            cellpadding="0"
            cellspacing="0"
            width="600"
            style="
              border-collapse: collapse;
              background-color: #ffffff;
              border-radius: 8px;
              overflow: hidden;
              box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
              margin-top: 20px;
              margin-bottom: 20px;
            "
          >
            <!-- Header -->
            <tr>
              <td
                align="center"
                bgcolor="#4361ee"
                style="
                  padding: 30px;
                  background: linear-gradient(135deg, #4361ee, #3a0ca3);
                "
              >
                <h1
                  style="
                    color: white;
                    margin: 0;
                    font-size: 28px;
                    font-weight: bold;
                  "
                >
                  CompletoPLUS
                </h1>
              </td>
            </tr>

            <!-- Content -->
            <tr>
              <td style="padding: 40px 30px">
                {% block content %}{% endblock %}
              </td>
            </tr>

            <!-- Footer -->
            <tr>
              <td
                bgcolor="#f5f5f5"
                style="
                  padding: 30px;
                  text-align: center;
                  font-size: 14px;
                  color: #666666;
                "
              >
                <p style="margin: 0 0 15px 0">
                  © {% now "Y" %} CompletoPLUS. All rights reserved.
                </p>
                <p style="margin: 0 0 15px 0">
                  <a
                    href="{{ site_url }}/terms/"
                    style="color: #4361ee; text-decoration: none; margin: 0 5px"
                    >Terms of Service</a
                  >
                  |
                  <a
                    href="{{ site_url }}/privacy/"
                    style="color: #4361ee; text-decoration: none; margin: 0 5px"
                    >Privacy Policy</a
                  >
                  |
                  <a
                    href="{{ site_url }}/contact/"
                    style="color: #4361ee; text-decoration: none; margin: 0 5px"
                    >Contact Us</a
                  >
                </p>
                <p style="margin: 0 0 15px 0">
                  If you have any questions, please contact us at
                  <a
                    href="mailto:<EMAIL>"
                    style="color: #4361ee; text-decoration: none"
                    ><EMAIL></a
                  >
                </p>

                <table
                  border="0"
                  cellpadding="0"
                  cellspacing="0"
                  width="100%"
                  style="border-collapse: collapse"
                >
                  <tr>
                    <td style="text-align: center; padding-top: 15px">
                      <a
                        href="#"
                        style="
                          color: #4361ee;
                          text-decoration: none;
                          margin: 0 10px;
                        "
                        >Facebook</a
                      >
                      |
                      <a
                        href="#"
                        style="
                          color: #4361ee;
                          text-decoration: none;
                          margin: 0 10px;
                        "
                        >Twitter</a
                      >
                      |
                      <a
                        href="#"
                        style="
                          color: #4361ee;
                          text-decoration: none;
                          margin: 0 10px;
                        "
                        >LinkedIn</a
                      >
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>
