{% extends 'base/base.html' %}

{% block title %}Data Analysis - CompletoPLUS{% endblock %}

{% block meta_description %}View and manage your data analysis requests. Track the status of your analysis projects and view results.{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2 mb-0">
            <i class="fas fa-chart-line text-primary me-2"></i>Data Analysis
        </h1>
        <div>
            <a href="{% url 'create_analysis_request' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>New Analysis Request
            </a>
        </div>
    </div>

    <!-- Filter Form -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>Filter Analysis Requests
            </h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="{{ filter_form.status.id_for_label }}" class="form-label">Status</label>
                    {{ filter_form.status }}
                </div>
                <div class="col-md-3">
                    <label for="{{ filter_form.analysis_type.id_for_label }}" class="form-label">Analysis Type</label>
                    {{ filter_form.analysis_type }}
                </div>
                <div class="col-md-3">
                    <label for="{{ filter_form.date_from.id_for_label }}" class="form-label">From Date</label>
                    {{ filter_form.date_from }}
                </div>
                <div class="col-md-3">
                    <label for="{{ filter_form.date_to.id_for_label }}" class="form-label">To Date</label>
                    {{ filter_form.date_to }}
                </div>
                <div class="col-md-9">
                    <label for="{{ filter_form.search.id_for_label }}" class="form-label">Search</label>
                    {{ filter_form.search }}
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>Apply Filters
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Analysis Requests List -->
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Analysis Requests
                </h5>
                <span class="badge bg-primary">{{ analysis_requests.count }} Request(s)</span>
            </div>
        </div>
        <div class="card-body p-0">
            {% if analysis_requests %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Title</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Project</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for analysis in analysis_requests %}
                        <tr>
                            <td>
                                <a href="{% url 'analysis_detail' analysis.id %}" class="text-decoration-none fw-bold">
                                    {{ analysis.title }}
                                </a>
                            </td>
                            <td>
                                {% if analysis.analysis_type == 'descriptive' %}
                                <span class="badge bg-info">Descriptive</span>
                                {% elif analysis.analysis_type == 'inferential' %}
                                <span class="badge bg-primary">Inferential</span>
                                {% elif analysis.analysis_type == 'regression' %}
                                <span class="badge bg-success">Regression</span>
                                {% elif analysis.analysis_type == 'clustering' %}
                                <span class="badge bg-warning text-dark">Clustering</span>
                                {% elif analysis.analysis_type == 'time_series' %}
                                <span class="badge bg-secondary">Time Series</span>
                                {% elif analysis.analysis_type == 'text_analysis' %}
                                <span class="badge bg-dark">Text Analysis</span>
                                {% else %}
                                <span class="badge bg-light text-dark">Custom</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if analysis.status == 'pending' %}
                                <span class="badge bg-warning text-dark">Pending</span>
                                {% elif analysis.status == 'in_progress' %}
                                <span class="badge bg-info">In Progress</span>
                                {% elif analysis.status == 'completed' %}
                                <span class="badge bg-success">Completed</span>
                                {% else %}
                                <span class="badge bg-danger">Failed</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'project_detail' analysis.project.id %}" class="text-decoration-none">
                                    {{ analysis.project.name }}
                                </a>
                            </td>
                            <td>{{ analysis.created_at|date:"M d, Y" }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'analysis_detail' analysis.id %}" class="btn btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if user.is_admin_user or analysis.status == 'pending' %}
                                    <a href="{% url 'update_analysis_request' analysis.id %}" class="btn btn-outline-secondary" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <div class="mb-3">
                    <i class="fas fa-chart-bar text-muted" style="font-size: 4rem;"></i>
                </div>
                <h5 class="text-muted">No analysis requests found</h5>
                <p class="text-muted">
                    {% if filter_form.is_bound %}
                    No analysis requests match your filter criteria. Try adjusting your filters.
                    {% else %}
                    You haven't created any analysis requests yet. Click the button below to create your first one.
                    {% endif %}
                </p>
                <a href="{% url 'create_analysis_request' %}" class="btn btn-primary mt-2">
                    <i class="fas fa-plus me-2"></i>Create Analysis Request
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
