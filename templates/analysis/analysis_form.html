{% extends 'base/base.html' %}

{% block title %}{% if analysis_request %}Edit{% else %}Create{% endif %} Analysis Request - CompletoPLUS{% endblock %}

{% block meta_description %}{% if analysis_request %}Edit your existing analysis request. Update details, change analysis type, or modify input files.{% else %}Create a new data analysis request. Specify the type of analysis you need and select input files.{% endif %}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'analysis_list' %}">Data Analysis</a></li>
            {% if analysis_request %}
            <li class="breadcrumb-item"><a href="{% url 'analysis_detail' analysis_request.id %}">{{ analysis_request.title }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit</li>
            {% else %}
            <li class="breadcrumb-item active" aria-current="page">Create Analysis Request</li>
            {% endif %}
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h1 class="h4 mb-0">
                        <i class="fas fa-{% if analysis_request %}edit{% else %}plus{% endif %} me-2"></i>
                        {% if analysis_request %}
                        Edit Analysis Request
                        {% else %}
                        Create Analysis Request
                        {% endif %}
                    </h1>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                        {% endif %}
                        
                        <div class="mb-3">
                            <label for="{{ form.title.id_for_label }}" class="form-label">Title</label>
                            {{ form.title }}
                            {% if form.title.help_text %}
                            <div class="form-text">{{ form.title.help_text }}</div>
                            {% endif %}
                            {% if form.title.errors %}
                            <div class="invalid-feedback d-block">{{ form.title.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                            {{ form.description }}
                            {% if form.description.help_text %}
                            <div class="form-text">{{ form.description.help_text }}</div>
                            {% endif %}
                            {% if form.description.errors %}
                            <div class="invalid-feedback d-block">{{ form.description.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.analysis_type.id_for_label }}" class="form-label">Analysis Type</label>
                            {{ form.analysis_type }}
                            {% if form.analysis_type.help_text %}
                            <div class="form-text">{{ form.analysis_type.help_text }}</div>
                            {% endif %}
                            {% if form.analysis_type.errors %}
                            <div class="invalid-feedback d-block">{{ form.analysis_type.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-4">
                            <label for="{{ form.input_files.id_for_label }}" class="form-label">Input Files</label>
                            {{ form.input_files }}
                            {% if form.input_files.help_text %}
                            <div class="form-text">{{ form.input_files.help_text }}</div>
                            {% endif %}
                            {% if form.input_files.errors %}
                            <div class="invalid-feedback d-block">{{ form.input_files.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-{% if analysis_request %}save{% else %}plus{% endif %} me-2"></i>
                                {% if analysis_request %}
                                Save Changes
                                {% else %}
                                Create Analysis Request
                                {% endif %}
                            </button>
                            {% if analysis_request %}
                            <a href="{% url 'analysis_detail' analysis_request.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            {% else %}
                            <a href="{% url 'analysis_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            {% endif %}
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Analysis Types Information -->
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Analysis Types Explained
                    </h5>
                </div>
                <div class="card-body">
                    <div class="accordion" id="analysisTypesAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="descriptiveHeading">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#descriptiveCollapse" aria-expanded="false" aria-controls="descriptiveCollapse">
                                    Descriptive Statistics
                                </button>
                            </h2>
                            <div id="descriptiveCollapse" class="accordion-collapse collapse" aria-labelledby="descriptiveHeading" data-bs-parent="#analysisTypesAccordion">
                                <div class="accordion-body">
                                    <p>Descriptive statistics summarize and describe the main features of a dataset. This includes measures of central tendency (mean, median, mode) and measures of variability (standard deviation, variance, range).</p>
                                    <p class="mb-0"><strong>Best for:</strong> Understanding the basic characteristics of your data, data exploration, and getting a general overview of your dataset.</p>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="inferentialHeading">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#inferentialCollapse" aria-expanded="false" aria-controls="inferentialCollapse">
                                    Inferential Statistics
                                </button>
                            </h2>
                            <div id="inferentialCollapse" class="accordion-collapse collapse" aria-labelledby="inferentialHeading" data-bs-parent="#analysisTypesAccordion">
                                <div class="accordion-body">
                                    <p>Inferential statistics use sample data to make inferences about a larger population. This includes hypothesis testing, confidence intervals, and significance tests.</p>
                                    <p class="mb-0"><strong>Best for:</strong> Drawing conclusions that extend beyond the immediate data, testing hypotheses, and making predictions about a population based on a sample.</p>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="regressionHeading">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#regressionCollapse" aria-expanded="false" aria-controls="regressionCollapse">
                                    Regression Analysis
                                </button>
                            </h2>
                            <div id="regressionCollapse" class="accordion-collapse collapse" aria-labelledby="regressionHeading" data-bs-parent="#analysisTypesAccordion">
                                <div class="accordion-body">
                                    <p>Regression analysis examines the relationship between dependent and independent variables. This includes linear regression, multiple regression, and logistic regression.</p>
                                    <p class="mb-0"><strong>Best for:</strong> Understanding how variables are related, predicting outcomes based on input variables, and identifying which factors have the most influence on a particular outcome.</p>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="clusteringHeading">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#clusteringCollapse" aria-expanded="false" aria-controls="clusteringCollapse">
                                    Clustering Analysis
                                </button>
                            </h2>
                            <div id="clusteringCollapse" class="accordion-collapse collapse" aria-labelledby="clusteringHeading" data-bs-parent="#analysisTypesAccordion">
                                <div class="accordion-body">
                                    <p>Clustering analysis groups similar data points together based on their characteristics. This includes k-means clustering, hierarchical clustering, and density-based clustering.</p>
                                    <p class="mb-0"><strong>Best for:</strong> Identifying natural groupings in your data, customer segmentation, and pattern recognition.</p>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="timeSeriesHeading">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#timeSeriesCollapse" aria-expanded="false" aria-controls="timeSeriesCollapse">
                                    Time Series Analysis
                                </button>
                            </h2>
                            <div id="timeSeriesCollapse" class="accordion-collapse collapse" aria-labelledby="timeSeriesHeading" data-bs-parent="#analysisTypesAccordion">
                                <div class="accordion-body">
                                    <p>Time series analysis examines data points collected over time to identify trends, seasonal patterns, and forecasts. This includes ARIMA models, exponential smoothing, and seasonal decomposition.</p>
                                    <p class="mb-0"><strong>Best for:</strong> Analyzing data that changes over time, forecasting future values, and identifying seasonal patterns or trends.</p>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="textAnalysisHeading">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#textAnalysisCollapse" aria-expanded="false" aria-controls="textAnalysisCollapse">
                                    Text Analysis
                                </button>
                            </h2>
                            <div id="textAnalysisCollapse" class="accordion-collapse collapse" aria-labelledby="textAnalysisHeading" data-bs-parent="#analysisTypesAccordion">
                                <div class="accordion-body">
                                    <p>Text analysis processes and analyzes textual data to extract meaningful insights. This includes sentiment analysis, topic modeling, and text classification.</p>
                                    <p class="mb-0"><strong>Best for:</strong> Analyzing customer feedback, social media data, survey responses, and other text-based data sources.</p>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="customHeading">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#customCollapse" aria-expanded="false" aria-controls="customCollapse">
                                    Custom Analysis
                                </button>
                            </h2>
                            <div id="customCollapse" class="accordion-collapse collapse" aria-labelledby="customHeading" data-bs-parent="#analysisTypesAccordion">
                                <div class="accordion-body">
                                    <p>Custom analysis is tailored to your specific needs and may combine multiple analysis techniques. This is ideal when your requirements don't fit neatly into one of the standard analysis types.</p>
                                    <p class="mb-0"><strong>Best for:</strong> Complex analysis requirements, specialized industry needs, or when you need a combination of different analysis techniques.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
