{% extends 'base/base.html' %}

{% block title %}{{ analysis_request.title }} - Data Analysis - CompletoPLUS{% endblock %}

{% block meta_description %}View details and results for your data analysis request. Track progress, add notes, and download analysis results.{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'analysis_list' %}">Data Analysis</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ analysis_request.title }}</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-start mb-4">
        <div>
            <h1 class="h2 mb-1">{{ analysis_request.title }}</h1>
            <p class="text-muted mb-0">
                <span class="me-3">
                    <i class="fas fa-calendar-alt me-1"></i>
                    Created: {{ analysis_request.created_at|date:"M d, Y" }}
                </span>
                <span class="me-3">
                    <i class="fas fa-user me-1"></i>
                    Client: {{ analysis_request.client.get_full_name|default:analysis_request.client.username }}
                </span>
                <span>
                    <i class="fas fa-folder me-1"></i>
                    Project: <a href="{% url 'project_detail' analysis_request.project.id %}">{{ analysis_request.project.name }}</a>
                </span>
            </p>
        </div>
        <div class="d-flex">
            {% if user.is_admin_user or analysis_request.status == 'pending' %}
            <a href="{% url 'update_analysis_request' analysis_request.id %}" class="btn btn-outline-primary me-2">
                <i class="fas fa-edit me-2"></i>Edit
            </a>
            {% endif %}
            {% if user.is_admin_user or analysis_request.status == 'pending' %}
            <a href="{% url 'delete_analysis_request' analysis_request.id %}" class="btn btn-outline-danger">
                <i class="fas fa-trash-alt me-2"></i>Delete
            </a>
            {% endif %}
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Analysis Details -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Analysis Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="fw-bold">Description</h6>
                        <p class="mb-0">{{ analysis_request.description|linebreaks }}</p>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Analysis Type</h6>
                            <p class="mb-3">
                                {% if analysis_request.analysis_type == 'descriptive' %}
                                <span class="badge bg-info">Descriptive Statistics</span>
                                {% elif analysis_request.analysis_type == 'inferential' %}
                                <span class="badge bg-primary">Inferential Statistics</span>
                                {% elif analysis_request.analysis_type == 'regression' %}
                                <span class="badge bg-success">Regression Analysis</span>
                                {% elif analysis_request.analysis_type == 'clustering' %}
                                <span class="badge bg-warning text-dark">Clustering Analysis</span>
                                {% elif analysis_request.analysis_type == 'time_series' %}
                                <span class="badge bg-secondary">Time Series Analysis</span>
                                {% elif analysis_request.analysis_type == 'text_analysis' %}
                                <span class="badge bg-dark">Text Analysis</span>
                                {% else %}
                                <span class="badge bg-light text-dark">Custom Analysis</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Status</h6>
                            <p class="mb-3">
                                {% if analysis_request.status == 'pending' %}
                                <span class="badge bg-warning text-dark">Pending</span>
                                {% elif analysis_request.status == 'in_progress' %}
                                <span class="badge bg-info">In Progress</span>
                                {% elif analysis_request.status == 'completed' %}
                                <span class="badge bg-success">Completed</span>
                                {% else %}
                                <span class="badge bg-danger">Failed</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Assigned Analyst</h6>
                            <p class="mb-3">
                                {% if analysis_request.analyst %}
                                {{ analysis_request.analyst.get_full_name|default:analysis_request.analyst.username }}
                                {% else %}
                                <span class="text-muted">Not assigned yet</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Completed At</h6>
                            <p class="mb-3">
                                {% if analysis_request.completed_at %}
                                {{ analysis_request.completed_at|date:"M d, Y H:i" }}
                                {% else %}
                                <span class="text-muted">Not completed yet</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Input Files -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-file-upload me-2"></i>Input Files
                    </h5>
                </div>
                <div class="card-body p-0">
                    {% if analysis_request.input_files.all %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>File Name</th>
                                    <th>Uploaded By</th>
                                    <th>Uploaded On</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for file in analysis_request.input_files.all %}
                                <tr>
                                    <td>
                                        <i class="fas fa-file me-2 text-primary"></i>
                                        {{ file.file_name }}
                                    </td>
                                    <td>{{ file.owner.get_full_name|default:file.owner.username }}</td>
                                    <td>{{ file.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        <a href="{{ file.file.url }}" class="btn btn-sm btn-outline-primary" download>
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted mb-0">No input files selected for this analysis.</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Analysis Results -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Analysis Results
                    </h5>
                    {% if user.is_admin_user %}
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addResultModal">
                        <i class="fas fa-plus me-1"></i>Add Result
                    </button>
                    {% endif %}
                </div>
                <div class="card-body p-0">
                    {% if results %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Title</th>
                                    <th>Type</th>
                                    <th>Created By</th>
                                    <th>Created On</th>
                                    <th>Final</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for result in results %}
                                <tr>
                                    <td>
                                        <i class="fas fa-file-alt me-2 text-primary"></i>
                                        {{ result.title }}
                                    </td>
                                    <td>{{ result.result_type }}</td>
                                    <td>{{ result.created_by.get_full_name|default:result.created_by.username }}</td>
                                    <td>{{ result.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        {% if result.is_final %}
                                        <span class="badge bg-success">Final</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Draft</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ result.result_file.url }}" class="btn btn-sm btn-outline-primary" download>
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted mb-0">No results have been added yet.</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Notes -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-comments me-2"></i>Notes
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Add Note Form -->
                    <form method="post" action="{% url 'add_analysis_note' analysis_request.id %}" class="mb-4">
                        {% csrf_token %}
                        <div class="mb-3">
                            {{ note_form.note }}
                        </div>
                        {% if user.is_admin_user %}
                        <div class="form-check mb-3">
                            {{ note_form.is_private }}
                            <label class="form-check-label" for="{{ note_form.is_private.id_for_label }}">
                                Private note (only visible to analysts)
                            </label>
                        </div>
                        {% endif %}
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>Add Note
                        </button>
                    </form>

                    <!-- Notes List -->
                    {% if notes %}
                    <div class="notes-list">
                        {% for note in notes %}
                        <div class="note-item mb-3 p-3 border rounded {% if note.is_private %}bg-light{% endif %}">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <span class="fw-bold">{{ note.created_by.get_full_name|default:note.created_by.username }}</span>
                                    <span class="text-muted ms-2">{{ note.created_at|date:"M d, Y H:i" }}</span>
                                </div>
                                {% if note.is_private %}
                                <span class="badge bg-warning text-dark">Private</span>
                                {% endif %}
                            </div>
                            <p class="mb-0">{{ note.note|linebreaks }}</p>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-3">
                        <p class="text-muted mb-0">No notes have been added yet.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Admin Actions -->
            {% if user.is_admin_user %}
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>Admin Actions
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Update Status -->
                    <form method="post" action="{% url 'update_analysis_status' analysis_request.id %}" class="mb-3">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="status" class="form-label">Update Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="pending" {% if analysis_request.status == 'pending' %}selected{% endif %}>Pending</option>
                                <option value="in_progress" {% if analysis_request.status == 'in_progress' %}selected{% endif %}>In Progress</option>
                                <option value="completed" {% if analysis_request.status == 'completed' %}selected{% endif %}>Completed</option>
                                <option value="failed" {% if analysis_request.status == 'failed' %}selected{% endif %}>Failed</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Status
                        </button>
                    </form>

                    <!-- Assign Analyst -->
                    <form method="post" action="{% url 'assign_analyst' analysis_request.id %}">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="analyst_id" class="form-label">Assign Analyst</label>
                            <select name="analyst_id" id="analyst_id" class="form-select">
                                <option value="">-- Unassign --</option>
                                {% for user in user.objects.filter(user_type='admin') %}
                                <option value="{{ user.id }}" {% if analysis_request.analyst == user %}selected{% endif %}>
                                    {{ user.get_full_name|default:user.username }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user-check me-2"></i>Assign Analyst
                        </button>
                    </form>
                </div>
            </div>
            {% endif %}

            <!-- Parameters -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-sliders-h me-2"></i>Parameters
                    </h5>
                    {% if user.is_admin_user %}
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addParameterModal">
                        <i class="fas fa-plus me-1"></i>Add
                    </button>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if parameters %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Value</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for param in parameters %}
                                <tr>
                                    <td>
                                        <span class="fw-bold">{{ param.name }}</span>
                                        {% if param.description %}
                                        <i class="fas fa-info-circle ms-1 text-muted" data-bs-toggle="tooltip" title="{{ param.description }}"></i>
                                        {% endif %}
                                    </td>
                                    <td>{{ param.value }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-3">
                        <p class="text-muted mb-0">No parameters have been added yet.</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Timeline -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>Timeline
                    </h5>
                </div>
                <div class="card-body p-0">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">
                            <div class="d-flex">
                                <div class="timeline-icon bg-primary text-white">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div class="ms-3">
                                    <div class="fw-bold">Analysis Request Created</div>
                                    <div class="text-muted small">{{ analysis_request.created_at|date:"M d, Y H:i" }}</div>
                                </div>
                            </div>
                        </li>
                        {% if analysis_request.status != 'pending' %}
                        <li class="list-group-item">
                            <div class="d-flex">
                                <div class="timeline-icon bg-info text-white">
                                    <i class="fas fa-sync"></i>
                                </div>
                                <div class="ms-3">
                                    <div class="fw-bold">Analysis Started</div>
                                    <div class="text-muted small">{{ analysis_request.updated_at|date:"M d, Y H:i" }}</div>
                                </div>
                            </div>
                        </li>
                        {% endif %}
                        {% if analysis_request.status == 'completed' %}
                        <li class="list-group-item">
                            <div class="d-flex">
                                <div class="timeline-icon bg-success text-white">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div class="ms-3">
                                    <div class="fw-bold">Analysis Completed</div>
                                    <div class="text-muted small">{{ analysis_request.completed_at|date:"M d, Y H:i" }}</div>
                                </div>
                            </div>
                        </li>
                        {% endif %}
                        {% if analysis_request.status == 'failed' %}
                        <li class="list-group-item">
                            <div class="d-flex">
                                <div class="timeline-icon bg-danger text-white">
                                    <i class="fas fa-times"></i>
                                </div>
                                <div class="ms-3">
                                    <div class="fw-bold">Analysis Failed</div>
                                    <div class="text-muted small">{{ analysis_request.updated_at|date:"M d, Y H:i" }}</div>
                                </div>
                            </div>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Result Modal -->
{% if user.is_admin_user %}
<div class="modal fade" id="addResultModal" tabindex="-1" aria-labelledby="addResultModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'add_analysis_result' analysis_request.id %}" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="addResultModalLabel">Add Analysis Result</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="{{ result_form.title.id_for_label }}" class="form-label">Title</label>
                        {{ result_form.title }}
                    </div>
                    <div class="mb-3">
                        <label for="{{ result_form.description.id_for_label }}" class="form-label">Description</label>
                        {{ result_form.description }}
                    </div>
                    <div class="mb-3">
                        <label for="{{ result_form.result_file.id_for_label }}" class="form-label">Result File</label>
                        {{ result_form.result_file }}
                    </div>
                    <div class="mb-3">
                        <label for="{{ result_form.result_type.id_for_label }}" class="form-label">Result Type</label>
                        {{ result_form.result_type }}
                        <div class="form-text">E.g., CSV, PDF, Excel, Image, etc.</div>
                    </div>
                    <div class="form-check mb-3">
                        {{ result_form.is_final }}
                        <label class="form-check-label" for="{{ result_form.is_final.id_for_label }}">
                            This is the final result
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Result</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Parameter Modal -->
<div class="modal fade" id="addParameterModal" tabindex="-1" aria-labelledby="addParameterModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'add_analysis_parameter' analysis_request.id %}">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="addParameterModalLabel">Add Analysis Parameter</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="{{ parameter_form.name.id_for_label }}" class="form-label">Parameter Name</label>
                        {{ parameter_form.name }}
                    </div>
                    <div class="mb-3">
                        <label for="{{ parameter_form.value.id_for_label }}" class="form-label">Parameter Value</label>
                        {{ parameter_form.value }}
                    </div>
                    <div class="mb-3">
                        <label for="{{ parameter_form.description.id_for_label }}" class="form-label">Description (Optional)</label>
                        {{ parameter_form.description }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Parameter</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}

<style>
    .timeline-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
{% endblock %}
