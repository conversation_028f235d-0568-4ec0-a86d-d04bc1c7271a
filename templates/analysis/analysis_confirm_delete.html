{% extends 'base/base.html' %}

{% block title %}Delete Analysis Request - CompletoPLUS{% endblock %}

{% block meta_description %}Confirm deletion of your analysis request. This action cannot be undone.{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'analysis_list' %}">Data Analysis</a></li>
            <li class="breadcrumb-item"><a href="{% url 'analysis_detail' analysis_request.id %}">{{ analysis_request.title }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">Delete</li>
        </ol>
    </nav>

    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm border-danger">
                <div class="card-header bg-danger text-white">
                    <h1 class="h4 mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>Delete Analysis Request
                    </h1>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>Warning:</strong> This action cannot be undone.
                    </div>
                    
                    <p>Are you sure you want to delete the following analysis request?</p>
                    
                    <div class="card mb-4">
                        <div class="card-body">
                            <h5 class="card-title">{{ analysis_request.title }}</h5>
                            <p class="card-text text-muted">
                                <small>
                                    <i class="fas fa-calendar-alt me-1"></i>Created: {{ analysis_request.created_at|date:"M d, Y" }}
                                </small>
                            </p>
                            <p class="card-text">{{ analysis_request.description|truncatewords:30 }}</p>
                            <div class="d-flex">
                                <span class="badge bg-info me-2">{{ analysis_request.get_analysis_type_display }}</span>
                                <span class="badge bg-{% if analysis_request.status == 'pending' %}warning text-dark{% elif analysis_request.status == 'in_progress' %}info{% elif analysis_request.status == 'completed' %}success{% else %}danger{% endif %}">
                                    {{ analysis_request.get_status_display }}
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash-alt me-2"></i>Yes, Delete This Analysis Request
                            </button>
                            <a href="{% url 'analysis_detail' analysis_request.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>No, Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
