<!DOCTYPE html>
{% load static %}
<html lang="en" data-bs-theme="light">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{% block title %}CompletoPLUS{% endblock %}</title>
    <meta
      name="description"
      content="{% block meta_description %}CompletoPLUS - Submit your assignments, projects, theses, dissertations, data analysis, web development, and machine learning projects for expert completion.{% endblock %}"
    />
    <meta
      name="keywords"
      content="{% block meta_keywords %}file sharing, project management, document collaboration, secure file transfer, data analysis, web development, machine learning, file storage, team collaboration, project tracking, document management, data visualization, statistical analysis, research data, academic projects, business analytics, secure sharing{% endblock %}"
    />

    <meta name="author" content="CompletoPLUS" />
    <meta name="robots" content="index, follow" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="theme-color" content="#4361ee" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />

    <!-- Canonical URL -->
    <link rel="canonical" href="{{ request.build_absolute_uri }}" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="{{ request.build_absolute_uri }}" />
    <meta
      property="og:title"
      content="{% block og_title %}{{ block.super }}{% endblock %}"
    />
    <meta
      property="og:description"
      content="{% block og_description %}{{ block.super }}{% endblock %}"
    />
    <meta
      property="og:image"
      content="{% block og_image %}{{ site_url }}/static/img/completoplus-og-image.png{% endblock %}"
    />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="{{ request.build_absolute_uri }}" />
    <meta
      property="twitter:title"
      content="{% block twitter_title %}{{ block.super }}{% endblock %}"
    />
    <meta
      property="twitter:description"
      content="{% block twitter_description %}{{ block.super }}{% endblock %}"
    />
    <meta
      property="twitter:image"
      content="{% block twitter_image %}{{ site_url }}/static/img/completoplus-twitter-image.png{% endblock %}"
    />

    <!-- Structured Data - Organization -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "CompletoPLUS",
        "url": "{{ site_url }}",
        "logo": "{{ site_url }}/static/img/completoplus-logo.png",
        "sameAs": [
          "https://twitter.com/completoplus",
          "https://www.facebook.com/completoplus",
          "https://www.linkedin.com/company/completoplus"
        ],
        "contactPoint": {
          "@type": "ContactPoint",
          "telephone": "",
          "contactType": "customer service",
          "email": "<EMAIL>",
          "availableLanguage": ["English"]
        }
      }
    </script>

    <!-- Structured Data - WebSite -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "url": "{{ site_url }}",
        "name": "CompletoPLUS",
        "description": "Submit your assignments, projects, theses, dissertations, data analysis, web development, and machine learning projects for expert completion.",
        "potentialAction": {
          "@type": "SearchAction",
          "target": "{{ site_url }}/search?q={search_term_string}",
          "query-input": "required name=search_term_string"
        }
      }
    </script>

    <link
      rel="icon"
      href="/static/img/completoplus-favicon.png"
      type="image/png"
    />

    <!-- Structured Data / JSON-LD -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "CompletoPLUS",
        "url": "https://completoplus.com",
        "logo": "https://completoplus.com/static/img/logo.png",
        "description": "Submit your assignments, projects, theses, dissertations, data analysis, web development, and machine learning projects for expert completion.",
        "email": "<EMAIL>",
        "sameAs": [],
        "address": {
          "@type": "PostalAddress",
          "addressCountry": "Global"
        },
        "service": [
          {
            "@type": "Service",
            "name": "Academic Projects",
            "description": "Expert completion of assignments, projects, theses, and dissertations.",
            "url": "https://completoplus.com/services/"
          },
          {
            "@type": "Service",
            "name": "Data Analysis",
            "description": "Professional data analysis using Python for research projects, statistical analysis, and data visualization.",
            "url": "https://completoplus.com/services/data-analysis/"
          },
          {
            "@type": "Service",
            "name": "Web Development",
            "description": "Custom web application development with modern technologies and responsive design for all devices.",
            "url": "https://completoplus.com/services/web-development/"
          },
          {
            "@type": "Service",
            "name": "Machine Learning",
            "description": "AI and machine learning solutions for predictive modeling, natural language processing, and data mining.",
            "url": "https://completoplus.com/services/machine-learning/"
          }
        ]
      }
    </script>

    <!-- BreadcrumbList Schema -->
    {% block breadcrumb_schema %}
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": "https://completoplus.com/"
          }
        ]
      }
    </script>
    {% endblock %}

    <!-- Bootstrap 5 CSS with Modern Theme -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />

    <!-- Google Fonts - Poppins for modern typography -->
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />

    <!-- Animate.css for subtle animations -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"
    />

    <!-- Custom CSS -->
    <style>
      :root {
        --primary-color: #4361ee;
        --primary-dark: #3a56d4;
        --secondary-color: #7209b7;
        --success-color: #06d6a0;
        --warning-color: #ffd166;
        --danger-color: #ef476f;
        --light-color: #f8f9fa;
        --dark-color: #212529;
        --completoplus-blue: #4361ee;
        --completoplus-purple: #7209b7;
        --completoplus-cyan: #4cc9f0;
        --completoplus-pink: #f72585;
        --completoplus-indigo: #3a0ca3;
        --completoplus-gradient: linear-gradient(
          135deg,
          var(--completoplus-blue),
          var(--completoplus-purple)
        );

        /* Additional colors */
        --text-purple: #7209b7;
        --gray-100: #f8f9fa;
        --gray-200: #e9ecef;
        --gray-300: #dee2e6;
        --gray-400: #ced4da;
        --gray-500: #adb5bd;
        --gray-600: #6c757d;
        --gray-700: #495057;
        --gray-800: #343a40;
        --gray-900: #212529;
        --box-shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.05);
        --box-shadow-md: 0 5px 15px rgba(0, 0, 0, 0.08);
        --box-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
        --transition-base: all 0.3s ease;
        --border-radius-sm: 5px;
        --border-radius-md: 10px;
        --border-radius-lg: 15px;
      }

      body {
        padding-top: 60px;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        font-family: "Poppins", sans-serif;
      }

      /* Dark mode specific styles */
      [data-bs-theme="dark"] {
        --bs-body-bg: #121212;
        --bs-body-color: #e0e0e0;
      }

      [data-bs-theme="dark"] .navbar {
        background-color: #1e1e1e !important;
      }

      [data-bs-theme="dark"] .card {
        background-color: #1e1e1e;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      [data-bs-theme="dark"] .footer {
        background-color: #1e1e1e;
      }

      .content {
        flex: 1;
        padding: 1.5rem 0;
      }

      /* Modern Navbar */
      .navbar {
        box-shadow: var(--box-shadow-md);
        padding: 0.75rem 1rem;
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
      }

      .navbar-brand {
        font-weight: 600;
        letter-spacing: 0.5px;
      }

      .navbar-dark {
        background: var(--completoplus-gradient);
      }

      .nav-link {
        font-weight: 500;
        padding: 0.5rem 1rem;
        transition: var(--transition-base);
        position: relative;
      }

      .nav-link:hover {
        transform: translateY(-2px);
      }

      .nav-link::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background-color: white;
        transition: var(--transition-base);
        transform: translateX(-50%);
        opacity: 0;
      }

      .nav-link:hover::after {
        width: 30px;
        opacity: 1;
      }

      .brand-icon {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        font-size: 1rem;
      }

      .nav-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
      }

      .avatar-circle {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        font-size: 0.9rem;
      }

      .dropdown-icon {
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: rgba(var(--bs-primary-rgb), 0.1);
      }

      .dropdown-icon.text-danger {
        background-color: rgba(var(--bs-danger-rgb), 0.1);
      }

      .theme-toggle-btn {
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        padding: 0;
        background: rgba(255, 255, 255, 0.1);
      }

      .theme-toggle-btn:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      /* Notification Badge */
      .notification-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: var(--danger-color);
        color: white;
        font-size: 0.6rem;
        font-weight: 600;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0% {
          box-shadow: 0 0 0 0 rgba(239, 71, 111, 0.7);
        }
        70% {
          box-shadow: 0 0 0 5px rgba(239, 71, 111, 0);
        }
        100% {
          box-shadow: 0 0 0 0 rgba(239, 71, 111, 0);
        }
      }

      /* Modern Cards */
      .card {
        border: none;
        border-radius: var(--border-radius-md);
        margin-bottom: 24px;
        box-shadow: var(--box-shadow-md);
        transition: var(--transition-base);
        overflow: hidden;
      }

      .card:hover {
        transform: translateY(-3px);
        box-shadow: var(--box-shadow-lg);
      }

      .card-header {
        font-weight: 600;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--gray-200);
        background-color: rgba(248, 249, 250, 0.5);
      }

      .card-body {
        padding: 1.5rem;
      }

      .card-header.bg-primary {
        background: linear-gradient(
          135deg,
          var(--primary-color),
          var(--primary-dark)
        ) !important;
      }

      .card-header.bg-success {
        background: linear-gradient(
          135deg,
          var(--success-color),
          #04b589
        ) !important;
      }

      /* Status Badges */
      .status-badge {
        font-size: 0.75rem;
        padding: 0.35rem 0.75rem;
        border-radius: 50px;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
      }

      .status-badge::before {
        content: "";
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 6px;
      }

      .status-pending {
        background-color: rgba(255, 209, 102, 0.2);
        color: #e6b800;
      }

      .status-pending::before {
        background-color: #e6b800;
      }

      .status-in-progress {
        background-color: rgba(67, 97, 238, 0.2);
        color: var(--primary-color);
      }

      .status-in-progress::before {
        background-color: var(--primary-color);
      }

      .status-completed {
        background-color: rgba(6, 214, 160, 0.2);
        color: var(--success-color);
      }

      .status-completed::before {
        background-color: var(--success-color);
      }

      /* Buttons */
      .btn {
        border-radius: var(--border-radius-sm);
        padding: 0.5rem 1.25rem;
        font-weight: 500;
        transition: var(--transition-base);
        position: relative;
        overflow: hidden;
      }

      .btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--box-shadow-sm);
      }

      .btn:active {
        transform: translateY(0);
      }

      .btn-primary {
        background: var(--completoplus-gradient);
        border: none;
      }

      .btn-outline-primary:hover {
        background: var(--completoplus-gradient);
        border-color: transparent;
      }

      .btn-primary:hover {
        background-color: var(--primary-dark);
        border-color: var(--primary-dark);
      }

      .btn-success {
        background-color: var(--success-color);
        border-color: var(--success-color);
      }

      .btn-danger {
        background-color: var(--danger-color);
        border-color: var(--danger-color);
      }

      .btn-warning {
        background-color: var(--warning-color);
        border-color: var(--warning-color);
        color: var(--dark-color);
      }

      /* Form Controls */
      .form-control {
        border-radius: var(--border-radius-sm);
        padding: 0.75rem 1rem;
        border: 1px solid var(--gray-300);
        transition: var(--transition-base);
        font-size: 0.95rem;
      }

      .form-control:focus {
        border-color: var(--completoplus-blue);
        box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.15);
      }

      .form-select {
        border-radius: var(--border-radius-sm);
        padding: 0.75rem 1rem;
        border: 1px solid var(--gray-300);
        transition: var(--transition-base);
        font-size: 0.95rem;
      }

      .form-select:focus {
        border-color: var(--completoplus-blue);
        box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.15);
      }

      .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        color: var(--gray-700);
      }

      /* Progress Bar */
      .progress {
        height: 10px;
        border-radius: 5px;
        background-color: var(--gray-200);
        margin-bottom: 0.5rem;
        overflow: hidden;
      }

      .progress-bar {
        border-radius: 5px;
        background: var(--completoplus-gradient);
        transition: width 0.6s ease;
        position: relative;
        overflow: hidden;
      }

      .progress-bar::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          90deg,
          rgba(255, 255, 255, 0) 0%,
          rgba(255, 255, 255, 0.2) 50%,
          rgba(255, 255, 255, 0) 100%
        );
        animation: progress-shine 2s infinite linear;
      }

      @keyframes progress-shine {
        0% {
          transform: translateX(-100%);
        }
        100% {
          transform: translateX(100%);
        }
      }

      /* Footer */
      .footer {
        margin-top: auto;
        padding: 0;
        background: linear-gradient(
          135deg,
          var(--completoplus-indigo),
          var(--completoplus-purple)
        );
        color: var(--light-color);
      }

      .footer .hover-effect {
        transition: var(--transition-base);
      }

      .footer .hover-effect:hover {
        color: rgba(255, 255, 255, 1) !important;
        transform: translateX(5px);
      }

      .footer .icon-circle {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        transition: var(--transition-base);
      }

      .footer .contact-item:hover .icon-circle {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
      }

      /* Additional utility classes */
      .text-purple {
        color: var(--text-purple) !important;
      }
    </style>

    {% block extra_css %}{% endblock %}
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
      <div class="container">
        <a
          class="navbar-brand d-flex align-items-center"
          href="{% url 'home' %}"
        >
          <div class="brand-icon me-2">
            <i class="fas fa-check-circle"></i>
          </div>
          <span class="fw-bold">CompletoPLUS</span>
        </a>
        <button
          class="navbar-toggler border-0"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
          aria-controls="navbarNav"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto">
            {% if user.is_authenticated %}
            <li class="nav-item">
              <a
                class="nav-link d-flex align-items-center"
                href="{% url 'dashboard' %}"
              >
                <div class="nav-icon me-2">
                  <i class="fas fa-tachometer-alt"></i>
                </div>
                <span>Dashboard</span>
              </a>
            </li>
            <li class="nav-item">
              <a
                class="nav-link d-flex align-items-center"
                href="{% url 'project_list' %}"
              >
                <div class="nav-icon me-2">
                  <i class="fas fa-project-diagram"></i>
                </div>
                <span>Projects</span>
              </a>
            </li>
            <li class="nav-item">
              <a
                class="nav-link d-flex align-items-center"
                href="{% url 'my_files' %}"
              >
                <div class="nav-icon me-2">
                  <i class="fas fa-file-alt"></i>
                </div>
                <span>My Files</span>
              </a>
            </li>
            <li class="nav-item">
              <a
                class="nav-link d-flex align-items-center"
                href="{% url 'message_list' %}"
              >
                <div class="nav-icon me-2">
                  <i class="fas fa-comments"></i>
                </div>
                <span>Messages</span>
              </a>
            </li>
            {% endif %}
          </ul>
          <ul class="navbar-nav align-items-center">
            <li class="nav-item me-2">
              <button
                id="theme-toggle"
                class="btn nav-link theme-toggle-btn"
                title="Toggle dark/light mode"
              >
                <i id="theme-toggle-icon" class="fas fa-moon"></i>
              </button>
            </li>
            {% if user.is_authenticated %}
            <li class="nav-item position-relative me-3">
              <a
                class="nav-link notification-link"
                href="{% url 'notification_list' %}"
              >
                <div class="nav-icon">
                  <i class="fas fa-bell"></i>
                  {% if notifications_count > 0 %}
                  <span class="notification-badge"
                    >{{ notifications_count }}</span
                  >
                  {% endif %}
                </div>
              </a>
            </li>
            <li class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle d-flex align-items-center"
                href="#"
                id="navbarDropdown"
                role="button"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <div class="avatar-circle me-2">
                  <i class="fas fa-user"></i>
                </div>
                <span>{{ user.username }}</span>
              </a>
              <ul
                class="dropdown-menu dropdown-menu-end shadow border-0"
                aria-labelledby="navbarDropdown"
              >
                <li>
                  <a
                    class="dropdown-item d-flex align-items-center py-2"
                    href="{% url 'profile' %}"
                  >
                    <div class="dropdown-icon me-2 text-primary">
                      <i class="fas fa-user"></i>
                    </div>
                    <span>Profile</span>
                  </a>
                </li>
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a
                    class="dropdown-item d-flex align-items-center py-2"
                    href="{% url 'logout' %}"
                  >
                    <div class="dropdown-icon me-2 text-danger">
                      <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <span>Logout</span>
                  </a>
                </li>
              </ul>
            </li>
            {% else %}
            <li class="nav-item me-2">
              <a class="nav-link" href="{% url 'login' %}">
                <i class="fas fa-sign-in-alt me-1"></i> Login
              </a>
            </li>
            <li class="nav-item">
              <a
                class="btn btn-primary btn-sm px-3 py-2"
                href="{% url 'register' %}"
              >
                <i class="fas fa-user-plus me-1"></i> Register
              </a>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="content">
      <!-- Messages -->
      {% if messages %}
      <div class="container mt-4">
        {% for message in messages %}
        <div
          class="alert alert-{{ message.tags }} alert-dismissible fade show animate__animated animate__fadeIn shadow-sm"
          role="alert"
        >
          <div class="d-flex align-items-center">
            {% if message.tags == 'success' %}
            <i class="fas fa-check-circle me-2 fs-4"></i>
            {% elif message.tags == 'error' or message.tags == 'danger' %}
            <i class="fas fa-exclamation-circle me-2 fs-4"></i>
            {% elif message.tags == 'warning' %}
            <i class="fas fa-exclamation-triangle me-2 fs-4"></i>
            {% elif message.tags == 'info' %}
            <i class="fas fa-info-circle me-2 fs-4"></i>
            {% endif %}
            <div>{{ message }}</div>
          </div>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="alert"
            aria-label="Close"
          ></button>
        </div>
        {% endfor %}
      </div>
      {% endif %}

      <!-- Page Content -->
      {% block content %}{% endblock %}
    </div>

    {% include 'base/footer.html' %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JSZip for client-side zip file creation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>

    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Dark Mode JS -->
    <script src="/static/js/dark-mode.js"></script>

    <!-- Notifications functionality removed -->

    <!-- Enhanced file upload functionality -->
    <script src="/static/js/drag-drop-upload.js"></script>
    <script src="/static/js/file-upload-toggle.js"></script>

    <!-- Dashboard settings -->
    <script src="/static/js/dashboard-settings.js"></script>

    <!-- Custom JavaScript -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(
          document.querySelectorAll('[data-bs-toggle="tooltip"]')
        );
        tooltipTriggerList.map(function (tooltipTriggerEl) {
          return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Auto-dismiss alerts after 5 seconds
        setTimeout(function () {
          const alerts = document.querySelectorAll(".alert");
          alerts.forEach(function (alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
          });
        }, 5000);

        // Add active class to current nav item
        const currentLocation = window.location.pathname;
        const navLinks = document.querySelectorAll(".navbar-nav .nav-link");
        navLinks.forEach((link) => {
          if (link.getAttribute("href") === currentLocation) {
            link.classList.add("active");
            link.setAttribute("aria-current", "page");
          }
        });

        // Card hover effect
        const cards = document.querySelectorAll(".card");
        cards.forEach((card) => {
          card.addEventListener("mouseenter", function () {
            this.classList.add("shadow-lg");
          });
          card.addEventListener("mouseleave", function () {
            this.classList.remove("shadow-lg");
          });
        });
      });
    </script>

    {% block extra_js %}{% endblock %}
  </body>
</html>
