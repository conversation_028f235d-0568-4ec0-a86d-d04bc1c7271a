# HTTPS Enforcement for CompletoPLUS

This document explains the comprehensive HTTPS enforcement implementation for the CompletoPLUS production environment.

## Overview

CompletoPLUS implements strict HTTPS enforcement to ensure all user traffic is encrypted and secure. The implementation provides two modes of operation and comprehensive security headers.

## HTTPS Enforcement Modes

### 1. REDIRECT Mode (Default - User Friendly)
- Automatically redirects HTTP requests to HTTPS
- Uses 301 Permanent Redirect for SEO benefits
- Provides seamless user experience
- Recommended for production

### 2. BLOCK Mode (Strict Security)
- Blocks HTTP requests with 403 Forbidden
- Shows a styled error page with HTTPS link
- Maximum security enforcement
- Use when strict security is required

## Configuration

### Settings Configuration (`fileshare/settings.py`)

```python
# HTTPS Enforcement Mode
HTTPS_ENFORCEMENT_MODE = 'REDIRECT'  # or 'BLOCK'

# Core HTTPS settings
SECURE_SSL_REDIRECT = not DEBUG
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

# HSTS (HTTP Strict Transport Security)
SECURE_HSTS_SECONDS = 31536000  # 1 year
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Secure cookies
SESSION_COOKIE_SECURE = not DEBUG
CSRF_COOKIE_SECURE = not DEBUG
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_HTTPONLY = True
```

### Middleware Order (`fileshare/settings.py`)

```python
MIDDLEWARE = [
    'fileshare.middleware.StrictHTTPSMiddleware',  # Must be first
    'fileshare.middleware.DomainCanonicalizeMiddleware',
    'django.middleware.security.SecurityMiddleware',
    # ... other middleware
    'fileshare.middleware.SecurityHeadersMiddleware',
]
```

## Security Headers

The implementation adds comprehensive security headers:

- **HSTS**: Forces HTTPS for 1 year with subdomain inclusion
- **CSP**: Content Security Policy to prevent XSS attacks
- **X-Frame-Options**: Prevents clickjacking (DENY)
- **X-Content-Type-Options**: Prevents MIME sniffing
- **Referrer-Policy**: Controls referrer information
- **Permissions-Policy**: Restricts browser features

## Testing HTTPS Enforcement

### Using the Management Command

```bash
# Test current HTTPS enforcement
python manage.py test_https

# Test specific URL
python manage.py test_https --url http://completoplus.com

# Get help on enforcement modes
python manage.py test_https --mode redirect
python manage.py test_https --mode block
```

### Manual Testing

1. **HTTP to HTTPS Redirect Test**:
   ```bash
   curl -I http://completoplus.com
   curl -I http://www.completoplus.com
   ```
   Should return 301/302 redirect to HTTPS

2. **HTTPS Access Test**:
   ```bash
   curl -I https://completoplus.com
   curl -I https://www.completoplus.com
   ```
   Should return 200 OK with security headers

3. **Security Headers Test**:
   ```bash
   curl -I https://completoplus.com | grep -E "(Strict-Transport|X-Frame|X-Content)"
   ```

## Railway.com Deployment

### Environment Variables
No additional environment variables needed. The configuration uses:
- `HTTP_X_FORWARDED_PROTO` header (automatically set by Railway)
- `DEBUG=False` for production (set in Railway environment)

### Domain Configuration
Ensure both domains are configured in Railway:
- `completoplus.com`
- `www.completoplus.com`

## Troubleshooting

### Common Issues

1. **Redirect Loops**:
   - Check `SECURE_PROXY_SSL_HEADER` setting
   - Verify Railway proxy headers

2. **Mixed Content Warnings**:
   - Ensure all resources use HTTPS URLs
   - Check CSP settings

3. **Cookie Issues**:
   - Verify secure cookie settings
   - Check domain configuration

### Debug Commands

```bash
# Check current settings
python manage.py shell -c "from django.conf import settings; print(f'DEBUG: {settings.DEBUG}'); print(f'HTTPS_MODE: {settings.HTTPS_ENFORCEMENT_MODE}')"

# Test HTTPS enforcement
python manage.py test_https

# Check security headers
curl -I https://completoplus.com
```

## Security Benefits

1. **Data Encryption**: All traffic encrypted in transit
2. **Authentication**: Prevents man-in-the-middle attacks
3. **Integrity**: Ensures data hasn't been tampered with
4. **SEO Benefits**: Google prefers HTTPS sites
5. **User Trust**: Browser security indicators
6. **Compliance**: Meets modern security standards

## Switching Enforcement Modes

To change from REDIRECT to BLOCK mode:

1. Edit `fileshare/settings.py`
2. Change `HTTPS_ENFORCEMENT_MODE = 'BLOCK'`
3. Deploy to Railway
4. Test with `python manage.py test_https`

## Production Checklist

- [ ] HTTPS_ENFORCEMENT_MODE configured
- [ ] DEBUG=False in production
- [ ] Both domains (apex and www) configured
- [ ] SSL certificate valid
- [ ] Security headers present
- [ ] HSTS working
- [ ] No mixed content warnings
- [ ] Cookies secure
- [ ] CSP policy appropriate

## Support

For issues with HTTPS enforcement:
1. Check the troubleshooting section
2. Use the test command: `python manage.py test_https`
3. Verify Railway.com SSL configuration
4. Check browser developer tools for security warnings
